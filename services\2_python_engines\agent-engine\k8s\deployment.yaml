apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-engine-deployment
  labels:
    app: agent-engine
spec:
  replicas: 1
  selector:
    matchLabels:
      app: agent-engine
  template:
    metadata:
      labels:
        app: agent-engine
    spec:
      containers:
      - name: agent-engine
        image: athena/agent-engine:0.0.13
        imagePullPolicy: IfNotPresent
        env:
        - name: RABBITMQ_HOST
          value: rabbitmq-svc # RabbitMQ 服务名称
        - name: MLFLOW_TRACKING_URI
          value: http://mlflow-svc:5000 # MLflow 服务地址
        - name: MAX_DECISION_CYCLES
          value: "5" # 最大决策循环次数
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m" 