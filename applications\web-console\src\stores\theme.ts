import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export type ThemeMode = 'light' | 'dark' | 'auto'
export type ColorScheme = 'blue' | 'green' | 'purple' | 'orange' | 'red'

export const useThemeStore = defineStore('theme', () => {
  // 状态
  const mode = ref<ThemeMode>((localStorage.getItem('theme-mode') as ThemeMode) || 'auto')
  const colorScheme = ref<ColorScheme>((localStorage.getItem('color-scheme') as ColorScheme) || 'blue')
  const fontSize = ref<number>(parseInt(localStorage.getItem('font-size') || '14'))
  const compactMode = ref<boolean>(localStorage.getItem('compact-mode') === 'true')
  const sidebarCollapsed = ref<boolean>(localStorage.getItem('sidebar-collapsed') === 'true')
  const scale = ref<number>(parseInt(localStorage.getItem('theme-scale') || '100'))
  const animations = ref<boolean>(localStorage.getItem('theme-animations') !== 'false')

  // 计算属性
  const isDark = computed(() => {
    if (mode.value === 'dark') return true
    if (mode.value === 'light') return false
    // auto mode - 检测系统主题
    return window.matchMedia('(prefers-color-scheme: dark)').matches
  })

  const currentTheme = computed(() => ({
    mode: mode.value,
    colorScheme: colorScheme.value,
    fontSize: fontSize.value,
    compactMode: compactMode.value,
    isDark: isDark.value
  }))

  // 颜色方案配置
  const colorSchemes = {
    blue: {
      primary: '#3b82f6',
      light: '#60a5fa',
      primaryLight: '#60a5fa',
      primaryDark: '#1d4ed8'
    },
    green: {
      primary: '#10b981',
      light: '#34d399',
      primaryLight: '#34d399',
      primaryDark: '#047857'
    },
    purple: {
      primary: '#8b5cf6',
      light: '#a78bfa',
      primaryLight: '#a78bfa',
      primaryDark: '#7c3aed'
    },
    orange: {
      primary: '#f59e0b',
      light: '#fbbf24',
      primaryLight: '#fbbf24',
      primaryDark: '#d97706'
    },
    red: {
      primary: '#ef4444',
      light: '#f87171',
      primaryLight: '#f87171',
      primaryDark: '#dc2626'
    }
  }

  /**
   * 设置主题模式
   */
  const setMode = (newMode: ThemeMode) => {
    mode.value = newMode
    localStorage.setItem('theme-mode', newMode)
    applyTheme()
  }

  /**
   * 设置颜色方案
   */
  const setColorScheme = (scheme: ColorScheme) => {
    colorScheme.value = scheme
    localStorage.setItem('color-scheme', scheme)
    applyTheme()
  }

  /**
   * 设置字体大小
   */
  const setFontSize = (size: number) => {
    fontSize.value = size
    localStorage.setItem('font-size', size.toString())
    applyTheme()
  }

  /**
   * 切换紧凑模式
   */
  const toggleCompactMode = () => {
    compactMode.value = !compactMode.value
    localStorage.setItem('compact-mode', compactMode.value.toString())
    applyTheme()
  }

  /**
   * 设置界面缩放
   */
  const setScale = (newScale: number) => {
    scale.value = newScale
    localStorage.setItem('theme-scale', newScale.toString())
    applyTheme()
  }

  /**
   * 设置动画效果
   */
  const setAnimations = (enabled: boolean) => {
    animations.value = enabled
    localStorage.setItem('theme-animations', enabled.toString())
    applyTheme()
  }

  /**
   * 切换侧边栏状态
   */
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    localStorage.setItem('sidebar-collapsed', sidebarCollapsed.value.toString())
  }

  /**
   * 应用主题到DOM
   */
  const applyTheme = () => {
    const root = document.documentElement
    const colors = colorSchemes[colorScheme.value]

    // 设置颜色变量
    root.style.setProperty('--primary-color', colors.primary)
    root.style.setProperty('--primary-light', colors.primaryLight || colors.light)
    root.style.setProperty('--primary-dark', colors.primaryDark)

    // 设置字体大小
    root.style.setProperty('--base-font-size', `${fontSize.value}px`)

    // 设置界面缩放
    root.style.setProperty('--scale-factor', `${scale.value / 100}`)
    root.style.setProperty('--zoom-level', `${scale.value}%`)

    // 设置动画效果
    if (animations.value) {
      root.classList.add('animations-enabled')
      root.classList.remove('animations-disabled')
    } else {
      root.classList.add('animations-disabled')
      root.classList.remove('animations-enabled')
    }

    // 设置紧凑模式
    if (compactMode.value) {
      root.classList.add('compact-mode')
    } else {
      root.classList.remove('compact-mode')
    }

    // 设置暗色模式
    if (isDark.value) {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
  }

  /**
   * 重置主题设置
   */
  const resetTheme = () => {
    setMode('auto')
    setColorScheme('blue')
    setFontSize(14)
    compactMode.value = false
    localStorage.setItem('compact-mode', 'false')
    applyTheme()
  }

  /**
   * 初始化主题
   */
  const initTheme = () => {
    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', () => {
      if (mode.value === 'auto') {
        applyTheme()
      }
    })

    // 应用初始主题
    applyTheme()
  }

  // 自动初始化
  if (typeof window !== 'undefined') {
    initTheme()
  }

  return {
    // 状态
    mode,
    colorScheme,
    fontSize,
    compactMode,
    sidebarCollapsed,
    scale,
    animations,

    // 计算属性
    isDark,
    currentTheme,
    colorSchemes,

    // 方法
    setMode,
    setColorScheme,
    setFontSize,
    setScale,
    setAnimations,
    toggleCompactMode,
    toggleSidebar,
    applyTheme,
    resetTheme,
    initTheme
  }
})
