<template>
  <div class="knowledge-container">
    <!-- 顶部搜索和操作栏 -->
    <el-card class="filter-card fade-in">
      <div class="filter-form">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="知识标题">
            <el-input v-model="searchForm.title" placeholder="请输入知识标题" clearable />
          </el-form-item>
          <el-form-item label="知识分类">
            <el-select v-model="searchForm.category" placeholder="选择分类" clearable>
              <el-option label="技术文档" value="tech" />
              <el-option label="业务流程" value="business" />
              <el-option label="政策解读" value="policy" />
              <el-option label="操作指南" value="guide" />
              <el-option label="常见问题" value="faq" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
              <el-option label="已发布" value="published" />
              <el-option label="草稿" value="draft" />
              <el-option label="审核中" value="reviewing" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 知识库统计 -->
    <div class="knowledge-stats slide-in-right">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6" v-for="(stat, index) in knowledgeStats" :key="index">
          <el-card class="stat-card hover-lift" :body-style="{ padding: '20px' }">
            <div class="stat-content">
              <div class="stat-icon" :class="stat.iconClass">
                <el-icon><component :is="stat.icon" /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stat.count }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 知识库列表 -->
    <el-card class="list-card fade-in">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <el-icon class="header-icon"><Reading /></el-icon>
            <span>知识库</span>
            <el-tag type="info" class="count-tag">{{ knowledgeData.length }} 篇</el-tag>
          </div>
          <div class="header-actions">
            <el-button type="success" :icon="Upload" plain @click="handleImport">批量导入</el-button>
            <el-button type="info" :icon="Download" plain @click="handleExport">导出数据</el-button>
            <el-button type="primary" :icon="Plus" @click="handleCreate">新建知识</el-button>
          </div>
        </div>
      </template>
      
      <!-- 知识库表格 -->
      <el-table
        :data="knowledgeData"
        border
        stripe
        style="width: 100%"
        v-loading="loading"
        :header-cell-style="{ background: '#f5f7fa' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="id" label="ID" width="80" sortable />
        <el-table-column prop="title" label="知识标题" min-width="200">
          <template #default="{ row }">
            <div class="knowledge-title-cell">
              <span class="knowledge-title" :title="row.title">{{ row.title }}</span>
              <el-tag v-if="row.isHot" size="small" type="danger" effect="dark">热门</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="分类" width="120">
          <template #default="{ row }">
            <el-tag :type="getCategoryColor(row.category)" effect="plain">
              {{ getCategoryText(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="author" label="作者" width="100" />
        <el-table-column prop="viewCount" label="浏览量" width="100" align="center" sortable>
          <template #default="{ row }">
            <span class="view-count">{{ formatNumber(row.viewCount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="likeCount" label="点赞数" width="100" align="center" sortable>
          <template #default="{ row }">
            <span class="like-count">{{ row.likeCount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              :effect="row.status === 'published' ? 'dark' : 'light'"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" sortable width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-btns">
              <el-button 
                type="primary" 
                size="small" 
                text 
                :icon="View" 
                @click="handleView(row)"
              >
                查看
              </el-button>
              <el-button 
                type="primary" 
                size="small" 
                text 
                :icon="Edit" 
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-dropdown>
                <el-button size="small" text>
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleCopy(row)">
                      <el-icon><CopyDocument /></el-icon> 复制
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleShare(row)">
                      <el-icon><Share /></el-icon> 分享
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleDownload(row)">
                      <el-icon><Download /></el-icon> 下载
                    </el-dropdown-item>
                    <el-dropdown-item divided @click="handleDelete(row)" style="color: #F56C6C;">
                      <el-icon><Delete /></el-icon> 删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Search, 
  Refresh, 
  Edit, 
  Delete, 
  View, 
  Download, 
  Upload,
  Reading,
  ArrowDown,
  CopyDocument,
  Share
} from '@element-plus/icons-vue'

// 表格加载状态
const loading = ref(false)

// 搜索表单
const searchForm = reactive({
  title: '',
  category: '',
  status: ''
})

// 统计数据
const knowledgeStats = [
  { label: '全部知识', count: 328, icon: 'Reading', iconClass: 'all-icon' },
  { label: '已发布', count: 256, icon: 'CircleCheck', iconClass: 'published-icon' },
  { label: '草稿', count: 45, icon: 'EditPen', iconClass: 'draft-icon' },
  { label: '本月新增', count: 23, icon: 'TrendCharts', iconClass: 'new-icon' }
]

// 知识库数据
const knowledgeData = ref([
  {
    id: 'KB-001',
    title: 'Athena系统用户操作指南',
    category: 'guide',
    author: '系统管理员',
    viewCount: 1256,
    likeCount: 89,
    status: 'published',
    updateTime: '2025-01-17 10:30:00',
    isHot: true,
    summary: '详细介绍Athena系统的各项功能使用方法...'
  },
  {
    id: 'KB-002',
    title: '物理信息神经网络(PINN)技术原理',
    category: 'tech',
    author: '技术专家',
    viewCount: 892,
    likeCount: 156,
    status: 'published',
    updateTime: '2025-01-16 15:20:00',
    isHot: true,
    summary: '深入解析PINN技术的理论基础和实现方法...'
  },
  {
    id: 'KB-003',
    title: '城市政策分析业务流程',
    category: 'business',
    author: '业务专家',
    viewCount: 567,
    likeCount: 43,
    status: 'published',
    updateTime: '2025-01-15 09:45:00',
    isHot: false,
    summary: '介绍城市政策分析的标准业务流程...'
  },
  {
    id: 'KB-004',
    title: '系统常见问题解答',
    category: 'faq',
    author: '客服团队',
    viewCount: 2341,
    likeCount: 234,
    status: 'published',
    updateTime: '2025-01-14 16:30:00',
    isHot: true,
    summary: '收集整理用户使用过程中的常见问题...'
  },
  {
    id: 'KB-005',
    title: '新版本功能介绍',
    category: 'guide',
    author: '产品经理',
    viewCount: 0,
    likeCount: 0,
    status: 'draft',
    updateTime: '2025-01-17 14:20:00',
    isHot: false,
    summary: '介绍系统新版本的功能特性...'
  }
])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(5)

// 选中行
const selectedRows = ref([])

// 页面加载时
onMounted(() => {
  fetchKnowledgeData()
})

// 获取知识库数据
const fetchKnowledgeData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 500)
}

// 分类文本映射
const getCategoryText = (category: string) => {
  const categoryMap: Record<string, string> = {
    'tech': '技术文档',
    'business': '业务流程',
    'policy': '政策解读',
    'guide': '操作指南',
    'faq': '常见问题'
  }
  return categoryMap[category] || category
}

// 分类颜色映射
const getCategoryColor = (category: string) => {
  const colorMap: Record<string, string> = {
    'tech': 'primary',
    'business': 'success',
    'policy': 'warning',
    'guide': 'info',
    'faq': 'danger'
  }
  return colorMap[category] || ''
}

// 状态文本映射
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'published': '已发布',
    'draft': '草稿',
    'reviewing': '审核中'
  }
  return statusMap[status] || status
}

// 状态类型映射
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'published': 'success',
    'draft': 'info',
    'reviewing': 'warning'
  }
  return typeMap[status] || ''
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchKnowledgeData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.title = ''
  searchForm.category = ''
  searchForm.status = ''
  handleSearch()
}

// 表格选择行变化
const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchKnowledgeData()
}

// 页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchKnowledgeData()
}

// 新建知识
const handleCreate = () => {
  ElMessage.info('新建知识功能开发中...')
}

// 查看知识
const handleView = (row: any) => {
  ElMessage.info(`查看知识：${row.title}`)
}

// 编辑知识
const handleEdit = (row: any) => {
  ElMessage.info(`编辑知识：${row.title}`)
}

// 复制知识
const handleCopy = (row: any) => {
  ElMessage.info(`复制知识：${row.title}`)
}

// 分享知识
const handleShare = (row: any) => {
  ElMessage.info(`分享知识：${row.title}`)
}

// 下载知识
const handleDownload = (row: any) => {
  ElMessage.info(`下载知识：${row.title}`)
}

// 删除知识
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确认删除知识：${row.title}？此操作不可恢复！`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'danger'
  }).then(() => {
    const index = knowledgeData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      knowledgeData.value.splice(index, 1)
      total.value--
      ElMessage.success('删除成功')
    }
  }).catch(() => {})
}

// 导入知识
const handleImport = () => {
  ElMessage.info('批量导入功能开发中...')
}

// 导出数据
const handleExport = () => {
  ElMessage.info('导出数据功能开发中...')
}
</script>

<style scoped>
.knowledge-container {
  width: 100%;
}

.filter-card {
  margin-bottom: 16px;
  background-color: var(--bg-primary);
}

.knowledge-stats {
  margin-bottom: 16px;
}

.stat-card {
  margin-bottom: 16px;
  transition: all 0.3s ease;
  background: var(--bg-primary);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.all-icon {
  background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
}

.published-icon {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
}

.draft-icon {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

.new-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.list-card {
  margin-bottom: 16px;
  background-color: var(--bg-primary);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: var(--text-primary);
}

.header-icon {
  margin-right: 8px;
  color: var(--primary-color);
}

.count-tag {
  margin-left: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.knowledge-title-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.knowledge-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  color: var(--text-primary);
}

.view-count {
  color: var(--primary-color);
  font-weight: 500;
}

.like-count {
  color: var(--danger-color);
  font-weight: 500;
}

.action-btns {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  gap: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .search-form {
    flex-wrap: wrap;
  }

  .el-form--inline .el-form-item {
    margin-right: 10px;
  }

  .header-actions {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .action-btns {
    flex-wrap: wrap;
    justify-content: flex-end;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }
}

@media (max-width: 576px) {
  .header-actions .el-button {
    flex: 1;
  }

  .pagination-container {
    justify-content: center;
  }
}
</style>
