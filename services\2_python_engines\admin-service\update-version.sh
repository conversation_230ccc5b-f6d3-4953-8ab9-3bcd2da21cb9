#!/bin/bash

# 版本更新脚本

set -e

if [ $# -eq 0 ]; then
    echo "❌ 请提供版本号"
    echo "用法: $0 <version>"
    echo "示例: $0 0.0.2"
    exit 1
fi

NEW_VERSION=$1
IMAGE_NAME="athena/admin-service"

echo "🔄 更新 admin-service 版本到 v${NEW_VERSION}..."

# 更新 build.sh 中的版本号
echo "📝 更新 build.sh..."
sed -i.bak "s/VERSION=\".*\"/VERSION=\"${NEW_VERSION}\"/" build.sh

# 更新 deployment.yaml 中的镜像标签
echo "📝 更新 deployment.yaml..."
sed -i.bak "s|image: ${IMAGE_NAME}:.*|image: ${IMAGE_NAME}:${NEW_VERSION}|" k8s/deployment.yaml

# 清理备份文件
rm -f build.sh.bak k8s/deployment.yaml.bak

echo "✅ 版本更新完成！"
echo ""
echo "📋 更新内容:"
echo "- build.sh: VERSION=\"${NEW_VERSION}\""
echo "- k8s/deployment.yaml: image: ${IMAGE_NAME}:${NEW_VERSION}"
echo ""
echo "🚀 运行 ./build.sh 来构建和部署新版本"
