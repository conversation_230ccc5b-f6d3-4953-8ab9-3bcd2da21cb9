apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-service
  namespace: default
  labels:
    app: admin-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: admin-service
  template:
    metadata:
      labels:
        app: admin-service
    spec:
      serviceAccountName: admin-service-sa
      containers:
      - name: admin-service
        image: athena/admin-service:0.0.1
        imagePullPolicy: Never
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: PORT
          value: "8000"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: admin-service-svc
  namespace: default
  labels:
    app: admin-service
spec:
  selector:
    app: admin-service
  ports:
  - name: http
    port: 80
    targetPort: 8000
    protocol: TCP
  type: ClusterIP
