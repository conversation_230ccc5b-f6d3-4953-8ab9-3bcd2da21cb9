<template>
  <div class="rag-knowledge-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon class="title-icon"><Reading /></el-icon>
        RAG 知识库管理
      </h1>
      <p class="page-description">管理ChromaDB向量数据库中的文档和知识内容</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6" v-for="stat in knowledgeStats" :key="stat.label">
        <el-card class="stat-card hover-lift">
          <div class="stat-content">
            <div class="stat-icon" :class="stat.iconClass">
              <el-icon size="24">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stat.count }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-upload
            ref="uploadRef"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :show-file-list="false"
            accept=".txt,.md,.pdf,.doc,.docx"
          >
            <el-button type="primary" :icon="Upload" :loading="uploading">
              上传文档
            </el-button>
          </el-upload>
          <el-button :icon="Refresh" @click="refreshData">刷新</el-button>
          <el-button :icon="Search" @click="showSearchDialog">RAG查询测试</el-button>
        </div>
        <div class="toolbar-right">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索文档..."
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
            style="width: 300px"
          />
        </div>
      </div>
    </el-card>

    <!-- 文档列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">文档列表</span>
          <span class="card-subtitle">共 {{ total }} 个文档</span>
        </div>
      </template>

      <el-table
        :data="filteredDocuments"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="filename" label="文件名" min-width="200">
          <template #default="{ row }">
            <div class="filename-cell">
              <el-icon class="file-icon"><Document /></el-icon>
              <span>{{ row.filename }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="chunk_count" label="文本块数" width="100">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.chunk_count || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="上传时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="metadata" label="元数据" min-width="200">
          <template #default="{ row }">
            <el-popover placement="top" width="300" trigger="hover">
              <template #reference>
                <el-button type="text" size="small">查看详情</el-button>
              </template>
              <div class="metadata-content">
                <div v-for="(value, key) in row.metadata" :key="key" class="metadata-item">
                  <strong>{{ key }}:</strong> {{ value }}
                </div>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" :icon="View" @click="previewDocument(row)">
              预览
            </el-button>
            <el-button type="success" size="small" :icon="Search" @click="testRAGQuery(row)">
              测试
            </el-button>
            <el-button type="danger" size="small" :icon="Delete" @click="deleteDocument(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- RAG查询测试对话框 -->
    <el-dialog
      v-model="searchDialogVisible"
      title="RAG查询测试"
      width="800px"
    >
      <div class="search-container">
        <el-form :model="searchForm" label-width="100px">
          <el-form-item label="查询问题">
            <el-input
              v-model="searchForm.query"
              type="textarea"
              :rows="3"
              placeholder="请输入要查询的问题..."
            />
          </el-form-item>
          <el-form-item label="返回结果数">
            <el-input-number
              v-model="searchForm.top_k"
              :min="1"
              :max="10"
              :step="1"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="performRAGSearch" :loading="searching">
              执行查询
            </el-button>
            <el-button @click="clearSearchResults">清空结果</el-button>
          </el-form-item>
        </el-form>

        <!-- 查询结果 -->
        <div v-if="searchResults.length > 0" class="search-results">
          <h4>查询结果：</h4>
          <div v-for="(result, index) in searchResults" :key="index" class="result-item">
            <div class="result-header">
              <span class="result-index">结果 {{ index + 1 }}</span>
              <el-tag size="small" type="info">相似度: {{ (1 - result.distance).toFixed(3) }}</el-tag>
            </div>
            <div class="result-content">{{ result.document }}</div>
            <div class="result-metadata" v-if="result.metadata">
              <small>来源: {{ result.metadata.source || '未知' }}</small>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 文档预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="文档预览"
      width="70%"
      top="5vh"
    >
      <div class="preview-container">
        <div class="preview-header">
          <h4>{{ currentDocument?.filename }}</h4>
          <div class="preview-meta">
            <span>上传时间: {{ formatDate(currentDocument?.created_at) }}</span>
            <span>文本块数: {{ currentDocument?.chunk_count || 0 }}</span>
          </div>
        </div>
        <div class="preview-content">
          <el-scrollbar height="400px">
            <pre>{{ currentDocument?.content || '暂无内容预览' }}</pre>
          </el-scrollbar>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Upload,
  Search,
  Refresh,
  View,
  Delete,
  Document,
  Reading,
  Files,
  DataBoard,
  Connection
} from '@element-plus/icons-vue'
import { ragKnowledgeApi, type Document as DocumentType, type SearchRequest, type UploadResponse } from '@/api/knowledge'
import { useAuthStore } from '@/stores/auth'

// 响应式数据
const loading = ref(false)
const uploading = ref(false)
const searching = ref(false)
const searchDialogVisible = ref(false)
const previewDialogVisible = ref(false)

// 数据
const documents = ref<DocumentType[]>([])
const selectedRows = ref<DocumentType[]>([])
const searchKeyword = ref('')
const currentDocument = ref<DocumentType | null>(null)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 统计数据
const knowledgeStats = ref([
  { label: '文档总数', count: 0, icon: 'Document', iconClass: 'document-icon' },
  { label: '文本块数', count: 0, icon: 'Files', iconClass: 'chunk-icon' },
  { label: '向量维度', count: 384, icon: 'DataBoard', iconClass: 'vector-icon' },
  { label: '索引大小', count: 0, icon: 'Connection', iconClass: 'index-icon' }
])

// 搜索表单
const searchForm = reactive<SearchRequest>({
  query: '',
  top_k: 3
})

// 搜索结果
const searchResults = ref<Array<{
  document: string
  distance: number
  metadata: Record<string, any>
}>>([])

// 认证store
const authStore = useAuthStore()

// 上传配置
const uploadUrl = '/api/rag/upload'
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${authStore.token}`
}))

// 计算属性
const filteredDocuments = computed(() => {
  if (!searchKeyword.value) {
    return documents.value
  }
  return documents.value.filter(doc =>
    doc.filename.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 页面加载时初始化数据
onMounted(() => {
  loadDocuments()
  loadKnowledgeStats()
})

// 加载文档列表
const loadDocuments = async () => {
  loading.value = true
  try {
    const data = await ragKnowledgeApi.getDocuments()
    documents.value = data
    total.value = data.length
    updateStats()
  } catch (error) {
    console.error('加载文档列表失败:', error)
    ElMessage.error('加载文档列表失败')
    // 使用模拟数据
    documents.value = [
      {
        id: '1',
        filename: 'policy_document_1.txt',
        content: '这是一个政策文档的示例内容...',
        metadata: { source: 'policy_document_1.txt', type: 'policy' },
        created_at: '2025-01-17T10:30:00Z',
        chunk_count: 15
      },
      {
        id: '2',
        filename: 'technical_guide.md',
        content: '技术指南的示例内容...',
        metadata: { source: 'technical_guide.md', type: 'guide' },
        created_at: '2025-01-16T15:20:00Z',
        chunk_count: 23
      }
    ]
    total.value = documents.value.length
    updateStats()
  } finally {
    loading.value = false
  }
}

// 加载知识库统计数据
const loadKnowledgeStats = async () => {
  try {
    const stats = await ragKnowledgeApi.getKnowledgeStats()
    // 更新统计数据
    knowledgeStats.value[0].count = stats.document_count || documents.value.length
    knowledgeStats.value[1].count = stats.chunk_count || 0
    knowledgeStats.value[3].count = stats.index_size || 0
  } catch (error) {
    console.error('加载统计数据失败:', error)
    updateStats()
  }
}

// 更新统计数据
const updateStats = () => {
  knowledgeStats.value[0].count = documents.value.length
  knowledgeStats.value[1].count = documents.value.reduce((sum, doc) => sum + (doc.chunk_count || 0), 0)
}

// 刷新数据
const refreshData = () => {
  loadDocuments()
  loadKnowledgeStats()
}

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

// 上传前检查
const beforeUpload = (file: File) => {
  const isValidType = ['text/plain', 'text/markdown', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('只支持 txt, md, pdf, doc, docx 格式的文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }

  uploading.value = true
  return true
}

// 上传成功处理
const handleUploadSuccess = (response: UploadResponse) => {
  uploading.value = false
  ElMessage.success(`文档上传成功！已创建 ${response.chunks_created} 个文本块`)
  loadDocuments()
}

// 上传失败处理
const handleUploadError = (error: any) => {
  uploading.value = false
  console.error('上传失败:', error)
  ElMessage.error('文档上传失败')
}

// 显示搜索对话框
const showSearchDialog = () => {
  searchDialogVisible.value = true
}

// 执行RAG搜索
const performRAGSearch = async () => {
  if (!searchForm.query.trim()) {
    ElMessage.warning('请输入查询问题')
    return
  }

  searching.value = true
  try {
    const result = await ragKnowledgeApi.searchKnowledge(searchForm)

    // 处理搜索结果
    const documents = result.results.documents[0] || []
    const metadatas = result.results.metadatas[0] || []
    const distances = result.results.distances[0] || []

    searchResults.value = documents.map((doc, index) => ({
      document: doc,
      distance: distances[index] || 0,
      metadata: metadatas[index] || {}
    }))

    if (searchResults.value.length === 0) {
      ElMessage.info('未找到相关内容')
    } else {
      ElMessage.success(`找到 ${searchResults.value.length} 个相关结果`)
    }
  } catch (error) {
    console.error('RAG查询失败:', error)
    ElMessage.error('RAG查询失败')
  } finally {
    searching.value = false
  }
}

// 清空搜索结果
const clearSearchResults = () => {
  searchResults.value = []
  searchForm.query = ''
}

// 预览文档
const previewDocument = (document: DocumentType) => {
  currentDocument.value = document
  previewDialogVisible.value = true
}

// 测试RAG查询（针对特定文档）
const testRAGQuery = (document: DocumentType) => {
  searchForm.query = `关于 ${document.filename} 的内容`
  showSearchDialog()
}

// 删除文档
const deleteDocument = async (document: DocumentType) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文档 "${document.filename}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await ragKnowledgeApi.deleteDocument(document.id)
    ElMessage.success('删除成功')
    loadDocuments()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文档失败:', error)
      ElMessage.error('删除文档失败')
    }
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 处理表格选择变化
const handleSelectionChange = (selection: DocumentType[]) => {
  selectedRows.value = selection
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
}

// 处理当前页变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
}
</script>

<style scoped>
.rag-knowledge-container {
  padding: 24px;
  background: var(--bg-secondary);
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.title-icon {
  color: var(--primary-color);
}

.page-description {
  color: var(--text-secondary);
  margin: 0;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.document-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.chunk-icon { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.vector-icon { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.index-icon { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-top: 4px;
}

.toolbar-card {
  margin-bottom: 24px;
  border: none;
  box-shadow: var(--shadow-sm);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-card {
  border: none;
  box-shadow: var(--shadow-sm);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.card-title {
  font-weight: 600;
  color: var(--text-primary);
}

.card-subtitle {
  color: var(--text-secondary);
  font-size: 14px;
}

.filename-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: var(--primary-color);
}

.metadata-content {
  max-height: 200px;
  overflow-y: auto;
}

.metadata-item {
  margin-bottom: 8px;
  padding: 4px 0;
  border-bottom: 1px solid var(--border-light);
}

.metadata-item:last-child {
  border-bottom: none;
}

.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.search-container {
  padding: 16px 0;
}

.search-results {
  margin-top: 24px;
  border-top: 1px solid var(--border-light);
  padding-top: 16px;
}

.result-item {
  margin-bottom: 16px;
  padding: 16px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  border-left: 4px solid var(--primary-color);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.result-index {
  font-weight: 600;
  color: var(--text-primary);
}

.result-content {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 8px;
}

.result-metadata {
  color: var(--text-tertiary);
  font-size: 12px;
}

.preview-container {
  padding: 16px 0;
}

.preview-header {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-light);
}

.preview-header h4 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
}

.preview-meta {
  display: flex;
  gap: 24px;
  color: var(--text-secondary);
  font-size: 14px;
}

.preview-content {
  background: var(--bg-tertiary);
  border-radius: 8px;
  padding: 16px;
}

.preview-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: var(--text-primary);
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rag-knowledge-container {
    padding: 16px;
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-left {
    flex-wrap: wrap;
  }

  .toolbar-right {
    margin-top: 12px;
  }

  .stat-content {
    gap: 12px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
  }

  .stat-value {
    font-size: 20px;
  }

  .preview-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
