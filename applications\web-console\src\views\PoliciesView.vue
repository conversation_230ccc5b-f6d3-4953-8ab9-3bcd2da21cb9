<template>
  <div class="policies-container">
    <!-- 顶部搜索和操作栏 -->
    <el-card class="filter-card fade-in">
      <div class="filter-form">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="政策标题">
            <el-input v-model="searchForm.title" placeholder="请输入政策标题" clearable />
          </el-form-item>
          <el-form-item label="政策类型">
            <el-select v-model="searchForm.type" placeholder="选择政策类型" clearable>
              <el-option label="城市规划" value="urban-planning" />
              <el-option label="环境保护" value="environmental" />
              <el-option label="经济发展" value="economic" />
              <el-option label="社会保障" value="social-security" />
              <el-option label="交通运输" value="transportation" />
            </el-select>
          </el-form-item>
          <el-form-item label="发布状态">
            <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
              <el-option label="草稿" value="draft" />
              <el-option label="审核中" value="reviewing" />
              <el-option label="已发布" value="published" />
              <el-option label="已废止" value="abolished" />
            </el-select>
          </el-form-item>
          <el-form-item label="发布时间">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 政策统计 -->
    <div class="policy-stats slide-in-right">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6" v-for="(stat, index) in policyStats" :key="index">
          <el-card class="stat-card hover-lift" :body-style="{ padding: '20px' }">
            <div class="stat-content">
              <div class="stat-icon" :class="stat.iconClass">
                <el-icon><component :is="stat.icon" /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stat.count }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 政策列表 -->
    <el-card class="list-card fade-in">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <el-icon class="header-icon"><Files /></el-icon>
            <span>政策列表</span>
            <el-tag type="info" class="count-tag">{{ policyData.length }} 项</el-tag>
          </div>
          <div class="header-actions">
            <el-button type="success" :icon="Upload" plain @click="handleImport">导入政策</el-button>
            <el-button type="info" :icon="Download" plain @click="handleExport">导出数据</el-button>
            <el-button type="primary" :icon="Plus" @click="handleCreate">新建政策</el-button>
          </div>
        </div>
      </template>

      <!-- 政策表格 -->
      <el-table
        :data="policyData"
        border
        stripe
        style="width: 100%"
        v-loading="loading"
        :header-cell-style="{ background: '#f5f7fa' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="id" label="政策ID" width="100" sortable />
        <el-table-column prop="title" label="政策标题" min-width="200">
          <template #default="{ row }">
            <div class="policy-title-cell">
              <span class="policy-title" :title="row.title">{{ row.title }}</span>
              <el-tag v-if="row.isUrgent" size="small" type="danger" effect="dark">紧急</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="政策类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getPolicyTypeColor(row.type)" effect="plain">
              {{ getPolicyTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              :effect="row.status === 'published' ? 'dark' : 'light'"
            >
              <div class="status-tag">
                <div v-if="row.status === 'reviewing'" class="status-dot pulsing"></div>
                <span>{{ getStatusText(row.status) }}</span>
              </div>
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="department" label="发布部门" width="120" />
        <el-table-column prop="publishDate" label="发布时间" sortable width="120" />
        <el-table-column prop="effectiveDate" label="生效时间" sortable width="120" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-btns">
              <el-button
                type="primary"
                size="small"
                text
                :icon="View"
                @click="handleView(row)"
              >
                查看
              </el-button>
              <el-button
                v-if="row.status !== 'published'"
                type="primary"
                size="small"
                text
                :icon="Edit"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-dropdown>
                <el-button size="small" text>
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleDuplicate(row)">
                      <el-icon><CopyDocument /></el-icon> 复制政策
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleVersionHistory(row)">
                      <el-icon><Clock /></el-icon> 版本历史
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleDownloadPdf(row)">
                      <el-icon><Download /></el-icon> 下载PDF
                    </el-dropdown-item>
                    <el-dropdown-item divided @click="handleDelete(row)" style="color: #F56C6C;">
                      <el-icon><Delete /></el-icon> 删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Refresh,
  Edit,
  Delete,
  View,
  Download,
  Upload,
  Files,
  ArrowDown,
  CopyDocument,
  Clock
} from '@element-plus/icons-vue'

// 表格加载状态
const loading = ref(false)
const dateRange = ref([])

// 搜索表单
const searchForm = reactive({
  title: '',
  type: '',
  status: ''
})

// 统计数据
const policyStats = [
  { label: '全部政策', count: 156, icon: 'Files', iconClass: 'all-icon' },
  { label: '已发布', count: 89, icon: 'CircleCheck', iconClass: 'published-icon' },
  { label: '审核中', count: 23, icon: 'Clock', iconClass: 'reviewing-icon' },
  { label: '草稿', count: 44, icon: 'EditPen', iconClass: 'draft-icon' }
]

// 政策数据
const policyData = ref([
  {
    id: 'POL-2025-001',
    title: '关于加强城市绿化建设的实施意见',
    type: 'urban-planning',
    status: 'published',
    department: '城建局',
    publishDate: '2025-01-15',
    effectiveDate: '2025-02-01',
    isUrgent: false,
    summary: '为进一步提升城市生态环境质量，加强城市绿化建设工作...'
  },
  {
    id: 'POL-2025-002',
    title: '新能源汽车推广应用补贴政策',
    type: 'environmental',
    status: 'published',
    department: '发改委',
    publishDate: '2025-01-10',
    effectiveDate: '2025-01-15',
    isUrgent: true,
    summary: '为推动新能源汽车产业发展，促进节能减排...'
  },
  {
    id: 'POL-2025-003',
    title: '中小企业创新发展扶持办法',
    type: 'economic',
    status: 'reviewing',
    department: '经信局',
    publishDate: '',
    effectiveDate: '',
    isUrgent: false,
    summary: '为支持中小企业创新发展，激发市场主体活力...'
  },
  {
    id: 'POL-2025-004',
    title: '城市公共交通优化调整方案',
    type: 'transportation',
    status: 'draft',
    department: '交通局',
    publishDate: '',
    effectiveDate: '',
    isUrgent: false,
    summary: '为优化城市公共交通网络，提升市民出行体验...'
  },
  {
    id: 'POL-2025-005',
    title: '社会保障体系完善实施细则',
    type: 'social-security',
    status: 'published',
    department: '人社局',
    publishDate: '2025-01-08',
    effectiveDate: '2025-01-20',
    isUrgent: false,
    summary: '为完善社会保障体系，保障民生福祉...'
  }
])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(5)

// 选中行
const selectedRows = ref([])

// 生命周期钩子
onMounted(() => {
  fetchPolicyData()
})

// 获取政策数据
const fetchPolicyData = () => {
  loading.value = true
  // 模拟API请求延迟
  setTimeout(() => {
    loading.value = false
  }, 500)
}

// 政策类型文本映射
const getPolicyTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'urban-planning': '城市规划',
    'environmental': '环境保护',
    'economic': '经济发展',
    'social-security': '社会保障',
    'transportation': '交通运输'
  }
  return typeMap[type] || type
}

// 政策类型颜色映射
const getPolicyTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'urban-planning': 'primary',
    'environmental': 'success',
    'economic': 'warning',
    'social-security': 'info',
    'transportation': 'danger'
  }
  return colorMap[type] || ''
}

// 状态文本映射
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'draft': '草稿',
    'reviewing': '审核中',
    'published': '已发布',
    'abolished': '已废止'
  }
  return statusMap[status] || status
}

// 状态类型映射
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'draft': 'info',
    'reviewing': 'warning',
    'published': 'success',
    'abolished': 'danger'
  }
  return typeMap[status] || ''
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchPolicyData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.title = ''
  searchForm.type = ''
  searchForm.status = ''
  dateRange.value = []
  handleSearch()
}

// 表格选择行变化
const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchPolicyData()
}

// 页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchPolicyData()
}

// 创建政策
const handleCreate = () => {
  ElMessage.info('新建政策功能开发中...')
}

// 查看政策
const handleView = (row: any) => {
  ElMessage.info(`查看政策：${row.title}`)
}

// 编辑政策
const handleEdit = (row: any) => {
  ElMessage.info(`编辑政策：${row.title}`)
}

// 复制政策
const handleDuplicate = (row: any) => {
  ElMessage.info(`复制政策：${row.title}`)
}

// 版本历史
const handleVersionHistory = (row: any) => {
  ElMessage.info(`查看版本历史：${row.title}`)
}

// 下载PDF
const handleDownloadPdf = (row: any) => {
  ElMessage.info(`下载PDF：${row.title}`)
}

// 删除政策
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确认删除政策：${row.title}？此操作不可恢复！`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'danger'
  }).then(() => {
    const index = policyData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      policyData.value.splice(index, 1)
      total.value--
      ElMessage.success('删除成功')
    }
  }).catch(() => {})
}

// 导入政策
const handleImport = () => {
  ElMessage.info('导入政策功能开发中...')
}

// 导出数据
const handleExport = () => {
  ElMessage.info('导出数据功能开发中...')
}
</script>

<style scoped>
.policies-container {
  width: 100%;
}

.filter-card {
  margin-bottom: 16px;
  background-color: var(--bg-primary);
}

.policy-stats {
  margin-bottom: 16px;
}

.stat-card {
  margin-bottom: 16px;
  transition: all 0.3s ease;
  background: var(--bg-primary);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.all-icon {
  background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
}

.published-icon {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
}

.reviewing-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
}

.draft-icon {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.list-card {
  margin-bottom: 16px;
  background-color: var(--bg-primary);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: var(--text-primary);
}

.header-icon {
  margin-right: 8px;
  color: var(--primary-color);
}

.count-tag {
  margin-left: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.policy-title-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.policy-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  color: var(--text-primary);
}

.status-tag {
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
}

.status-dot.pulsing {
  animation: pulse 1.5s infinite;
}

.action-btns {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  gap: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .search-form {
    flex-wrap: wrap;
  }

  .el-form--inline .el-form-item {
    margin-right: 10px;
  }

  .header-actions {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .action-btns {
    flex-wrap: wrap;
    justify-content: flex-end;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }
}

@media (max-width: 576px) {
  .header-actions .el-button {
    flex: 1;
  }

  .pagination-container {
    justify-content: center;
  }
}
</style>
