#!/usr/bin/env python3
"""
Agent Executor Service 测试脚本

用于测试 Agent 循环和工具函数功能
"""

import asyncio
import httpx
import json
import os

# 服务配置
SERVICE_URL = "http://localhost:8000"

async def test_tools_schema():
    """测试获取工具定义"""
    print("🔍 测试工具定义获取...")
    
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{SERVICE_URL}/tools-schema")
        if response.status_code == 200:
            data = response.json()
            print("✅ 工具定义获取成功")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 工具定义获取失败: {response.status_code}")

async def test_health():
    """测试健康检查"""
    print("\n🏥 测试健康检查...")
    
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{SERVICE_URL}/health")
        if response.status_code == 200:
            print("✅ 服务健康检查通过")
            print(response.json())
        else:
            print(f"❌ 健康检查失败: {response.status_code}")

async def test_service_info():
    """测试服务信息"""
    print("\n📋 测试服务信息...")
    
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{SERVICE_URL}/")
        if response.status_code == 200:
            data = response.json()
            print("✅ 服务信息获取成功")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 服务信息获取失败: {response.status_code}")

async def test_debug_llm(command: str):
    """测试 LLM 调试端点"""
    print(f"\n🔍 调试 LLM 调用: {command}")

    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            response = await client.post(
                f"{SERVICE_URL}/debug-llm",
                json={"command": command}
            )
            if response.status_code == 200:
                data = response.json()
                print("✅ LLM 调试成功")
                print(f"模型: {data.get('model')}")
                print(f"是否有工具调用: {data.get('response', {}).get('has_tool_calls')}")
                if data.get('response', {}).get('tool_calls'):
                    print("工具调用:")
                    for tc in data['response']['tool_calls']:
                        print(f"  - {tc['function']['name']}: {tc['function']['arguments']}")
                print(f"响应内容: {data.get('response', {}).get('content')}")
            else:
                print(f"❌ LLM 调试失败: {response.status_code}")
                print(response.text)
        except Exception as e:
            print(f"❌ LLM 调试异常: {e}")

async def test_agent_command(command: str):
    """测试 Agent 命令执行"""
    print(f"\n🤖 测试 Agent 命令: {command}")

    async with httpx.AsyncClient(timeout=120.0) as client:
        try:
            async with client.stream(
                "POST",
                f"{SERVICE_URL}/execute-command",
                json={"command": command}
            ) as response:
                if response.status_code == 200:
                    print("✅ Agent 开始执行...")
                    async for chunk in response.aiter_text():
                        if chunk:
                            print(chunk, end="")
                    print("\n✅ Agent 执行完成")
                else:
                    print(f"❌ Agent 执行失败: {response.status_code}")
                    print(await response.aread())
        except Exception as e:
            print(f"❌ Agent 执行异常: {e}")

async def test_individual_tools():
    """测试单个工具函数"""
    print("\n🔧 测试单个工具函数...")
    
    # 测试创建模拟任务
    print("\n1. 测试创建模拟任务...")
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{SERVICE_URL}/test-tools/run-simulation",
                json={
                    "city_name": "测试城市",
                    "rainfall": 50.0,
                    "temperature": 25.0,
                    "population": 1000000
                }
            )
            if response.status_code == 200:
                data = response.json()
                print("✅ 模拟任务创建测试成功")
                task_id = data.get("data", {}).get("id")
                if task_id:
                    print(f"   任务ID: {task_id}")
                    
                    # 测试获取任务状态
                    print("\n2. 测试获取任务状态...")
                    status_response = await client.post(
                        f"{SERVICE_URL}/test-tools/get-task-status",
                        json={"task_id": task_id}
                    )
                    if status_response.status_code == 200:
                        print("✅ 任务状态获取测试成功")
                        print(f"   状态: {status_response.json()}")
                    else:
                        print(f"❌ 任务状态获取失败: {status_response.status_code}")
            else:
                print(f"❌ 模拟任务创建失败: {response.status_code}")
                print(response.text)
        except Exception as e:
            print(f"❌ 工具测试异常: {e}")

async def main():
    """主测试函数"""
    print("🚀 开始测试 Agent Executor Service")
    print("=" * 50)
    
    # 基础功能测试
    await test_health()
    await test_service_info()
    await test_tools_schema()
    
    # 工具函数测试
    await test_individual_tools()
    
    # LLM 调试测试
    debug_commands = [
        "你好，请介绍一下你的功能",
        "我想为上海创建一个水资源模拟，降雨量60mm，温度28度，人口2500万",
        "请查询任务ID 1 的状态"
    ]

    print("\n" + "=" * 50)
    print("🔍 LLM 调试测试")
    print("=" * 50)

    for command in debug_commands:
        await test_debug_llm(command)

    # Agent 循环测试
    print("\n" + "=" * 50)
    print("🤖 Agent 循环测试")
    print("=" * 50)

    for command in debug_commands:
        await test_agent_command(command)
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
