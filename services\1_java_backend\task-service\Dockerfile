# --- Build Stage ---
# 使用一个明确的、广泛使用的 Maven 镜像
FROM maven:3.9.6-eclipse-temurin-17 AS builder

# 设置工作目录
WORKDIR /app

#自定义的 Maven 配置
COPY settings.xml /root/.m2/settings.xml

# 1. 复制 pom.xml，为缓存依赖做准备
COPY taskservice/pom.xml .

# 2. 下载所有依赖。这一步可以被高效缓存
#    如果 pom.xml 不变，后续构建会跳过此步
RUN mvn dependency:go-offline -B

# 3. 复制源代码
COPY taskservice/src ./src

# 4. 打包应用，生成 jar 文件。此步会使用上面已下载的依赖
RUN mvn package -DskipTests -B

# --- Final Stage ---
# 使用 Eclipse Temurin 的 Alpine JRE 镜像，这是一个很好的选择
FROM eclipse-temurin:17-jre-alpine

# 设置工作目录
WORKDIR /app

# 从构建阶段复制最终的 jar 文件
# 注意：构建阶段的 WORKDIR 是 /app，jar 在 target 目录下
COPY --from=builder /app/target/*.jar app.jar

# 暴露服务端口
EXPOSE 8080

# 定义容器启动命令
ENTRYPOINT ["java", "-jar", "app.jar"]