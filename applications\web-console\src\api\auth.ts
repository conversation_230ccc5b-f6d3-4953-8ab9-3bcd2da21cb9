import axios from 'axios'
import WhitelistService from '@/services/whitelistService'

// 定义登录请求的数据类型
export interface LoginRequest {
  username: string
  password: string
}

// 定义登录响应的数据类型
export interface LoginResponse {
  token: string
  username: string
}

// 创建 axios 实例
const api = axios.create({
  timeout: 10000, // 10秒超时
})

// 请求拦截器：自动添加 Authorization 头
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器：处理认证错误
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token 过期或无效，清除本地存储并跳转到登录页
      localStorage.removeItem('token')
      localStorage.removeItem('username')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

/**
 * 用户登录（带白名单验证）
 * @param loginData 登录数据
 * @returns Promise<LoginResponse>
 */
export const login = async (loginData: LoginRequest): Promise<LoginResponse> => {
  try {
    // 1. 首先进行白名单验证
    const isAllowed = await WhitelistService.preLoginValidation(loginData.username)
    if (!isAllowed) {
      throw new Error('用户不在白名单中或已被禁用')
    }

    // 2. 进行实际的登录请求
    const response = await api.post<LoginResponse>('/auth/login', loginData)

    // 3. 登录成功后的处理
    WhitelistService.handleLoginSuccess(loginData.username)

    return response.data
  } catch (error) {
    console.error('登录失败:', error)
    throw error
  }
}

/**
 * 用户登出
 */
export const logout = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('username')
  WhitelistService.clearUserInfo()
  window.location.href = '/login'
}

/**
 * 检查用户是否已登录
 * @returns boolean
 */
export const isAuthenticated = (): boolean => {
  const token = localStorage.getItem('token')
  return !!token
}

/**
 * 获取当前用户信息
 * @returns 用户信息对象或 null
 */
export const getCurrentUser = () => {
  const username = localStorage.getItem('username')
  const token = localStorage.getItem('token')
  
  if (username && token) {
    return { username, token }
  }
  
  return null
}

export default api
