<template>
  <div class="advanced-table">
    <!-- 表格工具栏 -->
    <div class="table-toolbar" v-if="showToolbar">
      <div class="toolbar-left">
        <slot name="toolbar-left">
          <el-button 
            v-if="showRefresh" 
            :icon="Refresh" 
            @click="handleRefresh"
            :loading="loading"
          >
            刷新
          </el-button>
          <el-button 
            v-if="showExport" 
            :icon="Download" 
            @click="handleExport"
          >
            导出
          </el-button>
        </slot>
      </div>
      
      <div class="toolbar-right">
        <slot name="toolbar-right">
          <!-- 列设置 -->
          <el-popover 
            v-if="showColumnSettings"
            placement="bottom-end" 
            :width="300" 
            trigger="click"
          >
            <template #reference>
              <el-button :icon="Setting" circle />
            </template>
            <div class="column-settings">
              <div class="settings-header">
                <span>列设置</span>
                <el-button text size="small" @click="resetColumns">重置</el-button>
              </div>
              <el-checkbox-group v-model="visibleColumns" class="column-list">
                <el-checkbox 
                  v-for="col in allColumns" 
                  :key="col.prop" 
                  :label="col.prop"
                  class="column-item"
                >
                  {{ col.label }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </el-popover>
          
          <!-- 密度设置 -->
          <el-dropdown v-if="showDensity" @command="handleDensityChange">
            <el-button :icon="Grid" circle />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="default" :class="{ active: density === 'default' }">
                  默认
                </el-dropdown-item>
                <el-dropdown-item command="medium" :class="{ active: density === 'medium' }">
                  中等
                </el-dropdown-item>
                <el-dropdown-item command="small" :class="{ active: density === 'small' }">
                  紧凑
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </slot>
      </div>
    </div>

    <!-- 表格主体 -->
    <el-table
      ref="tableRef"
      :data="data"
      :loading="loading"
      :border="border"
      :stripe="stripe"
      :size="density"
      :height="height"
      :max-height="maxHeight"
      :row-key="rowKey"
      :tree-props="treeProps"
      :default-sort="defaultSort"
      :header-cell-style="headerCellStyle"
      :row-class-name="rowClassName"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      @row-click="handleRowClick"
      @row-dblclick="handleRowDblClick"
      class="data-table"
      :class="tableClass"
    >
      <!-- 选择列 -->
      <el-table-column 
        v-if="showSelection" 
        type="selection" 
        width="50" 
        fixed="left"
        :selectable="selectable"
      />
      
      <!-- 序号列 -->
      <el-table-column 
        v-if="showIndex" 
        type="index" 
        label="序号" 
        width="60" 
        fixed="left"
        :index="indexMethod"
      />
      
      <!-- 动态列 -->
      <template v-for="column in filteredColumns" :key="column.prop">
        <el-table-column
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :fixed="column.fixed"
          :sortable="column.sortable"
          :align="column.align"
          :header-align="column.headerAlign"
          :show-overflow-tooltip="column.showOverflowTooltip !== false"
          :formatter="column.formatter"
          :class-name="column.className"
        >
          <template #default="scope" v-if="column.slot">
            <slot 
              :name="column.slot" 
              :row="scope.row" 
              :column="scope.column" 
              :$index="scope.$index"
            />
          </template>
          
          <template #header="scope" v-if="column.headerSlot">
            <slot 
              :name="column.headerSlot" 
              :column="scope.column" 
              :$index="scope.$index"
            />
          </template>
        </el-table-column>
      </template>
      
      <!-- 操作列 -->
      <el-table-column 
        v-if="showActions" 
        label="操作" 
        :width="actionWidth"
        fixed="right"
        class-name="action-column"
      >
        <template #default="scope">
          <slot 
            name="actions" 
            :row="scope.row" 
            :column="scope.column" 
            :$index="scope.$index"
          >
            <el-button size="small" type="primary" text>编辑</el-button>
            <el-button size="small" type="danger" text>删除</el-button>
          </slot>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="table-pagination" v-if="showPagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        :layout="paginationLayout"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Refresh, 
  Download, 
  Setting, 
  Grid 
} from '@element-plus/icons-vue'

export interface TableColumn {
  prop: string
  label: string
  width?: number | string
  minWidth?: number | string
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean | 'custom'
  align?: 'left' | 'center' | 'right'
  headerAlign?: 'left' | 'center' | 'right'
  showOverflowTooltip?: boolean
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
  className?: string
  slot?: string
  headerSlot?: string
}

interface Props {
  data: any[]
  columns: TableColumn[]
  loading?: boolean
  border?: boolean
  stripe?: boolean
  height?: number | string
  maxHeight?: number | string
  rowKey?: string
  treeProps?: object
  defaultSort?: object
  showToolbar?: boolean
  showRefresh?: boolean
  showExport?: boolean
  showColumnSettings?: boolean
  showDensity?: boolean
  showSelection?: boolean
  showIndex?: boolean
  showActions?: boolean
  showPagination?: boolean
  actionWidth?: number
  total?: number
  pageSizes?: number[]
  paginationLayout?: string
  selectable?: (row: any, index: number) => boolean
  rowClassName?: string | ((params: { row: any, rowIndex: number }) => string)
  indexMethod?: (index: number) => number
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  border: true,
  stripe: true,
  showToolbar: true,
  showRefresh: true,
  showExport: true,
  showColumnSettings: true,
  showDensity: true,
  showSelection: false,
  showIndex: false,
  showActions: true,
  showPagination: true,
  actionWidth: 150,
  total: 0,
  pageSizes: () => [10, 20, 50, 100],
  paginationLayout: 'total, sizes, prev, pager, next, jumper'
})

const emit = defineEmits<{
  refresh: []
  export: []
  selectionChange: [selection: any[]]
  sortChange: [sort: any]
  rowClick: [row: any, column: any, event: Event]
  rowDblClick: [row: any, column: any, event: Event]
  sizeChange: [size: number]
  currentChange: [page: number]
}>()

// 响应式数据
const tableRef = ref()
const density = ref<'default' | 'medium' | 'small'>('default')
const currentPage = ref(1)
const pageSize = ref(props.pageSizes[0])
const visibleColumns = ref<string[]>([])
const allColumns = ref<TableColumn[]>([])

// 计算属性
const filteredColumns = computed(() => {
  return allColumns.value.filter(col => visibleColumns.value.includes(col.prop))
})

const headerCellStyle = computed(() => ({
  background: 'var(--el-table-header-bg-color, #fafafa)',
  color: 'var(--el-table-header-text-color, #606266)',
  fontWeight: 600
}))

const tableClass = computed(() => ({
  [`table-${density.value}`]: true
}))

// 监听器
watch(() => props.columns, (newColumns) => {
  allColumns.value = [...newColumns]
  visibleColumns.value = newColumns.map(col => col.prop)
}, { immediate: true })

// 方法
const handleRefresh = () => {
  emit('refresh')
}

const handleExport = () => {
  emit('export')
}

const handleSelectionChange = (selection: any[]) => {
  emit('selectionChange', selection)
}

const handleSortChange = (sort: any) => {
  emit('sortChange', sort)
}

const handleRowClick = (row: any, column: any, event: Event) => {
  emit('rowClick', row, column, event)
}

const handleRowDblClick = (row: any, column: any, event: Event) => {
  emit('rowDblClick', row, column, event)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  emit('sizeChange', size)
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  emit('currentChange', page)
}

const handleDensityChange = (command: string) => {
  density.value = command as 'default' | 'medium' | 'small'
}

const resetColumns = () => {
  visibleColumns.value = allColumns.value.map(col => col.prop)
}

// 公开方法
const clearSelection = () => {
  tableRef.value?.clearSelection()
}

const toggleRowSelection = (row: any, selected?: boolean) => {
  tableRef.value?.toggleRowSelection(row, selected)
}

const toggleAllSelection = () => {
  tableRef.value?.toggleAllSelection()
}

const setCurrentRow = (row: any) => {
  tableRef.value?.setCurrentRow(row)
}

const clearSort = () => {
  tableRef.value?.clearSort()
}

const doLayout = () => {
  nextTick(() => {
    tableRef.value?.doLayout()
  })
}

defineExpose({
  clearSelection,
  toggleRowSelection,
  toggleAllSelection,
  setCurrentRow,
  clearSort,
  doLayout
})
</script>

<style scoped>
.advanced-table {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-secondary);
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.column-settings {
  padding: 8px 0;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px 8px;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: 8px;
  font-weight: 600;
}

.column-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 0 12px;
}

.column-item {
  display: block;
  margin-bottom: 8px;
  padding: 4px 0;
}

.data-table {
  --el-table-border-color: var(--border-light);
  --el-table-header-bg-color: var(--bg-tertiary);
  --el-table-row-hover-bg-color: var(--bg-tertiary);
}

.data-table :deep(.el-table__header-wrapper) {
  background: var(--bg-tertiary);
}

.data-table :deep(.action-column .cell) {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.table-pagination {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  border-top: 1px solid var(--border-light);
  background: var(--bg-secondary);
}

/* 密度样式 */
.table-small :deep(.el-table .el-table__cell) {
  padding: 4px 0;
}

.table-medium :deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

.table-default :deep(.el-table .el-table__cell) {
  padding: 12px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .table-pagination {
    justify-content: center;
  }

  .table-pagination :deep(.el-pagination) {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .table-toolbar {
    padding: 12px;
  }

  .toolbar-left,
  .toolbar-right {
    flex-wrap: wrap;
    gap: 6px;
  }

  .table-pagination {
    padding: 12px;
  }
}

/* 下拉菜单激活状态 */
:deep(.el-dropdown-menu__item.active) {
  color: var(--primary-color);
  background-color: var(--primary-color-light-9);
}
</style>
