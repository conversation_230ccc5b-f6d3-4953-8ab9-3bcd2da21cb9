import torch
import torch.nn as nn
import mlflow
import os

# 从环境变量中读取MLflow跟踪URI
MLFLOW_TRACKING_URI = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-svc:5000')
mlflow.set_tracking_uri(MLFLOW_TRACKING_URI)
print(f"model_loader.py: 使用MLflow跟踪URI: {MLFLOW_TRACKING_URI}")

# 定义与train_pinn.py完全相同的HydroPINN模型类
class HydroPINN(nn.Module):
    def __init__(self, hidden_layers=3, neurons_per_layer=20):
        super(HydroPINN, self).__init__()
        
        # 输入层: rainfall和temperature
        self.input_layer = nn.Linear(2, neurons_per_layer)
        
        # 隐藏层
        self.hidden_layers = nn.ModuleList()
        for _ in range(hidden_layers):
            self.hidden_layers.append(nn.Linear(neurons_per_layer, neurons_per_layer))
        
        # 输出层: water_level
        self.output_layer = nn.Linear(neurons_per_layer, 1)
        
        # 激活函数
        self.activation = nn.Tanh()
    
    def forward(self, x):
        x = self.activation(self.input_layer(x))
        for layer in self.hidden_layers:
            x = self.activation(layer(x))
        return self.output_layer(x)

# 扩展的HydroPINN模型，支持考虑取水量的影响
class EnhancedHydroPINN:
    def __init__(self, base_model):
        """
        包装原始的HydroPINN模型，添加对取水量的处理
        
        参数:
            base_model: 原始的HydroPINN模型实例
        """
        self.base_model = base_model
        # 定义流域面积（平方米）- 设定为中等规模水库集水区面积
        self.basin_area = 5000000  # 5平方公里 = 5,000,000平方米
        # 蒸发因子 - 影响水位下降的额外因素（温度越高，蒸发越多）
        self.evaporation_factor = 0.0002  # 每摄氏度每天蒸发损失的水位（米）
        # 渗透因子 - 地下水渗透导致的水位下降
        self.seepage_factor = 0.001  # 每天渗透损失的水位（米）
        # 降雨转化系数 - 降雨量转化为水位上升的系数（考虑径流损失）
        self.rainfall_conversion = 0.7  # 70%的降雨转化为有效水量
    
    def __call__(self, rainfall, temperature, net_water_withdrawal=0):
        """
        预测水位，考虑降雨、温度和取水量的影响
        
        参数:
            rainfall (float): 降雨量（毫米）
            temperature (float): 温度（摄氏度）
            net_water_withdrawal (float): 净取水量（立方米），默认为0
            
        返回:
            float: 预测的水位（米）
        """
        # 准备输入张量
        input_tensor = torch.tensor([[float(rainfall), float(temperature)]], dtype=torch.float32)
        
        # 获取模型所在设备
        device = next(self.base_model.parameters()).device
        input_tensor = input_tensor.to(device)
        
        # 使用基础模型预测初始水位
        with torch.no_grad():
            base_water_level = self.base_model(input_tensor).item()
        
        # 计算水位变化
        # 1. 取水导致的水位下降（米）
        water_level_decrease = net_water_withdrawal / self.basin_area
        
        # 2. 温度导致的蒸发损失（米）
        evaporation_loss = max(0, temperature - 20) * self.evaporation_factor
        
        # 3. 地下渗透损失（米）
        seepage_loss = self.seepage_factor
        
        # 4. 降雨导致的水位上升（米）- 毫米转米并考虑转化率
        rainfall_gain = (rainfall / 1000) * self.rainfall_conversion
        
        # 计算净水位变化
        net_water_level_change = rainfall_gain - water_level_decrease - evaporation_loss - seepage_loss
        
        # 应用水位变化
        adjusted_water_level = base_water_level + net_water_level_change
        
        # 确保水位不会低于0
        adjusted_water_level = max(0, adjusted_water_level)
        
        return adjusted_water_level


def load_model_from_mlflow(model_name="HydroPINN", model_version="1", basin_area=1000000):
    """
    从MLflow加载指定版本的模型
    
    参数:
        model_name (str): 模型名称，默认为"HydroPINN"
        model_version (str): 模型版本号，默认为"1"
        basin_area (float): 流域面积（平方米），默认为1,000,000
        
    返回:
        加载好的PyTorch模型
    """
    try:
        print(f"尝试从MLflow加载模型 {model_name} 版本 {model_version}")
        print(f"MLflow跟踪URI: {mlflow.get_tracking_uri()}")
        print(f"使用流域面积: {basin_area} 平方米")
        
        # 从MLflow加载模型
        model_uri = f"models:/{model_name}/{model_version}"
        base_model = mlflow.pytorch.load_model(model_uri)
        
        # 包装为增强模型
        enhanced_model = EnhancedHydroPINN(base_model)
        # 设置流域面积
        enhanced_model.basin_area = basin_area
        
        print(f"成功加载模型 {model_name} 版本 {model_version}")
        return enhanced_model
    
    except Exception as e:
        print(f"加载模型时出错: {str(e)}")
        print("尝试加载本地备份模型...")
        
        # 如果从MLflow加载失败，尝试加载本地备份模型
        try:
            # 初始化模型结构
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            backup_model = HydroPINN(hidden_layers=3, neurons_per_layer=20).to(device)
            
            # 检查本地模型文件是否存在
            local_model_path = os.path.join(os.path.dirname(__file__), "models", "hydro_pinn_v1.pth")
            if os.path.exists(local_model_path):
                backup_model.load_state_dict(torch.load(local_model_path, map_location=device))
                print(f"成功从本地加载备份模型: {local_model_path}")
                # 包装为增强模型
                enhanced_model = EnhancedHydroPINN(backup_model)
                # 设置流域面积
                enhanced_model.basin_area = basin_area
                return enhanced_model
            else:
                # 尝试在当前目录查找
                local_model_path = os.path.join(os.path.dirname(__file__), "hydro_pinn_v1.pth")
                if os.path.exists(local_model_path):
                    backup_model.load_state_dict(torch.load(local_model_path, map_location=device))
                    print(f"成功从当前目录加载备份模型: {local_model_path}")
                    # 包装为增强模型
                    enhanced_model = EnhancedHydroPINN(backup_model)
                    # 设置流域面积
                    enhanced_model.basin_area = basin_area
                    return enhanced_model
                else:
                    raise FileNotFoundError(f"本地模型文件不存在: {local_model_path}")
        
        except Exception as backup_error:
            print(f"加载备份模型也失败: {str(backup_error)}")
            raise Exception("无法加载模型，主要方法和备份方法都失败了")


if __name__ == "__main__":
    # 测试模型加载
    try:
        model = load_model_from_mlflow()
        print("模型已加载")
        
        # 创建一个测试输入
        rainfall = 50.0
        temperature = 25.0
        net_water_withdrawal = 10.0
        
        # 进行推理
        prediction = model(rainfall, temperature, net_water_withdrawal)
        
        print(f"测试输入 rainfall={rainfall}, temperature={temperature}, net_water_withdrawal={net_water_withdrawal}")
        print(f"预测水位: {prediction:.2f}")
    
    except Exception as e:
        print(f"测试失败: {str(e)}") 