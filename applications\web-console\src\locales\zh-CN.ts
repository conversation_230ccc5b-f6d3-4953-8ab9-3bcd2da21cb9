export default {
  // 通用
  common: {
    confirm: '确定',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    add: '添加',
    create: '创建',
    update: '更新',
    search: '搜索',
    reset: '重置',
    refresh: '刷新',
    export: '导出',
    import: '导入',
    upload: '上传',
    download: '下载',
    view: '查看',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    submit: '提交',
    close: '关闭',
    loading: '加载中...',
    noData: '暂无数据',
    total: '共 {count} 条',
    selected: '已选择 {count} 项',
    operation: '操作',
    status: '状态',
    createTime: '创建时间',
    updateTime: '更新时间',
    description: '描述',
    remark: '备注'
  },

  // 导航菜单
  menu: {
    home: '首页',
    system: '系统管理',
    users: '用户管理',
    roles: '角色管理',
    permissions: '权限管理',
    data: '数据管理',
    tasks: '任务管理',
    policies: '政策管理',
    knowledge: '知识库',
    about: '关于系统'
  },

  // 用户相关
  user: {
    profile: '个人信息',
    settings: '系统设置',
    logout: '退出登录',
    login: '登录',
    username: '用户名',
    password: '密码',
    email: '邮箱',
    phone: '手机号',
    avatar: '头像',
    lastLogin: '最后登录',
    online: '在线',
    offline: '离线'
  },

  // 首页
  home: {
    welcome: '欢迎回来',
    greeting: {
      morning: '早上好',
      afternoon: '下午好',
      evening: '晚上好',
      night: '夜深了'
    },
    todayIs: '今天是',
    workHappy: '祝您工作愉快！',
    createTask: '创建新任务',
    searchData: '查询数据',
    viewReports: '查看报告',
    totalUsers: '总用户数',
    totalTasks: '总任务数',
    totalPolicies: '政策数量',
    systemStatus: '系统状态',
    recentActivities: '最近活动',
    userLogin: '用户登录',
    taskCreate: '创建任务',
    policyUpdate: '更新政策',
    knowledgeQuery: '查询知识库'
  },

  // 用户管理
  users: {
    title: '用户管理',
    list: '用户列表',
    add: '新建用户',
    edit: '编辑用户',
    delete: '删除用户',
    enable: '启用',
    disable: '禁用',
    resetPassword: '重置密码',
    assignRole: '分配角色',
    userInfo: '用户信息',
    basicInfo: '基本信息',
    roleInfo: '角色信息',
    loginInfo: '登录信息',
    deleteConfirm: '确认删除用户：{name}？此操作不可恢复！',
    enableConfirm: '确认启用用户：{name}？',
    disableConfirm: '确认禁用用户：{name}？'
  },

  // 角色管理
  roles: {
    title: '角色管理',
    list: '角色列表',
    add: '新建角色',
    edit: '编辑角色',
    delete: '删除角色',
    copy: '复制角色',
    permissions: '权限配置',
    users: '查看用户',
    roleName: '角色名称',
    roleCode: '角色编码',
    roleIcon: '角色图标',
    roleColor: '角色颜色',
    userCount: '用户数量',
    deleteConfirm: '确认删除角色：{name}？此操作不可恢复！'
  },

  // 权限管理
  permissions: {
    title: '权限管理',
    list: '权限列表',
    add: '新建权限',
    edit: '编辑权限',
    delete: '删除权限',
    addChild: '添加子权限',
    permissionName: '权限名称',
    permissionCode: '权限编码',
    permissionType: '权限类型',
    parentPermission: '上级权限',
    routePath: '路由路径',
    apiPath: 'API路径',
    sort: '排序',
    types: {
      menu: '菜单权限',
      button: '按钮权限',
      data: '数据权限',
      api: 'API权限'
    },
    deleteConfirm: '确认删除权限：{name}？此操作不可恢复！'
  },

  // 任务管理
  tasks: {
    title: '任务管理',
    list: '任务列表',
    add: '新建任务',
    edit: '编辑任务',
    delete: '删除任务',
    taskName: '任务名称',
    taskType: '任务类型',
    priority: '优先级',
    assignee: '执行人',
    dueDate: '截止日期',
    progress: '进度',
    status: {
      pending: '待处理',
      processing: '处理中',
      completed: '已完成',
      cancelled: '已取消'
    },
    priorities: {
      low: '低',
      medium: '中',
      high: '高',
      urgent: '紧急'
    }
  },

  // 政策管理
  policies: {
    title: '政策管理',
    list: '政策列表',
    add: '新建政策',
    edit: '编辑政策',
    delete: '删除政策',
    policyTitle: '政策标题',
    policyType: '政策类型',
    department: '发布部门',
    publishDate: '发布时间',
    effectiveDate: '生效时间',
    status: {
      draft: '草稿',
      reviewing: '审核中',
      published: '已发布',
      abolished: '已废止'
    },
    types: {
      urbanPlanning: '城市规划',
      environmental: '环境保护',
      economic: '经济发展',
      socialSecurity: '社会保障',
      transportation: '交通运输'
    },
    urgent: '紧急',
    duplicate: '复制政策',
    versionHistory: '版本历史',
    downloadPdf: '下载PDF'
  },

  // 知识库
  knowledge: {
    title: '知识库',
    list: '知识列表',
    add: '新建知识',
    edit: '编辑知识',
    delete: '删除知识',
    knowledgeTitle: '知识标题',
    category: '知识分类',
    author: '作者',
    viewCount: '浏览量',
    likeCount: '点赞数',
    hot: '热门',
    share: '分享',
    categories: {
      tech: '技术文档',
      business: '业务流程',
      policy: '政策解读',
      guide: '操作指南',
      faq: '常见问题'
    },
    status: {
      published: '已发布',
      draft: '草稿',
      reviewing: '审核中'
    }
  },

  // 搜索
  search: {
    placeholder: '请输入搜索关键词...',
    advanced: '高级搜索',
    collapse: '收起',
    quickSearch: '快速搜索',
    advancedSearch: '高级搜索',
    searchHistory: '搜索记录',
    savedSearches: '保存的搜索',
    recentSearches: '最近搜索',
    saveSearch: '保存搜索',
    clearHistory: '清空',
    searchName: '搜索名称',
    searchNamePlaceholder: '请输入搜索名称',
    searchNameRule: '搜索名称长度应为1-20个字符',
    noConditions: '请先设置搜索条件',
    searchSaved: '搜索条件已保存'
  },

  // 表格
  table: {
    refresh: '刷新',
    export: '导出',
    columnSettings: '列设置',
    density: '密度',
    densityDefault: '默认',
    densityMedium: '中等',
    densitySmall: '紧凑',
    resetColumns: '重置',
    noData: '暂无数据',
    total: '共 {total} 条记录',
    selected: '已选择 {count} 项'
  },

  // 消息提示
  message: {
    success: '操作成功',
    error: '操作失败',
    warning: '警告',
    info: '提示',
    deleteSuccess: '删除成功',
    saveSuccess: '保存成功',
    updateSuccess: '更新成功',
    createSuccess: '创建成功',
    loginSuccess: '登录成功',
    logoutSuccess: '退出成功',
    networkError: '网络错误，请稍后重试',
    permissionDenied: '权限不足',
    dataNotFound: '数据不存在',
    operationConfirm: '确认执行此操作？',
    unsavedChanges: '有未保存的更改，确认离开？'
  },

  // 验证规则
  validation: {
    required: '此项为必填项',
    email: '请输入正确的邮箱地址',
    phone: '请输入正确的手机号码',
    password: '密码长度应为6-20个字符',
    username: '用户名长度应为3-20个字符',
    minLength: '长度不能少于 {min} 个字符',
    maxLength: '长度不能超过 {max} 个字符',
    number: '请输入数字',
    integer: '请输入整数',
    positive: '请输入正数',
    range: '请输入 {min} 到 {max} 之间的数值'
  },

  // 设置
  settings: {
    title: '系统设置',
    theme: '主题设置',
    language: '语言设置',
    notification: '通知设置',
    account: '账户设置',
    about: '关于系统',
    themeMode: '主题模式',
    colorScheme: '配色方案',
    fontSize: '字体大小',
    compactMode: '紧凑模式',
    enableSound: '声音提醒',
    enableDesktop: '桌面通知',
    autoSave: '自动保存',
    showTips: '显示提示',
    animations: '动画效果',
    keyboardShortcuts: '快捷键',
    exportSettings: '导出设置',
    importSettings: '导入设置',
    resetSettings: '重置设置',
    modes: {
      light: '浅色模式',
      dark: '深色模式',
      auto: '跟随系统'
    },
    colors: {
      blue: '蓝色',
      green: '绿色',
      purple: '紫色',
      orange: '橙色',
      red: '红色'
    }
  }
}
