# 🔐 白名单机制使用指南

本指南介绍如何使用和管理 Vue 项目中的用户白名单功能。

## 🎯 功能概述

白名单机制确保只有预先授权的用户才能登录系统，提供了额外的安全层保护。

### 核心特性
- ✅ **用户白名单验证**: 只允许白名单中的用户登录
- 🔧 **灵活配置**: 可以启用/禁用白名单功能
- 📊 **登录监控**: 记录所有登录尝试
- 👥 **用户管理**: 图形化界面管理白名单用户
- 📈 **统计分析**: 登录成功/失败统计

## 🚀 快速开始

### 1. 默认配置

系统默认包含以下用户：

| 用户名 | 密码 | 角色 | 状态 |
|--------|------|------|------|
| admin | admin123 | 管理员 | 启用 |
| operator | operator123 | 运维人员 | 启用 |
| viewer | viewer123 | 只读用户 | 启用 |

### 2. 访问白名单管理

1. 登录系统 (使用 admin 账户)
2. 在侧边栏找到 "集群管理" → "白名单管理"
3. 进入白名单管理页面

### 3. 基本操作

#### 添加用户
1. 点击 "添加用户" 按钮
2. 填写用户信息：
   - 用户名 (必填)
   - 显示名称 (必填)
   - 角色 (必选)
   - 部门 (可选)
   - 邮箱 (可选)
   - 状态 (启用/禁用)
3. 点击 "确定" 保存

#### 编辑用户
1. 在用户列表中点击 "编辑" 按钮
2. 修改用户信息
3. 点击 "确定" 保存更改

#### 启用/禁用用户
- 点击用户列表中的 "启用/禁用" 按钮
- 禁用的用户无法登录系统

#### 删除用户
1. 点击用户列表中的 "删除" 按钮
2. 确认删除操作

## ⚙️ 配置选项

### 白名单配置

在白名单管理页面点击 "配置" 按钮，可以设置：

- **启用白名单**: 开启/关闭白名单验证
- **大小写不敏感**: 用户名匹配时忽略大小写
- **记录登录尝试**: 是否记录所有登录尝试

### 代码配置

编辑 `src/config/whitelist.ts` 文件：

```typescript
// 白名单配置
export const WHITELIST_CONFIG = {
  // 是否启用白名单验证
  enabled: true,
  
  // 是否允许大小写不敏感匹配
  caseInsensitive: false,
  
  // 是否记录登录尝试
  logAttempts: true,
  
  // 失败尝试的最大次数（可选，用于后续扩展）
  maxFailedAttempts: 5,
  
  // 锁定时间（分钟）
  lockoutDuration: 30,
}
```

## 👥 用户角色说明

### 角色层级
1. **guest** (访客) - 最低权限
2. **viewer** (只读用户) - 查看权限
3. **user** (普通用户) - 基本操作权限
4. **operator** (运维人员) - 运维操作权限
5. **admin** (管理员) - 最高权限

### 角色权限
- **管理员**: 所有功能的完整访问权限
- **运维人员**: 集群管理、监控、日志查看权限
- **开发人员**: 开发相关功能权限
- **普通用户**: 基本查看和操作权限
- **只读用户**: 仅查看权限，无修改权限

## 📊 监控和日志

### 登录统计
白名单管理页面显示：
- 启用用户数量
- 禁用用户数量
- 今日成功登录次数
- 今日失败尝试次数

### 登录日志
点击 "登录日志" 按钮查看：
- 登录时间
- 用户名
- 登录结果 (成功/失败)
- IP 地址
- 用户代理信息

### 日志操作
- **清空日志**: 删除所有登录记录
- **导出日志**: 下载 CSV 格式的日志文件

## 🔧 开发者指南

### 添加新用户

在 `src/config/whitelist.ts` 中添加：

```typescript
export const WHITELIST_USERS: WhitelistUser[] = [
  // 现有用户...
  {
    username: 'newuser',
    displayName: '新用户',
    role: 'user',
    department: '开发部门',
    email: '<EMAIL>',
    enabled: true,
    createdAt: '2024-01-01',
  },
]
```

### 自定义验证逻辑

修改 `src/services/whitelistService.ts` 中的验证方法：

```typescript
static validateUser(username: string): WhitelistValidationResult {
  // 自定义验证逻辑
  // ...
}
```

### 权限检查

在组件中使用权限检查：

```typescript
import WhitelistService from '@/services/whitelistService'

// 检查用户权限
const hasAdminPermission = WhitelistService.hasPermission('admin')

// 获取当前用户信息
const userInfo = WhitelistService.getCurrentUserInfo()
```

## 🛡️ 安全最佳实践

### 1. 定期审查用户
- 定期检查白名单用户
- 及时禁用或删除不需要的账户
- 确保用户角色权限合适

### 2. 监控登录活动
- 定期查看登录日志
- 关注异常登录尝试
- 及时处理可疑活动

### 3. 配置建议
- 生产环境建议启用白名单
- 开发环境可以禁用白名单便于测试
- 启用登录尝试记录

### 4. 备份和恢复
- 定期备份白名单配置
- 记录重要的配置更改
- 准备应急访问方案

## 🔄 集成后端验证

### 当前实现
目前白名单验证在前端进行，适合快速部署和测试。

### 生产环境建议
1. **后端验证**: 将白名单验证移至后端
2. **数据库存储**: 使用数据库存储用户信息
3. **API 集成**: 通过 API 管理白名单
4. **审计日志**: 在服务器端记录详细日志

### 迁移步骤
1. 在后端实现白名单 API
2. 修改前端调用后端 API
3. 迁移现有用户数据
4. 更新权限验证逻辑

## 🐛 故障排除

### 常见问题

#### 1. 无法登录
**问题**: 用户在白名单中但无法登录
**解决方案**:
- 检查用户是否被禁用
- 确认用户名拼写正确
- 检查白名单是否启用

#### 2. 白名单不生效
**问题**: 禁用白名单后仍然验证
**解决方案**:
- 刷新页面重新加载配置
- 检查 `WHITELIST_CONFIG.enabled` 设置
- 清除浏览器缓存

#### 3. 日志不记录
**问题**: 登录尝试没有记录
**解决方案**:
- 检查 `WHITELIST_CONFIG.logAttempts` 设置
- 确认浏览器支持 localStorage
- 检查浏览器控制台错误

### 调试工具

在浏览器控制台中使用：

```javascript
// 查看当前配置
console.log(WhitelistService.exportConfig())

// 查看用户信息
console.log(WhitelistService.getCurrentUserInfo())

// 查看登录统计
console.log(WhitelistService.getWhitelistStats())
```

## 📝 更新日志

### v1.0.0 (2024-01-01)
- ✅ 初始版本发布
- ✅ 基础白名单功能
- ✅ 用户管理界面
- ✅ 登录日志记录
- ✅ 权限验证系统

---

**需要帮助？** 请查看项目文档或联系开发团队。
