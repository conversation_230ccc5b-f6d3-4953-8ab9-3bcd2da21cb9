FROM python:3.10-slim

WORKDIR /app

# 配置pip使用阿里云源
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set global.trusted-host mirrors.aliyun.com

# 首先只复制requirements.txt文件
COPY requirements.txt .

# 升级pip
RUN pip install --upgrade pip


# 安装torch CPU版本 (保持不变以利用缓存)
RUN pip install torch==2.3.0+cpu -f https://download.pytorch.org/whl/cpu/torch_stable.html

# 从此步开始改用官方源安装基本依赖
RUN pip config set global.index-url https://pypi.org/simple/ && \
    pip config set global.trusted-host pypi.org && \
    pip install --timeout 100 \
    tokenizers==0.13.3 \
    fastapi \
    uvicorn \
    psycopg2-binary \
    requests \
    openai \
    graphlib-backport && \
    pip cache purge

# 继续使用官方源安装大型依赖 (使用引号包裹版本约束)
RUN pip install chromadb==0.4.24 

RUN pip install "langchain<0.2.0"

RUN pip install langchain-community

RUN pip install sentence-transformers==2.4.0

RUN pip install mlflow 

RUN pip cache purge

# 创建模型目录
RUN mkdir -p /app/models

# 只有模型文件变化时才重建此层
COPY /all-MiniLM-L6-v2 /app/models/all-MiniLM-L6-v2

# 最后一步才复制应用代码，这样代码变更时只会重建最后一层
RUN python -c "from sentence_transformers import SentenceTransformer; SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')"

RUN pip install openai==1.2.0 && \
    pip install httpx==0.27.2

COPY main.py .
# 暴露端口
EXPOSE 8000

# 容器启动时执行的命令
CMD ["python", "-u", "main.py"] 