import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElNotification } from 'element-plus'

export interface Notification {
  id: string
  title: string
  message: string
  type: 'success' | 'warning' | 'info' | 'error'
  timestamp: Date
  read: boolean
  action?: {
    text: string
    handler: () => void
  }
  persistent?: boolean
}

export const useNotificationStore = defineStore('notification', () => {
  // 状态
  const notifications = ref<Notification[]>([])
  const maxNotifications = ref(50)
  const enableSound = ref(localStorage.getItem('notification-sound') !== 'false')
  const enableDesktop = ref(localStorage.getItem('desktop-notifications') === 'true')

  // 计算属性
  const unreadCount = computed(() => 
    notifications.value.filter(n => !n.read).length
  )

  const recentNotifications = computed(() => 
    notifications.value
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, 10)
  )

  /**
   * 添加通知
   */
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      id: generateId(),
      timestamp: new Date(),
      read: false,
      ...notification
    }

    notifications.value.unshift(newNotification)

    // 限制通知数量
    if (notifications.value.length > maxNotifications.value) {
      notifications.value = notifications.value.slice(0, maxNotifications.value)
    }

    // 显示Element Plus通知
    showElementNotification(newNotification)

    // 播放声音
    if (enableSound.value) {
      playNotificationSound()
    }

    // 显示桌面通知
    if (enableDesktop.value) {
      showDesktopNotification(newNotification)
    }

    return newNotification.id
  }

  /**
   * 标记通知为已读
   */
  const markAsRead = (id: string) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.read = true
    }
  }

  /**
   * 标记所有通知为已读
   */
  const markAllAsRead = () => {
    notifications.value.forEach(n => n.read = true)
  }

  /**
   * 删除通知
   */
  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  /**
   * 清空所有通知
   */
  const clearAll = () => {
    notifications.value = []
  }

  /**
   * 清空已读通知
   */
  const clearRead = () => {
    notifications.value = notifications.value.filter(n => !n.read)
  }

  /**
   * 设置声音开关
   */
  const setSoundEnabled = (enabled: boolean) => {
    enableSound.value = enabled
    localStorage.setItem('notification-sound', enabled.toString())
  }

  /**
   * 设置桌面通知开关
   */
  const setDesktopEnabled = (enabled: boolean) => {
    enableDesktop.value = enabled
    localStorage.setItem('desktop-notifications', enabled.toString())
    
    if (enabled) {
      requestDesktopPermission()
    }
  }

  /**
   * 快捷方法 - 成功通知
   */
  const success = (title: string, message: string) => {
    return addNotification({ title, message, type: 'success' })
  }

  /**
   * 快捷方法 - 警告通知
   */
  const warning = (title: string, message: string) => {
    return addNotification({ title, message, type: 'warning' })
  }

  /**
   * 快捷方法 - 信息通知
   */
  const info = (title: string, message: string) => {
    return addNotification({ title, message, type: 'info' })
  }

  /**
   * 快捷方法 - 错误通知
   */
  const error = (title: string, message: string) => {
    return addNotification({ title, message, type: 'error' })
  }

  // 私有方法
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  const showElementNotification = (notification: Notification) => {
    ElNotification({
      title: notification.title,
      message: notification.message,
      type: notification.type,
      duration: notification.persistent ? 0 : 4500,
      showClose: true
    })
  }

  const playNotificationSound = () => {
    try {
      const audio = new Audio('/notification.mp3')
      audio.volume = 0.3
      audio.play().catch(() => {
        // 忽略播放失败
      })
    } catch {
      // 忽略音频创建失败
    }
  }

  const showDesktopNotification = (notification: Notification) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.id
      })
    }
  }

  const requestDesktopPermission = async () => {
    if ('Notification' in window && Notification.permission === 'default') {
      await Notification.requestPermission()
    }
  }

  /**
   * 初始化通知系统
   */
  const initNotifications = () => {
    // 请求桌面通知权限
    if (enableDesktop.value) {
      requestDesktopPermission()
    }

    // 添加一些示例通知
    addNotification({
      title: '欢迎使用 Athena 系统',
      message: '系统已成功启动，祝您使用愉快！',
      type: 'success'
    })
  }

  return {
    // 状态
    notifications,
    maxNotifications,
    enableSound,
    enableDesktop,

    // 计算属性
    unreadCount,
    recentNotifications,

    // 方法
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    clearRead,
    setSoundEnabled,
    setDesktopEnabled,
    success,
    warning,
    info,
    error,
    initNotifications
  }
})
