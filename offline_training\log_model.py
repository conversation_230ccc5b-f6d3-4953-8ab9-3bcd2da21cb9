import os
import torch
import mlflow
from train_pinn import HydroPINN  # 从 train_pinn.py 导入模型类

# 优先使用环境变量中的地址，如果没有则使用服务名
mlflow_uri = os.environ.get("MLFLOW_TRACKING_URI", "http://mlflow-service:5000")
mlflow.set_tracking_uri(mlflow_uri)
print(f"MLflow 跟踪 URI: {mlflow.get_tracking_uri()}")

# 设置实验名称
mlflow.set_experiment("PINN-Hydro-Models")

# 开始一个 MLflow 运行
with mlflow.start_run(run_name="hydro-pinn-v1") as run:
    # 打印运行信息
    print(f"MLflow 运行 ID: {run.info.run_id}")
    print(f"MLflow 实验 ID: {run.info.experiment_id}")
    
    # 设备配置
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 实例化与训练时相同架构的模型
    model = HydroPINN(hidden_layers=3, neurons_per_layer=20).to(device)
    print("模型架构已初始化")
    
    # 加载训练好的模型权重
    model_path = "hydro_pinn_v1.pth"
    if os.path.exists(model_path):
        model.load_state_dict(torch.load(model_path, map_location=device))
        print(f"成功从 {model_path} 加载权重")
    else:
        raise FileNotFoundError(f"在 {model_path} 未找到模型权重文件")
    
    # 将模型设置为评估模式
    model.eval()
    
    # 记录模型参数作为指标
    mlflow.log_param("hidden_layers", 3)
    mlflow.log_param("neurons_per_layer", 20)
    mlflow.log_param("activation", "Tanh")
    
    # 记录 PyTorch 模型
    mlflow.pytorch.log_model(
        pytorch_model=model, 
        artifact_path="hydro-pinn-model",
        registered_model_name="HydroPINN",  # 这将在 MLflow 模型注册表中注册模型
        conda_env={
            "channels": ["defaults", "pytorch"],
            "dependencies": [
                "python=3.8.0",
                "pytorch>=1.8.0",
                "numpy>=1.19.2"
            ],
            "name": "hydro-pinn-env"
        }
    )
    print("模型已成功记录到 MLflow")
    
    # 获取记录的模型位置
    model_uri = f"runs:/{run.info.run_id}/hydro-pinn-model"
    print(f"模型保存在: {model_uri}")
    print(f"加载此模型请使用: mlflow.pytorch.load_model('{model_uri}')")

print("完成！模型已被记录到 MLflow。")
