export default {
  // Common
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    create: 'Create',
    update: 'Update',
    search: 'Search',
    reset: 'Reset',
    refresh: 'Refresh',
    export: 'Export',
    import: 'Import',
    upload: 'Upload',
    download: 'Download',
    view: 'View',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    submit: 'Submit',
    close: 'Close',
    loading: 'Loading...',
    noData: 'No Data',
    total: 'Total {count} items',
    selected: '{count} items selected',
    operation: 'Operation',
    status: 'Status',
    createTime: 'Create Time',
    updateTime: 'Update Time',
    description: 'Description',
    remark: 'Remark'
  },

  // Navigation Menu
  menu: {
    home: 'Home',
    system: 'System',
    users: 'Users',
    roles: 'Roles',
    permissions: 'Permissions',
    data: 'Data',
    tasks: 'Tasks',
    policies: 'Policies',
    knowledge: 'Knowledge',
    about: 'About'
  },

  // User Related
  user: {
    profile: 'Profile',
    settings: 'Settings',
    logout: 'Logout',
    login: 'Login',
    username: 'Userna<PERSON>',
    password: 'Password',
    email: 'Email',
    phone: 'Phone',
    avatar: 'Avatar',
    lastLogin: 'Last Login',
    online: 'Online',
    offline: 'Offline'
  },

  // Home
  home: {
    welcome: 'Welcome back',
    greeting: {
      morning: 'Good morning',
      afternoon: 'Good afternoon',
      evening: 'Good evening',
      night: 'Good night'
    },
    todayIs: 'Today is',
    workHappy: 'Have a great day!',
    createTask: 'Create Task',
    searchData: 'Search Data',
    viewReports: 'View Reports',
    totalUsers: 'Total Users',
    totalTasks: 'Total Tasks',
    totalPolicies: 'Total Policies',
    systemStatus: 'System Status',
    recentActivities: 'Recent Activities',
    userLogin: 'User Login',
    taskCreate: 'Task Created',
    policyUpdate: 'Policy Updated',
    knowledgeQuery: 'Knowledge Queried'
  },

  // User Management
  users: {
    title: 'User Management',
    list: 'User List',
    add: 'Add User',
    edit: 'Edit User',
    delete: 'Delete User',
    enable: 'Enable',
    disable: 'Disable',
    resetPassword: 'Reset Password',
    assignRole: 'Assign Role',
    userInfo: 'User Info',
    basicInfo: 'Basic Info',
    roleInfo: 'Role Info',
    loginInfo: 'Login Info',
    deleteConfirm: 'Confirm to delete user: {name}? This action cannot be undone!',
    enableConfirm: 'Confirm to enable user: {name}?',
    disableConfirm: 'Confirm to disable user: {name}?'
  },

  // Role Management
  roles: {
    title: 'Role Management',
    list: 'Role List',
    add: 'Add Role',
    edit: 'Edit Role',
    delete: 'Delete Role',
    copy: 'Copy Role',
    permissions: 'Permissions',
    users: 'View Users',
    roleName: 'Role Name',
    roleCode: 'Role Code',
    roleIcon: 'Role Icon',
    roleColor: 'Role Color',
    userCount: 'User Count',
    deleteConfirm: 'Confirm to delete role: {name}? This action cannot be undone!'
  },

  // Permission Management
  permissions: {
    title: 'Permission Management',
    list: 'Permission List',
    add: 'Add Permission',
    edit: 'Edit Permission',
    delete: 'Delete Permission',
    addChild: 'Add Child Permission',
    permissionName: 'Permission Name',
    permissionCode: 'Permission Code',
    permissionType: 'Permission Type',
    parentPermission: 'Parent Permission',
    routePath: 'Route Path',
    apiPath: 'API Path',
    sort: 'Sort',
    types: {
      menu: 'Menu Permission',
      button: 'Button Permission',
      data: 'Data Permission',
      api: 'API Permission'
    },
    deleteConfirm: 'Confirm to delete permission: {name}? This action cannot be undone!'
  },

  // Task Management
  tasks: {
    title: 'Task Management',
    list: 'Task List',
    add: 'Add Task',
    edit: 'Edit Task',
    delete: 'Delete Task',
    taskName: 'Task Name',
    taskType: 'Task Type',
    priority: 'Priority',
    assignee: 'Assignee',
    dueDate: 'Due Date',
    progress: 'Progress',
    status: {
      pending: 'Pending',
      processing: 'Processing',
      completed: 'Completed',
      cancelled: 'Cancelled'
    },
    priorities: {
      low: 'Low',
      medium: 'Medium',
      high: 'High',
      urgent: 'Urgent'
    }
  },

  // Policy Management
  policies: {
    title: 'Policy Management',
    list: 'Policy List',
    add: 'Add Policy',
    edit: 'Edit Policy',
    delete: 'Delete Policy',
    policyTitle: 'Policy Title',
    policyType: 'Policy Type',
    department: 'Department',
    publishDate: 'Publish Date',
    effectiveDate: 'Effective Date',
    status: {
      draft: 'Draft',
      reviewing: 'Reviewing',
      published: 'Published',
      abolished: 'Abolished'
    },
    types: {
      urbanPlanning: 'Urban Planning',
      environmental: 'Environmental',
      economic: 'Economic',
      socialSecurity: 'Social Security',
      transportation: 'Transportation'
    },
    urgent: 'Urgent',
    duplicate: 'Duplicate',
    versionHistory: 'Version History',
    downloadPdf: 'Download PDF'
  },

  // Knowledge Base
  knowledge: {
    title: 'Knowledge Base',
    list: 'Knowledge List',
    add: 'Add Knowledge',
    edit: 'Edit Knowledge',
    delete: 'Delete Knowledge',
    knowledgeTitle: 'Knowledge Title',
    category: 'Category',
    author: 'Author',
    viewCount: 'Views',
    likeCount: 'Likes',
    hot: 'Hot',
    share: 'Share',
    categories: {
      tech: 'Technical Documentation',
      business: 'Business Process',
      policy: 'Policy Interpretation',
      guide: 'User Guide',
      faq: 'FAQ'
    },
    status: {
      published: 'Published',
      draft: 'Draft',
      reviewing: 'Reviewing'
    }
  },

  // Search
  search: {
    placeholder: 'Enter search keywords...',
    advanced: 'Advanced',
    collapse: 'Collapse',
    quickSearch: 'Quick Search',
    advancedSearch: 'Advanced Search',
    searchHistory: 'Search History',
    savedSearches: 'Saved Searches',
    recentSearches: 'Recent Searches',
    saveSearch: 'Save Search',
    clearHistory: 'Clear',
    searchName: 'Search Name',
    searchNamePlaceholder: 'Enter search name',
    searchNameRule: 'Search name should be 1-20 characters',
    noConditions: 'Please set search conditions first',
    searchSaved: 'Search conditions saved'
  },

  // Table
  table: {
    refresh: 'Refresh',
    export: 'Export',
    columnSettings: 'Columns',
    density: 'Density',
    densityDefault: 'Default',
    densityMedium: 'Medium',
    densitySmall: 'Small',
    resetColumns: 'Reset',
    noData: 'No Data',
    total: 'Total {total} records',
    selected: '{count} items selected'
  },

  // Messages
  message: {
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info',
    deleteSuccess: 'Deleted successfully',
    saveSuccess: 'Saved successfully',
    updateSuccess: 'Updated successfully',
    createSuccess: 'Created successfully',
    loginSuccess: 'Login successful',
    logoutSuccess: 'Logout successful',
    networkError: 'Network error, please try again later',
    permissionDenied: 'Permission denied',
    dataNotFound: 'Data not found',
    operationConfirm: 'Confirm this operation?',
    unsavedChanges: 'You have unsaved changes, confirm to leave?'
  },

  // Validation
  validation: {
    required: 'This field is required',
    email: 'Please enter a valid email address',
    phone: 'Please enter a valid phone number',
    password: 'Password should be 6-20 characters',
    username: 'Username should be 3-20 characters',
    minLength: 'Minimum length is {min} characters',
    maxLength: 'Maximum length is {max} characters',
    number: 'Please enter a number',
    integer: 'Please enter an integer',
    positive: 'Please enter a positive number',
    range: 'Please enter a value between {min} and {max}'
  },

  // Settings
  settings: {
    title: 'Settings',
    theme: 'Theme',
    language: 'Language',
    notification: 'Notification',
    account: 'Account',
    about: 'About',
    themeMode: 'Theme Mode',
    colorScheme: 'Color Scheme',
    fontSize: 'Font Size',
    compactMode: 'Compact Mode',
    enableSound: 'Sound Notification',
    enableDesktop: 'Desktop Notification',
    autoSave: 'Auto Save',
    showTips: 'Show Tips',
    animations: 'Animations',
    keyboardShortcuts: 'Keyboard Shortcuts',
    exportSettings: 'Export Settings',
    importSettings: 'Import Settings',
    resetSettings: 'Reset Settings',
    modes: {
      light: 'Light Mode',
      dark: 'Dark Mode',
      auto: 'Follow System'
    },
    colors: {
      blue: 'Blue',
      green: 'Green',
      purple: 'Purple',
      orange: 'Orange',
      red: 'Red'
    }
  }
}
