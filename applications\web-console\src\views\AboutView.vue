<template>
  <div class="about-container">
    <el-row :gutter="24">
      <!-- 系统信息卡片 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="about-card system-info">
          <template #header>
            <div class="card-header">
              <h3>系统信息</h3>
              <el-tag type="success">稳定版</el-tag>
            </div>
          </template>
          
          <div class="info-content">
            <div class="info-item">
              <span class="info-label">系统名称</span>
              <span class="info-value">Athena 智能决策管理系统</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">版本号</span>
              <span class="info-value">v1.0.0</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">部署环境</span>
              <span class="info-value">Kubernetes 容器集群</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">后端架构</span>
              <span class="info-value">微服务 + API Gateway</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">前端框架</span>
              <span class="info-value">Vue 3 + Element Plus</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">数据更新</span>
              <span class="info-value">2025-01-17</span>
            </div>
          </div>
          
          <div class="system-actions">
            <el-button type="primary">
              <el-icon><RefreshRight /></el-icon>
              检查更新
            </el-button>
            <el-button>
              <el-icon><Document /></el-icon>
              查看文档
            </el-button>
          </div>
        </el-card>
      </el-col>
      
      <!-- 技术栈卡片 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="about-card tech-stack">
          <template #header>
            <div class="card-header">
              <h3>技术栈</h3>
            </div>
          </template>
          
          <div class="tech-content">
            <div class="tech-group">
              <h4>前端技术</h4>
              <div class="tech-tags">
                <el-tag>Vue 3</el-tag>
                <el-tag>TypeScript</el-tag>
                <el-tag>Vite</el-tag>
                <el-tag>Element Plus</el-tag>
                <el-tag>Pinia</el-tag>
              </div>
            </div>
            
            <div class="tech-group">
              <h4>后端技术</h4>
              <div class="tech-tags">
                <el-tag type="success">Spring Boot</el-tag>
                <el-tag type="success">Spring Cloud</el-tag>
                <el-tag type="success">Java</el-tag>
                <el-tag type="success">Python</el-tag>
                <el-tag type="success">FastAPI</el-tag>
              </div>
            </div>
            
            <div class="tech-group">
              <h4>数据库</h4>
              <div class="tech-tags">
                <el-tag type="warning">PostgreSQL</el-tag>
                <el-tag type="warning">Neo4j</el-tag>
                <el-tag type="warning">Redis</el-tag>
              </div>
            </div>
            
            <div class="tech-group">
              <h4>AI & 模型</h4>
              <div class="tech-tags">
                <el-tag type="danger">Physics-Informed Neural Networks</el-tag>
                <el-tag type="danger">LLM</el-tag>
                <el-tag type="danger">MLflow</el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 开发团队 -->
    <el-card class="about-card team-card">
      <template #header>
        <div class="card-header">
          <h3>开发团队</h3>
        </div>
      </template>
      
      <div class="team-content">
        <div class="team-description">
          <p>Athena系统由跨学科团队开发，集合了软件工程、人工智能、流体力学、政策分析等领域的专家。</p>
          <p>我们致力于打造智能决策支持平台，帮助政府和企业进行科学决策。</p>
        </div>
        
        <div class="contact-info">
          <h4>联系我们</h4>
          <p>
            <el-icon><Message /></el-icon> 
            <span><EMAIL></span>
          </p>
          <p>
            <el-icon><Phone /></el-icon>
            <span>************</span>
          </p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { RefreshRight, Document, Message, Phone } from '@element-plus/icons-vue'
</script>

<style scoped>
.about-container {
  width: 100%;
}

.about-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

/* 系统信息卡片样式 */
.info-content {
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px dashed #ebeef5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  width: 120px;
  color: #909399;
  font-size: 14px;
}

.info-value {
  flex: 1;
  color: #303133;
  font-weight: 500;
}

.system-actions {
  display: flex;
  gap: 16px;
}

/* 技术栈卡片样式 */
.tech-group {
  margin-bottom: 20px;
}

.tech-group h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #606266;
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tech-tags .el-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

/* 团队卡片样式 */
.team-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.team-description p {
  margin-bottom: 16px;
  line-height: 1.6;
  color: #606266;
}

.contact-info h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.contact-info p {
    display: flex;
    align-items: center;
  margin-bottom: 12px;
  color: #606266;
}

.contact-info .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .system-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .system-actions .el-button {
    width: 100%;
  }
  
  .info-item {
    flex-direction: column;
  }
  
  .info-label {
    width: 100%;
    margin-bottom: 4px;
  }
}
</style>
