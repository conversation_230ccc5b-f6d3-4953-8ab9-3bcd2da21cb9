<template>
  <div class="permissions-container">
    <!-- 顶部搜索和操作栏 -->
    <el-card class="filter-card fade-in">
      <div class="filter-form">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="权限名称">
            <el-input v-model="searchForm.permissionName" placeholder="请输入权限名称" clearable />
          </el-form-item>
          <el-form-item label="权限类型">
            <el-select v-model="searchForm.type" placeholder="选择权限类型" clearable>
              <el-option label="菜单权限" value="menu" />
              <el-option label="按钮权限" value="button" />
              <el-option label="数据权限" value="data" />
              <el-option label="API权限" value="api" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
              <el-option label="启用" value="active" />
              <el-option label="禁用" value="disabled" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 权限统计 -->
    <div class="permission-stats slide-in-right">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6" v-for="(stat, index) in permissionStats" :key="index">
          <el-card class="stat-card hover-lift" :body-style="{ padding: '20px' }">
            <div class="stat-content">
              <div class="stat-icon" :class="stat.iconClass">
                <el-icon><component :is="stat.icon" /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stat.count }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 权限树形结构 -->
    <el-card class="tree-card fade-in">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <el-icon class="header-icon"><Key /></el-icon>
            <span>权限管理</span>
            <el-tag type="info" class="count-tag">{{ permissionData.length }} 项</el-tag>
          </div>
          <div class="header-actions">
            <el-button type="success" :icon="Plus" @click="handleCreate">新建权限</el-button>
            <el-button type="primary" :icon="Refresh" @click="handleRefresh">刷新权限</el-button>
          </div>
        </div>
      </template>
      
      <!-- 权限树 -->
      <el-tree
        :data="permissionData"
        :props="treeProps"
        node-key="id"
        :default-expand-all="true"
        :expand-on-click-node="false"
        class="permission-tree"
      >
        <template #default="{ node, data }">
          <div class="tree-node">
            <div class="node-content">
              <el-icon class="node-icon" :style="{ color: getPermissionColor(data.type) }">
                <component :is="getPermissionIcon(data.type)" />
              </el-icon>
              <span class="node-label">{{ data.name }}</span>
              <el-tag 
                :type="getPermissionTypeColor(data.type)" 
                size="small" 
                effect="plain"
                class="node-type"
              >
                {{ getPermissionTypeText(data.type) }}
              </el-tag>
              <el-tag 
                :type="data.status === 'active' ? 'success' : 'danger'" 
                size="small"
                effect="dark"
                class="node-status"
              >
                {{ data.status === 'active' ? '启用' : '禁用' }}
              </el-tag>
            </div>
            <div class="node-actions">
              <el-button 
                type="primary" 
                size="small" 
                text 
                :icon="Plus" 
                @click="handleAddChild(data)"
                v-if="data.type === 'menu'"
              >
                添加子权限
              </el-button>
              <el-button 
                type="primary" 
                size="small" 
                text 
                :icon="Edit" 
                @click="handleEdit(data)"
              >
                编辑
              </el-button>
              <el-button 
                type="danger" 
                size="small" 
                text 
                :icon="Delete" 
                @click="handleDelete(data)"
              >
                删除
              </el-button>
            </div>
          </div>
        </template>
      </el-tree>
    </el-card>
    
    <!-- 权限表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑权限' : '新建权限'"
      width="650px"
    >
      <el-form
        ref="permissionFormRef"
        :model="permissionForm"
        :rules="permissionRules"
        label-width="120px"
      >
        <el-form-item label="上级权限" v-if="!isEdit">
          <el-tree-select
            v-model="permissionForm.parentId"
            :data="permissionData"
            :props="treeProps"
            placeholder="选择上级权限（可选）"
            clearable
            check-strictly
          />
        </el-form-item>
        
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="permissionForm.name" placeholder="请输入权限名称" />
        </el-form-item>
        
        <el-form-item label="权限编码" prop="code">
          <el-input v-model="permissionForm.code" placeholder="请输入权限编码" />
        </el-form-item>
        
        <el-form-item label="权限类型" prop="type">
          <el-select v-model="permissionForm.type" placeholder="选择权限类型">
            <el-option label="菜单权限" value="menu" />
            <el-option label="按钮权限" value="button" />
            <el-option label="数据权限" value="data" />
            <el-option label="API权限" value="api" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="路由路径" v-if="permissionForm.type === 'menu'">
          <el-input v-model="permissionForm.path" placeholder="请输入路由路径" />
        </el-form-item>
        
        <el-form-item label="API路径" v-if="permissionForm.type === 'api'">
          <el-input v-model="permissionForm.apiPath" placeholder="请输入API路径" />
        </el-form-item>
        
        <el-form-item label="排序">
          <el-input-number v-model="permissionForm.sort" :min="0" :max="999" />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-radio-group v-model="permissionForm.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="disabled">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="权限描述">
          <el-input v-model="permissionForm.description" type="textarea" :rows="3" placeholder="请输入权限描述" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Search, 
  Refresh, 
  Edit, 
  Delete, 
  Key,
  Menu,
  Operation,
  DataBoard,
  Link
} from '@element-plus/icons-vue'

// 表格加载状态
const loading = ref(false)

// 搜索表单
const searchForm = reactive({
  permissionName: '',
  type: '',
  status: ''
})

// 统计数据
const permissionStats = [
  { label: '全部权限', count: 45, icon: 'Key', iconClass: 'all-icon' },
  { label: '菜单权限', count: 18, icon: 'Menu', iconClass: 'menu-icon' },
  { label: '按钮权限', count: 15, icon: 'Operation', iconClass: 'button-icon' },
  { label: 'API权限', count: 12, icon: 'Link', iconClass: 'api-icon' }
]

// 树形属性配置
const treeProps = {
  children: 'children',
  label: 'name',
  value: 'id'
}

// 权限数据（树形结构）
const permissionData = ref([
  {
    id: 1,
    name: '系统管理',
    code: 'system',
    type: 'menu',
    path: '/system',
    status: 'active',
    sort: 1,
    description: '系统管理模块',
    children: [
      {
        id: 11,
        name: '用户管理',
        code: 'system:user',
        type: 'menu',
        path: '/system/users',
        status: 'active',
        sort: 1,
        description: '用户管理页面',
        children: [
          {
            id: 111,
            name: '新增用户',
            code: 'system:user:add',
            type: 'button',
            status: 'active',
            sort: 1,
            description: '新增用户按钮权限'
          },
          {
            id: 112,
            name: '编辑用户',
            code: 'system:user:edit',
            type: 'button',
            status: 'active',
            sort: 2,
            description: '编辑用户按钮权限'
          },
          {
            id: 113,
            name: '删除用户',
            code: 'system:user:delete',
            type: 'button',
            status: 'active',
            sort: 3,
            description: '删除用户按钮权限'
          }
        ]
      },
      {
        id: 12,
        name: '角色管理',
        code: 'system:role',
        type: 'menu',
        path: '/system/roles',
        status: 'active',
        sort: 2,
        description: '角色管理页面',
        children: [
          {
            id: 121,
            name: '新增角色',
            code: 'system:role:add',
            type: 'button',
            status: 'active',
            sort: 1,
            description: '新增角色按钮权限'
          },
          {
            id: 122,
            name: '编辑角色',
            code: 'system:role:edit',
            type: 'button',
            status: 'active',
            sort: 2,
            description: '编辑角色按钮权限'
          }
        ]
      }
    ]
  },
  {
    id: 2,
    name: '数据管理',
    code: 'data',
    type: 'menu',
    path: '/data',
    status: 'active',
    sort: 2,
    description: '数据管理模块',
    children: [
      {
        id: 21,
        name: '任务管理',
        code: 'data:task',
        type: 'menu',
        path: '/data/tasks',
        status: 'active',
        sort: 1,
        description: '任务管理页面'
      },
      {
        id: 22,
        name: '政策管理',
        code: 'data:policy',
        type: 'menu',
        path: '/data/policies',
        status: 'active',
        sort: 2,
        description: '政策管理页面'
      }
    ]
  }
])

// 对话框相关
const dialogVisible = ref(false)
const isEdit = ref(false)

// 表单引用
const permissionFormRef = ref()

// 权限表单
const permissionForm = reactive({
  id: null,
  parentId: null,
  name: '',
  code: '',
  type: 'menu',
  path: '',
  apiPath: '',
  sort: 0,
  status: 'active',
  description: ''
})

// 表单验证规则
const permissionRules = {
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入权限编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9:_]*$/, message: '权限编码只能包含字母、数字、冒号和下划线，且以字母开头', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择权限类型', trigger: 'change' }
  ]
}

// 页面加载时
onMounted(() => {
  // 初始化数据
})

// 获取权限图标
const getPermissionIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    'menu': 'Menu',
    'button': 'Operation',
    'data': 'DataBoard',
    'api': 'Link'
  }
  return iconMap[type] || 'Key'
}

// 获取权限颜色
const getPermissionColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'menu': '#409eff',
    'button': '#67c23a',
    'data': '#e6a23c',
    'api': '#f56c6c'
  }
  return colorMap[type] || '#909399'
}

// 权限类型文本映射
const getPermissionTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'menu': '菜单',
    'button': '按钮',
    'data': '数据',
    'api': 'API'
  }
  return typeMap[type] || type
}

// 权限类型颜色映射
const getPermissionTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'menu': 'primary',
    'button': 'success',
    'data': 'warning',
    'api': 'danger'
  }
  return colorMap[type] || ''
}

// 搜索处理
const handleSearch = () => {
  ElMessage.info('搜索功能开发中...')
}

// 重置搜索
const resetSearch = () => {
  searchForm.permissionName = ''
  searchForm.type = ''
  searchForm.status = ''
}

// 刷新权限
const handleRefresh = () => {
  ElMessage.success('权限数据已刷新')
}

// 新建权限
const handleCreate = () => {
  isEdit.value = false
  Object.keys(permissionForm).forEach(key => {
    if (key === 'status') {
      permissionForm[key] = 'active'
    } else if (key === 'type') {
      permissionForm[key] = 'menu'
    } else if (key === 'sort') {
      permissionForm[key] = 0
    } else {
      permissionForm[key] = ''
    }
  })
  permissionForm.id = null
  permissionForm.parentId = null
  dialogVisible.value = true
}

// 添加子权限
const handleAddChild = (parent: any) => {
  isEdit.value = false
  Object.keys(permissionForm).forEach(key => {
    if (key === 'status') {
      permissionForm[key] = 'active'
    } else if (key === 'type') {
      permissionForm[key] = 'button'
    } else if (key === 'sort') {
      permissionForm[key] = 0
    } else {
      permissionForm[key] = ''
    }
  })
  permissionForm.id = null
  permissionForm.parentId = parent.id
  dialogVisible.value = true
}

// 编辑权限
const handleEdit = (data: any) => {
  isEdit.value = true
  Object.keys(permissionForm).forEach(key => {
    permissionForm[key] = data[key] || ''
  })
  dialogVisible.value = true
}

// 删除权限
const handleDelete = (data: any) => {
  ElMessageBox.confirm(`确认删除权限：${data.name}？此操作不可恢复！`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'danger'
  }).then(() => {
    ElMessage.success('删除成功')
  }).catch(() => {})
}

// 提交表单
const submitForm = () => {
  permissionFormRef.value.validate((valid: boolean) => {
    if (!valid) return
    
    if (isEdit.value) {
      ElMessage.success('权限信息已更新')
    } else {
      ElMessage.success('权限添加成功')
    }
    
    dialogVisible.value = false
  })
}
</script>

<style scoped>
.permissions-container {
  width: 100%;
}

.filter-card {
  margin-bottom: 16px;
  background-color: var(--bg-primary);
}

.permission-stats {
  margin-bottom: 16px;
}

.stat-card {
  margin-bottom: 16px;
  transition: all 0.3s ease;
  background: var(--bg-primary);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.all-icon {
  background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
}

.menu-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
}

.button-icon {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
}

.api-icon {
  background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.tree-card {
  margin-bottom: 16px;
  background-color: var(--bg-primary);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: var(--text-primary);
}

.header-icon {
  margin-right: 8px;
  color: var(--primary-color);
}

.count-tag {
  margin-left: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.permission-tree {
  margin-top: 16px;
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8px 0;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.node-icon {
  font-size: 16px;
}

.node-label {
  font-weight: 500;
  color: var(--text-primary);
}

.node-type {
  margin-left: 8px;
}

.node-status {
  margin-left: 8px;
}

.node-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .search-form {
    flex-wrap: wrap;
  }

  .el-form--inline .el-form-item {
    margin-right: 10px;
  }
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }

  .tree-node {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .node-actions {
    opacity: 1;
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 576px) {
  .header-actions .el-button {
    flex: 1;
  }

  .node-content {
    flex-wrap: wrap;
  }
}
</style>
