<template>
  <el-card class="stat-card" :class="cardClass" @click="handleClick">
    <div class="stat-content">
      <!-- 图标区域 -->
      <div class="stat-icon" :class="iconClass" :style="iconStyle">
        <el-icon v-if="icon" :size="iconSize">
          <component :is="icon" />
        </el-icon>
        <span v-else-if="iconText" class="icon-text">{{ iconText }}</span>
      </div>
      
      <!-- 内容区域 -->
      <div class="stat-info">
        <div class="stat-header">
          <div class="stat-number" :class="{ 'counting': isAnimating }">
            {{ displayValue }}
          </div>
          <div v-if="trend" class="stat-trend" :class="trendClass">
            <el-icon class="trend-icon">
              <component :is="trendIcon" />
            </el-icon>
            <span class="trend-text">{{ trendText }}</span>
          </div>
        </div>
        
        <div class="stat-label">{{ label }}</div>
        
        <div v-if="description" class="stat-description">
          {{ description }}
        </div>
        
        <!-- 进度条 -->
        <div v-if="showProgress" class="stat-progress">
          <el-progress 
            :percentage="progressPercentage" 
            :stroke-width="4"
            :show-text="false"
            :color="progressColor"
          />
          <span class="progress-text">{{ progressText }}</span>
        </div>
        
        <!-- 额外信息 -->
        <div v-if="extra" class="stat-extra">
          <span class="extra-label">{{ extra.label }}</span>
          <span class="extra-value">{{ extra.value }}</span>
        </div>
      </div>
    </div>
    
    <!-- 迷你图表 -->
    <div v-if="chartData" class="stat-chart">
      <BaseChart
        :data="chartData"
        :options="chartOptions"
        :height="chartHeight"
        :loading="chartLoading"
        :show-toolbar="false"
        :show-legend="false"
      />
    </div>
    
    <!-- 操作按钮 -->
    <div v-if="actions && actions.length > 0" class="stat-actions">
      <el-button
        v-for="action in actions"
        :key="action.key"
        :type="action.type || 'text'"
        :size="action.size || 'small'"
        :icon="action.icon"
        @click.stop="handleAction(action)"
      >
        {{ action.label }}
      </el-button>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { TrendCharts, ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'
import BaseChart from './BaseChart.vue'

export interface StatAction {
  key: string
  label: string
  type?: string
  size?: string
  icon?: any
  handler: () => void
}

export interface StatTrend {
  value: number
  type: 'up' | 'down' | 'flat'
  text?: string
}

export interface StatExtra {
  label: string
  value: string | number
}

interface Props {
  value: number | string
  label: string
  description?: string
  icon?: any
  iconText?: string
  iconSize?: number
  iconColor?: string
  iconBackground?: string
  trend?: StatTrend
  extra?: StatExtra
  showProgress?: boolean
  progressValue?: number
  progressMax?: number
  progressColor?: string
  progressText?: string
  chartData?: any
  chartOptions?: any
  chartHeight?: string
  chartLoading?: boolean
  actions?: StatAction[]
  clickable?: boolean
  loading?: boolean
  animated?: boolean
  size?: 'small' | 'medium' | 'large'
  variant?: 'default' | 'gradient' | 'outlined' | 'minimal'
}

const props = withDefaults(defineProps<Props>(), {
  iconSize: 24,
  chartHeight: '60px',
  clickable: false,
  loading: false,
  animated: true,
  size: 'medium',
  variant: 'default'
})

const emit = defineEmits<{
  click: []
  action: [action: StatAction]
}>()

// 响应式数据
const displayValue = ref<string | number>(0)
const isAnimating = ref(false)

// 计算属性
const cardClass = computed(() => ({
  [`stat-card--${props.size}`]: true,
  [`stat-card--${props.variant}`]: true,
  'stat-card--clickable': props.clickable,
  'stat-card--loading': props.loading
}))

const iconClass = computed(() => ({
  'stat-icon--gradient': props.variant === 'gradient'
}))

const iconStyle = computed(() => ({
  color: props.iconColor,
  background: props.iconBackground
}))

const trendClass = computed(() => {
  if (!props.trend) return {}
  
  return {
    'trend--up': props.trend.type === 'up',
    'trend--down': props.trend.type === 'down',
    'trend--flat': props.trend.type === 'flat'
  }
})

const trendIcon = computed(() => {
  if (!props.trend) return TrendCharts
  
  switch (props.trend.type) {
    case 'up':
      return ArrowUp
    case 'down':
      return ArrowDown
    case 'flat':
      return Minus
    default:
      return TrendCharts
  }
})

const trendText = computed(() => {
  if (!props.trend) return ''
  
  if (props.trend.text) {
    return props.trend.text
  }
  
  const sign = props.trend.type === 'up' ? '+' : props.trend.type === 'down' ? '-' : ''
  return `${sign}${Math.abs(props.trend.value)}%`
})

const progressPercentage = computed(() => {
  if (!props.showProgress || !props.progressValue || !props.progressMax) return 0
  return Math.min((props.progressValue / props.progressMax) * 100, 100)
})

// 方法
const animateValue = (target: number) => {
  if (!props.animated || typeof target !== 'number') {
    displayValue.value = target
    return
  }

  isAnimating.value = true
  const start = 0
  const duration = 2000
  const startTime = Date.now()

  const animate = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.min(elapsed / duration, 1)
    
    // 缓动函数
    const easeOutQuart = 1 - Math.pow(1 - progress, 4)
    const current = Math.floor(start + (target - start) * easeOutQuart)
    
    displayValue.value = current
    
    if (progress < 1) {
      requestAnimationFrame(animate)
    } else {
      isAnimating.value = false
    }
  }
  
  requestAnimationFrame(animate)
}

const formatValue = (value: number | string) => {
  if (typeof value === 'number') {
    if (value >= 1000000) {
      return (value / 1000000).toFixed(1) + 'M'
    } else if (value >= 1000) {
      return (value / 1000).toFixed(1) + 'K'
    }
    return value.toLocaleString()
  }
  return value
}

const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}

const handleAction = (action: StatAction) => {
  action.handler()
  emit('action', action)
}

// 监听器
watch(() => props.value, (newValue) => {
  if (typeof newValue === 'number') {
    animateValue(newValue)
  } else {
    displayValue.value = newValue
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  if (typeof props.value === 'number') {
    animateValue(props.value)
  } else {
    displayValue.value = props.value
  }
})
</script>

<style scoped>
.stat-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

.stat-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.stat-card--clickable {
  cursor: pointer;
}

.stat-card--clickable:active {
  transform: translateY(0);
}

.stat-card--loading {
  opacity: 0.6;
  pointer-events: none;
}

/* 尺寸变体 */
.stat-card--small .stat-content {
  padding: 16px;
}

.stat-card--medium .stat-content {
  padding: 20px;
}

.stat-card--large .stat-content {
  padding: 24px;
}

/* 样式变体 */
.stat-card--gradient {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  border: none;
}

.stat-card--outlined {
  border: 2px solid var(--primary-color);
  background: transparent;
}

.stat-card--minimal {
  border: none;
  box-shadow: none;
  background: transparent;
}

.stat-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  color: var(--primary-color);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.stat-icon--gradient {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
}

.stat-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.stat-card:hover .stat-icon::before {
  left: 100%;
}

.icon-text {
  font-size: 18px;
  font-weight: 700;
}

.stat-info {
  flex: 1;
  min-width: 0;
}

.stat-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 8px;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
  transition: all 0.3s ease;
}

.stat-number.counting {
  color: var(--primary-color);
  transform: scale(1.05);
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
}

.trend--up {
  color: var(--success-color);
  background: rgba(16, 185, 129, 0.1);
}

.trend--down {
  color: var(--danger-color);
  background: rgba(239, 68, 68, 0.1);
}

.trend--flat {
  color: var(--text-secondary);
  background: var(--bg-tertiary);
}

.trend-icon {
  font-size: 10px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.stat-description {
  font-size: 12px;
  color: var(--text-tertiary);
  margin-bottom: 8px;
}

.stat-progress {
  margin-top: 12px;
}

.progress-text {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 4px;
  display: block;
}

.stat-extra {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid var(--border-light);
}

.extra-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.extra-value {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary);
}

.stat-chart {
  margin-top: 16px;
  border-top: 1px solid var(--border-light);
  padding-top: 16px;
}

.stat-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stat-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stat-header {
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }
  
  .stat-number {
    font-size: 24px;
  }
  
  .stat-actions {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .stat-card--small .stat-content,
  .stat-card--medium .stat-content,
  .stat-card--large .stat-content {
    padding: 16px;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .stat-icon {
    width: 48px;
    height: 48px;
  }
}
</style>
