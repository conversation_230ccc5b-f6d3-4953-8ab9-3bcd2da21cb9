FROM python:3.8-slim

WORKDIR /app

# Configure pip to use Alibaba Cloud mirror for faster downloads
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set global.trusted-host mirrors.aliyun.com

# Copy requirements file first for better Docker layer caching
COPY requirements.txt .

# Upgrade pip
RUN pip install --upgrade pip

# Install dependencies using official PyPI for better reliability
RUN pip config set global.index-url https://pypi.org/simple/ && \
    pip config set global.trusted-host pypi.org && \
    pip install --timeout 100 --no-cache-dir -r requirements.txt && \
    pip cache purge

# Copy application code (this should be last for optimal caching)
COPY main.py .

# Expose the application port
EXPOSE 8000

# Run the application
CMD ["python", "-u", "main.py"]
