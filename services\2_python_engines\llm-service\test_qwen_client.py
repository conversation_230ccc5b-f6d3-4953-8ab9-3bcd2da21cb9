#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import requests

"""
测试脚本：用于在本地Windows环境测试QwenClient类
使用方法：
1. 设置环境变量DASHSCOPE_API_KEY为您的通义千问API密钥
2. 运行此脚本: python test_qwen_client.py
"""

# 通义千问客户端实现 - 使用纯requests库
class QwenClient:
    def __init__(self, api_key, base_url):
        self.api_key = api_key
        self.base_url = base_url
        # 实现兼容OpenAI库的结构
        self.chat = type('ChatModule', (), {
            'completions': type('CompletionsModule', (), {
                'create': self.create_completion
            })
        })

    def create_completion(self, model, messages, stream=False):
        """
        使用纯requests库实现通义千问API的调用
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        data = {
            "model": model,
            "messages": messages,
            "stream": stream
        }
        
        endpoint = f"{self.base_url}/chat/completions"
        print(f"调用API端点: {endpoint}")
        print(f"请求头: {headers}")
        print(f"请求参数: {json.dumps(data, ensure_ascii=False)}")
        
        try:
            response = requests.post(
                endpoint,
                headers=headers,
                json=data,
                stream=stream
            )
            
            if response.status_code != 200:
                print(f"API请求失败，状态码: {response.status_code}")
                print(f"错误信息: {response.text}")
                return None
            
            if not stream:
                return response.json()
            else:
                # 返回一个迭代器，用于处理流式响应
                class StreamResponse:
                    def __init__(self, response):
                        self.response = response
                
                    def __iter__(self):
                        return self
                    
                    def __next__(self):
                        for line in self.response.iter_lines():
                            if line:
                                line = line.decode('utf-8').strip()
                                print(f"收到数据: {line}")
                                if line.startswith('data:'):
                                    data_str = line.replace('data:', '').strip()
                                    if data_str == '[DONE]':
                                        raise StopIteration
                                    try:
                                        data = json.loads(data_str)
                                        choice = type('Choice', (), {
                                            'delta': type('Delta', (), {
                                                'content': data.get('choices', [{}])[0].get('delta', {}).get('content', '')
                                            })
                                        })
                                        return type('Chunk', (), {'choices': [choice]})
                                    except json.JSONDecodeError as e:
                                        print(f"解析JSON失败: {e}, 原始数据: {data_str}")
                                        continue
                        raise StopIteration
                
                return StreamResponse(response)
                
        except Exception as e:
            print(f"API调用异常: {str(e)}")
            return None


def test_qwen_api():
    # 从环境变量获取API密钥
    api_key = os.environ.get("DASHSCOPE_API_KEY")
    if not api_key:
        print("错误：需要设置DASHSCOPE_API_KEY环境变量")
        return
    
    # 初始化客户端
    qwen_client = QwenClient(
        api_key=api_key,
        base_url="https://dashscope.aliyuncs.com/compatible/v1"
    )
    
    # 测试消息
    messages = [
        {"role": "user", "content": "你好，请介绍一下自己"}
    ]
    
    # 1. 测试非流式响应
    print("===== 测试普通响应 =====")
    response = qwen_client.chat.completions.create(
        model="qwen-plus",
        messages=messages,
        stream=False
    )
    
    if response:
        print("API响应成功:")
        print(json.dumps(response, ensure_ascii=False, indent=2))
    else:
        print("API响应失败")
    
    # 2. 测试流式响应
    print("\n===== 测试流式响应 =====")
    stream_response = qwen_client.chat.completions.create(
        model="qwen-plus",
        messages=messages,
        stream=True
    )
    
    if stream_response:
        print("开始接收流式响应...")
        full_text = ""
        for chunk in stream_response:
            if hasattr(chunk, 'choices') and chunk.choices:
                content = chunk.choices[0].delta.content
                if content:
                    full_text += content
                    print(content, end="", flush=True)
        print("\n\n完整响应:", full_text)
    else:
        print("流式API响应失败")


if __name__ == "__main__":
    test_qwen_api() 