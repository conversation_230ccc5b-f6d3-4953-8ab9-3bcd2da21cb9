# 系统设置功能测试清单

## 🎯 测试目标
确保系统设置中的所有功能都可用且有实际效果。

## 📋 功能测试清单

### 1. 外观设置 (Appearance)

#### 主题模式
- [ ] **浅色模式**: 点击后页面应用浅色主题
- [ ] **深色模式**: 点击后页面应用深色主题  
- [ ] **跟随系统**: 根据系统主题自动切换
- [ ] **主题预览**: 卡片显示对应主题的预览效果
- [ ] **设置持久化**: 刷新页面后设置保持

#### 配色方案
- [ ] **蓝色方案**: 应用蓝色主色调
- [ ] **绿色方案**: 应用绿色主色调
- [ ] **紫色方案**: 应用紫色主色调
- [ ] **橙色方案**: 应用橙色主色调
- [ ] **红色方案**: 应用红色主色调
- [ ] **颜色预览**: 显示主色和辅助色
- [ ] **实时应用**: 点击后立即生效

#### 界面缩放
- [ ] **缩放范围**: 80%-120%可调节
- [ ] **实时预览**: 拖动滑块时界面元素缩放
- [ ] **百分比显示**: 显示当前缩放百分比
- [ ] **设置保存**: localStorage存储

#### 动画效果
- [ ] **启用动画**: 界面过渡动画生效
- [ ] **禁用动画**: 所有动画效果关闭
- [ ] **即时生效**: 切换后立即应用

### 2. 语言与地区 (Language & Region)

#### 界面语言
- [ ] **中文**: 切换到中文界面
- [ ] **英文**: 切换到英文界面
- [ ] **语言标识**: 显示国旗和语言名称
- [ ] **成功提示**: 显示"语言设置已更新"

#### 时区设置
- [ ] **北京时间**: UTC+8
- [ ] **东京时间**: UTC+9
- [ ] **纽约时间**: UTC-5
- [ ] **伦敦时间**: UTC+0
- [ ] **成功提示**: 显示时区设置确认

#### 日期格式
- [ ] **YYYY-MM-DD**: 2025-01-18
- [ ] **MM/DD/YYYY**: 01/18/2025
- [ ] **DD/MM/YYYY**: 18/01/2025
- [ ] **中文格式**: 2025年01月18日
- [ ] **实时预览**: 显示当前日期的格式化结果

#### 时间格式
- [ ] **24小时制**: 14:30
- [ ] **12小时制**: 2:30 PM
- [ ] **实时预览**: 显示当前时间的格式化结果

### 3. 通知设置 (Notifications)

#### 桌面通知
- [ ] **启用**: 请求浏览器通知权限
- [ ] **禁用**: 关闭桌面通知
- [ ] **权限检查**: 检查浏览器通知权限状态
- [ ] **设置保存**: localStorage存储

#### 声音提醒
- [ ] **启用**: 通知时播放声音
- [ ] **禁用**: 静音通知
- [ ] **音频文件**: 检查notification.mp3存在
- [ ] **音量控制**: 音量设置为30%

#### 邮件通知
- [ ] **启用**: 显示启用确认消息
- [ ] **禁用**: 显示关闭确认消息
- [ ] **本地存储**: localStorage保存状态
- [ ] **状态同步**: 页面刷新后状态保持

### 4. 账户设置 (Account)

#### 用户信息
- [ ] **用户名显示**: 显示当前登录用户名
- [ ] **输入框禁用**: 用户名输入框为只读状态

#### 密码修改
- [ ] **弹窗显示**: 点击后显示密码输入弹窗
- [ ] **密码输入**: 输入框类型为password
- [ ] **确认操作**: 显示成功或取消消息
- [ ] **安全提示**: 提示密码修改成功

#### 自动登录
- [ ] **启用**: 显示启用确认
- [ ] **禁用**: 显示关闭确认
- [ ] **设置保存**: 更新用户偏好设置

#### 会话超时
- [ ] **时间选项**: 30分钟到永不超时
- [ ] **设置保存**: localStorage存储
- [ ] **确认消息**: 显示超时时间设置确认

### 5. 系统信息 (System Info)

#### 版本信息
- [ ] **系统版本**: 显示当前版本号
- [ ] **构建时间**: 显示构建日期
- [ ] **运行环境**: development/staging/production
- [ ] **API地址**: 显示API服务地址

#### 功能特性
- [ ] **特性列表**: 显示所有功能特性
- [ ] **状态标识**: 已启用/未启用标签
- [ ] **颜色区分**: 成功/信息颜色标识

#### 系统状态
- [ ] **状态卡片**: 显示系统运行状态
- [ ] **运行时间**: 显示99.9%运行时间
- [ ] **状态图标**: 绿色成功图标

### 6. 导入导出功能

#### 导出设置
- [ ] **文件生成**: 生成JSON格式设置文件
- [ ] **文件命名**: athena-settings-YYYY-MM-DD.json
- [ ] **内容完整**: 包含所有设置项
- [ ] **成功提示**: 显示导出成功消息

#### 导入设置
- [ ] **文件选择**: 只接受.json文件
- [ ] **设置应用**: 导入后应用所有设置
- [ ] **页面刷新**: 自动刷新以应用设置
- [ ] **错误处理**: 无效文件显示错误消息

#### 重置设置
- [ ] **确认弹窗**: 显示重置确认对话框
- [ ] **全部重置**: 所有设置恢复默认值
- [ ] **成功提示**: 显示重置成功消息
- [ ] **取消操作**: 可以取消重置操作

### 7. 界面交互

#### 导航切换
- [ ] **卡片导航**: 点击卡片切换设置面板
- [ ] **活跃状态**: 当前选中卡片高亮显示
- [ ] **悬停效果**: 鼠标悬停时卡片提升效果
- [ ] **动画过渡**: 面板切换有淡入动画

#### 响应式设计
- [ ] **桌面布局**: 左右分栏布局
- [ ] **移动布局**: 垂直堆叠布局
- [ ] **卡片网格**: 移动端导航卡片网格化
- [ ] **控件适配**: 表单控件在小屏幕上适配

## 🔧 技术验证

### localStorage检查
```javascript
// 检查设置是否正确保存
console.log('Theme Mode:', localStorage.getItem('theme-mode'))
console.log('Color Scheme:', localStorage.getItem('color-scheme'))
console.log('Font Size:', localStorage.getItem('font-size'))
console.log('Scale:', localStorage.getItem('theme-scale'))
console.log('Animations:', localStorage.getItem('theme-animations'))
console.log('Email Notifications:', localStorage.getItem('email-notifications'))
console.log('Session Timeout:', localStorage.getItem('session-timeout'))
```

### CSS变量检查
```javascript
// 检查CSS变量是否正确应用
const root = document.documentElement
console.log('Primary Color:', getComputedStyle(root).getPropertyValue('--primary-color'))
console.log('Font Size:', getComputedStyle(root).getPropertyValue('--base-font-size'))
console.log('Scale Factor:', getComputedStyle(root).getPropertyValue('--scale-factor'))
```

### 类名检查
```javascript
// 检查主题类名是否正确应用
console.log('Dark Mode:', document.documentElement.classList.contains('dark'))
console.log('Compact Mode:', document.documentElement.classList.contains('compact-mode'))
console.log('Animations:', document.documentElement.classList.contains('animations-enabled'))
```

## ✅ 测试通过标准

1. **功能完整性**: 所有设置项都能正常操作
2. **效果可见性**: 设置变更有明显的视觉反馈
3. **数据持久性**: 设置在页面刷新后保持
4. **错误处理**: 异常情况有适当的错误提示
5. **用户体验**: 操作流程顺畅，反馈及时

## 🐛 常见问题排查

1. **设置不生效**: 检查applyTheme方法是否被调用
2. **样式不更新**: 检查CSS变量是否正确设置
3. **localStorage错误**: 检查浏览器存储权限
4. **通知权限**: 检查浏览器通知权限设置
5. **文件导入失败**: 检查JSON格式是否正确
