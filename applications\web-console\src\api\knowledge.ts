import axios from 'axios'
import { useAuthStore } from '@/stores/auth'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
})

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理认证错误
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      const authStore = useAuthStore()
      authStore.clearAuth()
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// 知识图谱相关接口类型定义
export interface Policy {
  id: string
  name: string
  description: string
  water_saving_factor?: number
  start_date?: string
  end_date?: string
  city?: string
}

export interface City {
  name: string
  population: number
  region: string
}

export interface PolicyCreateRequest {
  name: string
  description: string
  water_saving_factor?: number
  start_date?: string
  end_date?: string
  cityName: string
}

export interface PolicyUpdateRequest {
  id: string
  name?: string
  description?: string
  water_saving_factor?: number
  start_date?: string
  end_date?: string
}

// 知识图谱管理API
export const knowledgeGraphApi = {
  // 获取指定城市的政策列表
  async getPoliciesByCity(cityName: string): Promise<Policy[]> {
    const response = await api.get(`/policies/${cityName}`)
    return response.data
  },

  // 获取所有城市列表
  async getCities(): Promise<City[]> {
    const response = await api.get('/cities')
    return response.data
  },

  // 创建新政策
  async createPolicy(policy: PolicyCreateRequest): Promise<Policy> {
    const response = await api.post('/policies', policy)
    return response.data
  },

  // 更新政策
  async updatePolicy(policy: PolicyUpdateRequest): Promise<Policy> {
    const response = await api.put(`/policies/${policy.id}`, policy)
    return response.data
  },

  // 删除政策
  async deletePolicy(policyId: string): Promise<void> {
    await api.delete(`/policies/${policyId}`)
  },

  // 获取知识图谱结构数据（用于可视化）
  async getGraphStructure(): Promise<any> {
    const response = await api.get('/graph/structure')
    return response.data
  }
}

// RAG知识库相关接口类型定义
export interface Document {
  id: string
  filename: string
  content: string
  metadata: Record<string, any>
  created_at: string
  chunk_count?: number
}

export interface SearchRequest {
  query: string
  top_k?: number
}

export interface SearchResult {
  query: string
  results: {
    documents: string[][]
    metadatas: Record<string, any>[][]
    distances: number[][]
  }
}

export interface UploadResponse {
  message: string
  filename: string
  chunks_created: number
}

// RAG知识库管理API
export const ragKnowledgeApi = {
  // 获取已注入的文档列表
  async getDocuments(): Promise<Document[]> {
    const response = await api.get('/rag/documents')
    return response.data
  },

  // 上传文档并注入到知识库
  async uploadDocument(file: File): Promise<UploadResponse> {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await api.post('/rag/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  },

  // 删除文档
  async deleteDocument(documentId: string): Promise<void> {
    await api.delete(`/rag/documents/${documentId}`)
  },

  // RAG查询测试
  async searchKnowledge(request: SearchRequest): Promise<SearchResult> {
    const response = await api.post('/rag/search', request)
    return response.data
  },

  // 获取知识库统计信息
  async getKnowledgeStats(): Promise<any> {
    const response = await api.get('/rag/stats')
    return response.data
  }
}

export default api
