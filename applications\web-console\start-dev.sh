#!/bin/bash

# 前端开发启动脚本

set -e

echo "🚀 启动 Athena Web Console 开发环境..."

# 检查 Node.js 和 npm
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

echo "🔧 检查后端连接..."

# 检查 API Gateway 是否可访问
if curl -s http://localhost:8080/actuator/health > /dev/null; then
    echo "✅ API Gateway 连接正常"

    # 检查 admin-service 路由
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/api/admin/pods | grep -q "401\|200"; then
        echo "✅ Admin Service 路由正常"
    else
        echo "⚠️  Admin Service 路由可能有问题"
    fi
else
    echo "❌ API Gateway 不可访问"
    echo ""
    echo "🔧 请按以下步骤操作："
    echo "1. 检查服务状态: kubectl get pods -l app=api-gateway"
    echo "2. 启动端口转发: kubectl port-forward svc/api-gateway-svc 8080:8080"
    echo "3. 检查 admin-service: kubectl get pods -l app=admin-service"
    echo ""
    echo "🔄 继续启动前端服务..."
fi

echo "🌐 启动开发服务器..."
echo ""
echo "📋 访问信息："
echo "- 前端地址: http://localhost:5173"
echo "- API 代理: http://localhost:8080"
echo ""
echo "🔑 默认登录信息："
echo "- 用户名: admin"
echo "- 密码: admin123"
echo ""
echo "� 白名单功能："
echo "- 白名单验证: 已启用"
echo "- 管理页面: 集群管理 → 白名单管理"
echo "- 默认用户: admin, operator, viewer"
echo ""
echo "�🐛 调试工具："
echo "- 在浏览器控制台运行 debug-api.js 脚本"
echo "- 或复制 debug-api.js 内容到控制台"
echo "- 使用 debugAPI.runAllTests() 测试所有 API"
echo ""

# 启动开发服务器
npm run dev
