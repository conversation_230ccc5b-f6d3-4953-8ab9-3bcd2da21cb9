<template>
  <div class="advanced-search">
    <!-- 快速搜索 -->
    <div class="quick-search">
      <el-input
        v-model="quickSearchValue"
        :placeholder="placeholder"
        :prefix-icon="Search"
        clearable
        @input="handleQuickSearch"
        @keyup.enter="handleSearch"
        class="search-input"
      >
        <template #append>
          <el-button 
            :icon="Filter" 
            @click="toggleAdvanced"
            :type="showAdvanced ? 'primary' : 'default'"
          >
            {{ showAdvanced ? '收起' : '高级' }}
          </el-button>
        </template>
      </el-input>
    </div>

    <!-- 高级搜索面板 -->
    <el-collapse-transition>
      <div v-show="showAdvanced" class="advanced-panel">
        <el-card class="search-card">
          <el-form 
            ref="searchFormRef"
            :model="searchForm" 
            :inline="true" 
            class="search-form"
            label-width="auto"
          >
            <!-- 动态搜索字段 -->
            <template v-for="field in searchFields" :key="field.key">
              <!-- 文本输入 -->
              <el-form-item 
                v-if="field.type === 'input'" 
                :label="field.label"
                :prop="field.key"
              >
                <el-input
                  v-model="searchForm[field.key]"
                  :placeholder="field.placeholder"
                  clearable
                />
              </el-form-item>

              <!-- 选择器 -->
              <el-form-item 
                v-else-if="field.type === 'select'" 
                :label="field.label"
                :prop="field.key"
              >
                <el-select
                  v-model="searchForm[field.key]"
                  :placeholder="field.placeholder"
                  clearable
                  :multiple="field.multiple"
                >
                  <el-option
                    v-for="option in field.options"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <!-- 日期选择 -->
              <el-form-item 
                v-else-if="field.type === 'date'" 
                :label="field.label"
                :prop="field.key"
              >
                <el-date-picker
                  v-model="searchForm[field.key]"
                  :type="field.dateType || 'date'"
                  :placeholder="field.placeholder"
                  clearable
                  :format="field.format"
                  :value-format="field.valueFormat"
                />
              </el-form-item>

              <!-- 日期范围 -->
              <el-form-item 
                v-else-if="field.type === 'daterange'" 
                :label="field.label"
                :prop="field.key"
              >
                <el-date-picker
                  v-model="searchForm[field.key]"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  clearable
                  :format="field.format"
                  :value-format="field.valueFormat"
                />
              </el-form-item>

              <!-- 数字输入 -->
              <el-form-item 
                v-else-if="field.type === 'number'" 
                :label="field.label"
                :prop="field.key"
              >
                <el-input-number
                  v-model="searchForm[field.key]"
                  :placeholder="field.placeholder"
                  :min="field.min"
                  :max="field.max"
                  :step="field.step"
                  clearable
                />
              </el-form-item>

              <!-- 数字范围 -->
              <el-form-item 
                v-else-if="field.type === 'numberrange'" 
                :label="field.label"
                :prop="field.key"
              >
                <div class="number-range">
                  <el-input-number
                    v-model="searchForm[field.key + 'Min']"
                    placeholder="最小值"
                    :min="field.min"
                    :max="field.max"
                    :step="field.step"
                  />
                  <span class="range-separator">-</span>
                  <el-input-number
                    v-model="searchForm[field.key + 'Max']"
                    placeholder="最大值"
                    :min="field.min"
                    :max="field.max"
                    :step="field.step"
                  />
                </div>
              </el-form-item>

              <!-- 开关 -->
              <el-form-item 
                v-else-if="field.type === 'switch'" 
                :label="field.label"
                :prop="field.key"
              >
                <el-switch
                  v-model="searchForm[field.key]"
                  :active-text="field.activeText"
                  :inactive-text="field.inactiveText"
                />
              </el-form-item>
            </template>

            <!-- 操作按钮 -->
            <el-form-item class="search-actions">
              <el-button type="primary" :icon="Search" @click="handleSearch">
                搜索
              </el-button>
              <el-button :icon="Refresh" @click="handleReset">
                重置
              </el-button>
              <el-button 
                v-if="showSaveSearch"
                :icon="Star" 
                @click="handleSaveSearch"
              >
                保存搜索
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </el-collapse-transition>

    <!-- 搜索历史/保存的搜索 -->
    <div v-if="showHistory && (searchHistory.length > 0 || savedSearches.length > 0)" class="search-history">
      <el-card class="history-card">
        <template #header>
          <div class="history-header">
            <span>搜索记录</span>
            <el-button text size="small" @click="clearHistory">清空</el-button>
          </div>
        </template>
        
        <!-- 保存的搜索 -->
        <div v-if="savedSearches.length > 0" class="saved-searches">
          <div class="section-title">保存的搜索</div>
          <div class="search-tags">
            <el-tag
              v-for="search in savedSearches"
              :key="search.id"
              closable
              @click="applySavedSearch(search)"
              @close="removeSavedSearch(search.id)"
              class="search-tag saved"
            >
              {{ search.name }}
            </el-tag>
          </div>
        </div>

        <!-- 搜索历史 -->
        <div v-if="searchHistory.length > 0" class="history-searches">
          <div class="section-title">最近搜索</div>
          <div class="search-tags">
            <el-tag
              v-for="(history, index) in searchHistory.slice(0, 10)"
              :key="index"
              @click="applyHistorySearch(history)"
              class="search-tag history"
            >
              {{ history.keyword || '高级搜索' }}
            </el-tag>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, 
  Filter, 
  Refresh, 
  Star 
} from '@element-plus/icons-vue'

export interface SearchField {
  key: string
  label: string
  type: 'input' | 'select' | 'date' | 'daterange' | 'number' | 'numberrange' | 'switch'
  placeholder?: string
  options?: { label: string; value: any }[]
  multiple?: boolean
  dateType?: string
  format?: string
  valueFormat?: string
  min?: number
  max?: number
  step?: number
  activeText?: string
  inactiveText?: string
}

export interface SavedSearch {
  id: string
  name: string
  conditions: Record<string, any>
  createTime: Date
}

interface Props {
  searchFields?: SearchField[]
  placeholder?: string
  showHistory?: boolean
  showSaveSearch?: boolean
  debounceTime?: number
}

const props = withDefaults(defineProps<Props>(), {
  searchFields: () => [],
  placeholder: '请输入搜索关键词...',
  showHistory: true,
  showSaveSearch: true,
  debounceTime: 300
})

const emit = defineEmits<{
  search: [conditions: Record<string, any>]
  quickSearch: [keyword: string]
  reset: []
}>()

// 响应式数据
const searchFormRef = ref()
const showAdvanced = ref(false)
const quickSearchValue = ref('')
const searchForm = reactive<Record<string, any>>({})
const searchHistory = ref<any[]>(JSON.parse(localStorage.getItem('search-history') || '[]'))
const savedSearches = ref<SavedSearch[]>(JSON.parse(localStorage.getItem('saved-searches') || '[]'))

// 计算属性
const hasActiveFilters = computed(() => {
  return Object.values(searchForm).some(value => 
    value !== null && value !== undefined && value !== '' && 
    (Array.isArray(value) ? value.length > 0 : true)
  )
})

// 监听器
let debounceTimer: NodeJS.Timeout

watch(quickSearchValue, (newValue) => {
  clearTimeout(debounceTimer)
  debounceTimer = setTimeout(() => {
    emit('quickSearch', newValue)
  }, props.debounceTime)
})

// 方法
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

const handleQuickSearch = () => {
  // 防抖处理在watch中
}

const handleSearch = () => {
  const conditions: Record<string, any> = {}
  
  // 添加快速搜索关键词
  if (quickSearchValue.value) {
    conditions.keyword = quickSearchValue.value
  }
  
  // 添加高级搜索条件
  Object.entries(searchForm).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '' && 
        (Array.isArray(value) ? value.length > 0 : true)) {
      conditions[key] = value
    }
  })
  
  // 保存到搜索历史
  addToHistory(conditions)
  
  emit('search', conditions)
}

const handleReset = () => {
  quickSearchValue.value = ''
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = null
  })
  searchFormRef.value?.resetFields()
  emit('reset')
}

const handleSaveSearch = async () => {
  if (!hasActiveFilters.value && !quickSearchValue.value) {
    ElMessage.warning('请先设置搜索条件')
    return
  }

  try {
    const { value: name } = await ElMessageBox.prompt('请输入搜索名称', '保存搜索', {
      confirmButtonText: '保存',
      cancelButtonText: '取消',
      inputPattern: /^.{1,20}$/,
      inputErrorMessage: '搜索名称长度应为1-20个字符'
    })

    const savedSearch: SavedSearch = {
      id: Date.now().toString(),
      name,
      conditions: {
        keyword: quickSearchValue.value,
        ...searchForm
      },
      createTime: new Date()
    }

    savedSearches.value.unshift(savedSearch)
    localStorage.setItem('saved-searches', JSON.stringify(savedSearches.value))
    ElMessage.success('搜索条件已保存')
  } catch {
    // 用户取消
  }
}

const addToHistory = (conditions: Record<string, any>) => {
  const historyItem = {
    conditions,
    keyword: conditions.keyword,
    timestamp: Date.now()
  }
  
  // 避免重复
  const existingIndex = searchHistory.value.findIndex(
    item => JSON.stringify(item.conditions) === JSON.stringify(conditions)
  )
  
  if (existingIndex > -1) {
    searchHistory.value.splice(existingIndex, 1)
  }
  
  searchHistory.value.unshift(historyItem)
  
  // 限制历史记录数量
  if (searchHistory.value.length > 20) {
    searchHistory.value = searchHistory.value.slice(0, 20)
  }
  
  localStorage.setItem('search-history', JSON.stringify(searchHistory.value))
}

const applyHistorySearch = (history: any) => {
  const { conditions } = history
  
  quickSearchValue.value = conditions.keyword || ''
  
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = conditions[key] || null
  })
  
  emit('search', conditions)
}

const applySavedSearch = (search: SavedSearch) => {
  const { conditions } = search
  
  quickSearchValue.value = conditions.keyword || ''
  
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = conditions[key] || null
  })
  
  emit('search', conditions)
}

const removeSavedSearch = (id: string) => {
  const index = savedSearches.value.findIndex(search => search.id === id)
  if (index > -1) {
    savedSearches.value.splice(index, 1)
    localStorage.setItem('saved-searches', JSON.stringify(savedSearches.value))
  }
}

const clearHistory = () => {
  searchHistory.value = []
  localStorage.removeItem('search-history')
}

// 初始化搜索表单
const initSearchForm = () => {
  props.searchFields.forEach(field => {
    if (field.type === 'numberrange') {
      searchForm[field.key + 'Min'] = null
      searchForm[field.key + 'Max'] = null
    } else {
      searchForm[field.key] = field.type === 'switch' ? false : null
    }
  })
}

// 初始化
initSearchForm()
</script>

<style scoped>
.advanced-search {
  margin-bottom: 16px;
}

.quick-search {
  margin-bottom: 16px;
}

.search-input {
  width: 100%;
  max-width: 600px;
}

.advanced-panel {
  margin-top: 16px;
}

.search-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
}

.search-form {
  margin: 0;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 16px;
  margin-right: 16px;
}

.search-actions {
  margin-left: auto;
}

.number-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-separator {
  color: var(--text-secondary);
  font-weight: 500;
}

.search-history {
  margin-top: 16px;
}

.history-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.saved-searches,
.history-searches {
  margin-bottom: 16px;
}

.saved-searches:last-child,
.history-searches:last-child {
  margin-bottom: 0;
}

.search-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.search-tag {
  cursor: pointer;
  transition: all 0.2s ease;
}

.search-tag:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.search-tag.saved {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  border: none;
}

.search-tag.history {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
}

.search-tag.history:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
  }

  .search-form :deep(.el-form-item) {
    margin-right: 0;
    width: 100%;
  }

  .search-actions {
    margin-left: 0;
    text-align: center;
  }

  .number-range {
    flex-direction: column;
    align-items: stretch;
  }

  .range-separator {
    text-align: center;
    margin: 4px 0;
  }
}

@media (max-width: 576px) {
  .search-input {
    max-width: 100%;
  }

  .search-tags {
    justify-content: center;
  }

  .search-tag {
    font-size: 12px;
    padding: 4px 8px;
  }
}

/* 动画效果 */
.search-card {
  transition: all 0.3s ease;
}

.search-card:hover {
  box-shadow: var(--shadow-md);
}

.history-card {
  transition: all 0.3s ease;
}

.history-card:hover {
  box-shadow: var(--shadow-md);
}
</style>
