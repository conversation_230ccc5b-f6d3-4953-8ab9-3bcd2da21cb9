import { ElMessage, ElNotification } from 'element-plus'
import { 
  checkWhitelist, 
  logLoginAttempt, 
  updateLastLogin,
  WHITELIST_CONFIG,
  type WhitelistUser 
} from '@/config/whitelist'

export interface WhitelistValidationResult {
  allowed: boolean
  user?: WhitelistUser
  reason?: string
  code?: string
}

/**
 * 白名单验证服务
 */
export class WhitelistService {
  /**
   * 验证用户是否允许登录
   * @param username 用户名
   * @returns 验证结果
   */
  static validateUser(username: string): WhitelistValidationResult {
    // 检查用户名是否为空
    if (!username || username.trim() === '') {
      return {
        allowed: false,
        reason: '用户名不能为空',
        code: 'EMPTY_USERNAME'
      }
    }

    // 如果白名单功能未启用，允许所有用户
    if (!WHITELIST_CONFIG.enabled) {
      return {
        allowed: true,
        user: {
          username: username.trim(),
          enabled: true,
          role: 'user'
        }
      }
    }

    // 检查白名单
    const user = checkWhitelist(username.trim())
    
    if (!user) {
      logLoginAttempt(username, false)
      return {
        allowed: false,
        reason: `用户 "${username}" 不在系统白名单中，请联系管理员`,
        code: 'NOT_IN_WHITELIST'
      }
    }

    if (!user.enabled) {
      logLoginAttempt(username, false)
      return {
        allowed: false,
        reason: `用户 "${username}" 已被禁用，请联系管理员`,
        code: 'USER_DISABLED'
      }
    }

    return {
      allowed: true,
      user
    }
  }

  /**
   * 处理登录前的白名单验证
   * @param username 用户名
   * @returns 是否允许继续登录
   */
  static async preLoginValidation(username: string): Promise<boolean> {
    const result = this.validateUser(username)
    
    if (!result.allowed) {
      // 显示错误消息
      ElMessage.error({
        message: result.reason || '登录验证失败',
        duration: 5000,
        showClose: true
      })

      // 如果是白名单相关错误，显示更详细的通知
      if (result.code === 'NOT_IN_WHITELIST' || result.code === 'USER_DISABLED') {
        ElNotification.warning({
          title: '访问受限',
          message: `${result.reason}\n\n如需访问权限，请联系系统管理员添加您的账号到白名单中。`,
          duration: 8000,
          position: 'top-right'
        })
      }

      return false
    }

    return true
  }

  /**
   * 处理登录成功后的操作
   * @param username 用户名
   */
  static handleLoginSuccess(username: string) {
    const user = checkWhitelist(username)
    if (user) {
      // 记录成功登录
      logLoginAttempt(username, true)
      
      // 更新最后登录时间
      updateLastLogin(username)
      
      // 显示欢迎消息
      ElMessage.success({
        message: `欢迎回来，${user.displayName || username}！`,
        duration: 3000
      })

      // 存储用户信息到 localStorage
      const userInfo = {
        username: user.username,
        displayName: user.displayName,
        role: user.role,
        department: user.department,
        email: user.email,
        loginTime: new Date().toISOString()
      }
      localStorage.setItem('user_info', JSON.stringify(userInfo))
    }
  }

  /**
   * 获取当前用户信息
   * @returns 用户信息或 null
   */
  static getCurrentUserInfo(): WhitelistUser | null {
    try {
      const userInfo = localStorage.getItem('user_info')
      return userInfo ? JSON.parse(userInfo) : null
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }

  /**
   * 检查当前用户权限
   * @param requiredRole 需要的角色
   * @returns 是否有权限
   */
  static hasPermission(requiredRole: string): boolean {
    const userInfo = this.getCurrentUserInfo()
    if (!userInfo) return false

    const roleHierarchy: Record<string, number> = {
      'guest': 0,
      'viewer': 1,
      'user': 2,
      'operator': 3,
      'admin': 4
    }

    const userLevel = roleHierarchy[userInfo.role || 'guest'] || 0
    const requiredLevel = roleHierarchy[requiredRole] || 0

    return userLevel >= requiredLevel
  }

  /**
   * 清除用户信息
   */
  static clearUserInfo() {
    localStorage.removeItem('user_info')
  }

  /**
   * 获取白名单统计信息
   * @returns 统计信息
   */
  static getWhitelistStats() {
    const attempts = JSON.parse(localStorage.getItem('login_attempts') || '[]')
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    
    const todayAttempts = attempts.filter((attempt: any) => 
      new Date(attempt.timestamp) >= today
    )
    
    const successfulToday = todayAttempts.filter((attempt: any) => attempt.success)
    const failedToday = todayAttempts.filter((attempt: any) => !attempt.success)
    
    return {
      totalAttempts: attempts.length,
      todayAttempts: todayAttempts.length,
      successfulToday: successfulToday.length,
      failedToday: failedToday.length,
      lastAttempt: attempts.length > 0 ? attempts[attempts.length - 1] : null
    }
  }

  /**
   * 导出白名单配置（用于管理）
   * @returns 配置信息
   */
  static exportConfig() {
    return {
      enabled: WHITELIST_CONFIG.enabled,
      caseInsensitive: WHITELIST_CONFIG.caseInsensitive,
      logAttempts: WHITELIST_CONFIG.logAttempts,
      stats: this.getWhitelistStats()
    }
  }
}

export default WhitelistService
