import pika
import sys
import os
import time
import signal
import json # 1. 导入 json 库
import mlflow # 导入 MLflow 库
import tempfile
import datetime
import requests  # 导入requests库用于HTTP请求
import torch  # 导入PyTorch
from model_loader import load_model_from_mlflow  # 导入模型加载函数
import traceback # 导入 traceback 模块

# 设置MLflow Tracking Server的URI
MLFLOW_TRACKING_URI = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-svc:5000')
mlflow.set_tracking_uri(MLFLOW_TRACKING_URI)

# 从环境变量读取模型信息
MODEL_NAME = os.getenv('MODEL_NAME', 'HydroPINN')
MODEL_VERSION = os.getenv('MODEL_VERSION', '1')

# 流域参数设置
BASIN_AREA = float(os.getenv('BASIN_AREA', '5000000'))  # 默认流域面积为5平方公里（5,000,000平方米）

# Java后端服务地址
TASK_SERVICE_URL = os.getenv('TASK_SERVICE_URL', 'http://taskservice-svc:8080')

# 设置MinIO认证信息
os.environ['AWS_ACCESS_KEY_ID'] = os.getenv('AWS_ACCESS_KEY_ID', 'minioadmin')
os.environ['AWS_SECRET_ACCESS_KEY'] = os.getenv('AWS_SECRET_ACCESS_KEY', 'minioadmin')

# 默认的降雨量和温度值，当没有提供时使用
DEFAULT_RAINFALL = float(os.getenv('DEFAULT_RAINFALL', '2.5'))  # 默认日降雨量2.5毫米（月均75毫米）
DEFAULT_TEMPERATURE = float(os.getenv('DEFAULT_TEMPERATURE', '22.0'))  # 默认温度22摄氏度

class GracefulKiller:
    """
    这个类用于捕获系统的终止信号 (SIGINT, SIGTERM)，
    以便让程序能够优雅地关闭，而不是被强制杀死。
    这对于在 Kubernetes 或 Docker 中运行至关重要。
    """
    kill_now = False
    def __init__(self):
        signal.signal(signal.SIGINT, self.exit_gracefully)
        signal.signal(signal.SIGTERM, self.exit_gracefully)

    def exit_gracefully(self, signum, frame):
        print(f"--- Received signal {signum}, shutting down gracefully. ---")
        self.kill_now = True

class PINNEngine:
    """
    PINN引擎类，处理RabbitMQ消息，执行模型推理，并管理消息流
    """
    def __init__(self, rabbitmq_host='rabbitmq-svc'):
        # 保存RabbitMQ连接信息
        self.rabbitmq_host = rabbitmq_host
        self.connection = None
        self.channel = None
        
        # 加载PINN模型
        self.pinn_model = self.load_model()
        
        # 任务状态跟踪
        self.active_tasks = {}  # 用于跟踪活跃的任务 {task_id: task_info}
    
    def load_model(self):
        """加载PINN模型"""
        print("--- 正在加载HydroPINN模型 ---")
        try:
            # 从MLflow加载模型，使用环境变量中指定的名称和版本
            model = load_model_from_mlflow(model_name=MODEL_NAME, model_version=MODEL_VERSION, basin_area=BASIN_AREA)
            print(f"--- 成功加载模型 {MODEL_NAME} 版本 {MODEL_VERSION} ---")
            return model
        except Exception as e:
            print(f"--- 加载模型失败: {e} ---")
            return None
    
    def connect(self):
        """建立到RabbitMQ的连接"""
        print(f"[*] Trying to connect to RabbitMQ at {self.rabbitmq_host}...")
        self.connection = pika.BlockingConnection(
            pika.ConnectionParameters(
                host=self.rabbitmq_host,
                heartbeat=600,
                blocked_connection_timeout=300
            )
        )
        self.channel = self.connection.channel()
        
        # 声明所需的队列
        self.channel.queue_declare(queue='agent_actions', durable=True)
        self.channel.queue_declare(queue='environment_updates', durable=True)
        self.channel.queue_declare(queue='simulation_results', durable=True)
        
        print('[*] Connection successful. Waiting for messages.')
    
    def handle_agent_action(self, ch, method, properties, body):
        """处理从agent_actions队列接收到的消息"""
        print("\n" + "="*50)
        print(f"[*] [PINN_CALLBACK_START] 收到一条新消息！")
        print("="*50)

        try:
            # 步骤 1: 解析消息
            print("[PINN] 步骤 1: 正在尝试用 json.loads() 解析消息体...")
            message_str = body.decode('utf-8')
            message = json.loads(message_str)
            print(f"[PINN] >> 消息解析成功: {message}")
            
            # 步骤 2: 提取关键 ID
            task_id = message.get('taskId', 'N/A')
            mlflow_run_id = message.get('mlflowRunId')
            net_water_withdrawal = message.get('netWaterWithdrawal') # 保持现有参数提取

            print(f"[PINN] >> 提取到 Task ID: {task_id}, MLflow Run ID: {mlflow_run_id}")

            if not mlflow_run_id or not task_id or task_id == 'N/A':
                print("[PINN][ERROR] 消息中缺少 'mlflowRunId' 或 'taskId'，无法处理。")
                print("="*50 + "\n")
                return
            
            # 检查任务是否已存在（多轮交互）
            if task_id in self.active_tasks:
                # 更新现有任务的取水量
                task = self.active_tasks[task_id]
                task['net_water_withdrawal'] = net_water_withdrawal
                cycle_count = task.get('cycle_count', 0) + 1
                task['cycle_count'] = cycle_count
                print(f"[PINN] 多轮交互：更新任务 {task_id}，循环 {cycle_count}")
            else:
                # 首次交互，创建新任务
                self.active_tasks[task_id] = {
                    'task_id': task_id,
                    'mlflow_run_id': mlflow_run_id,
                    'net_water_withdrawal': net_water_withdrawal,
                    'start_time': time.time(),
                    'cycle_count': 0
                }
                print(f"[PINN] 首次交互：创建新任务 {task_id}")
            
            # 步骤 5: 模型推理
            print("[PINN] 步骤 5: 正在进行 PINN 模型推理...")
            self.predict_water_level(task_id)
            print("[PINN] >> 模型推理完成。")
            
        except json.JSONDecodeError as e:
            print("\n" + "!"*50)
            print(f"[PINN][FATAL_ERROR] Failed to decode JSON message: {e}")
            print("--- TRACEBACK ---")
            traceback.print_exc()
            print("!"*50 + "\n")
        except Exception as e:
            print("\n" + "!"*50)
            print(f"[PINN][FATAL_ERROR] An unexpected error occurred while processing message: {e}")
            print("--- TRACEBACK ---")
            traceback.print_exc()
            print("!"*50 + "\n")
        finally:
            # 无论成功还是失败，都必须确认消息，避免队列阻塞
            # 注意: auto_ack=True 已设置，无需显式 ch.basic_ack
            print("[PINN] 正在等待下一条消息。")
            print("="*50 + "\n")
    
    def predict_water_level(self, task_id):
        """使用PINN模型预测水位"""
        task = self.active_tasks.get(task_id)
        if not task:
            print(f"[PINN][ERROR] 任务 {task_id} 未找到，无法预测水位。")
            return
        
        try:
            # 获取任务信息
            mlflow_run_id = task['mlflow_run_id']
            net_water_withdrawal = task['net_water_withdrawal']
            
            # 步骤 3: 连接并启动 MLflow Run
            print(f"[PINN] 步骤 3: 正在尝试用 mlflow.start_run() 启动 Run...")
            with mlflow.start_run(run_id=mlflow_run_id, run_name=f"pinn_simulation_for_task_{task_id}") as run:
                print(f"[PINN] >> MLflow Run 成功启动/恢复！Run ID: {run.info.run_id}")
                
                # 检查是否是多轮交互
                cycle_count = task.get('cycle_count', 0)
                if 'previous_water_level' in task:
                    base_water_level = task['previous_water_level']
                    print(f"[PINN] 多轮交互：使用上一轮水位 {base_water_level} 作为基准")
                else:
                    base_water_level = 5.0  # 默认水库水位5米
                    print(f"[PINN] 首次交互：使用默认基准水位 {base_water_level}")
                
                # 步骤 4: 记录参数
                print("[PINN] 步骤 4: 正在准备并记录参数到 MLflow...")
                # 为每个循环的参数添加前缀，避免参数重复
                cycle_prefix = f"cycle_{cycle_count}_" if cycle_count > 0 else ""
                
                params_to_log = {
                    f"{cycle_prefix}net_water_withdrawal": net_water_withdrawal,
                    f"{cycle_prefix}pinn_processed_at": datetime.datetime.now().isoformat(),
                    f"{cycle_prefix}basin_area": BASIN_AREA,
                    f"{cycle_prefix}rainfall": DEFAULT_RAINFALL,
                    f"{cycle_prefix}temperature": DEFAULT_TEMPERATURE
                }
                if cycle_count > 0:
                    params_to_log[f"{cycle_prefix}base_water_level"] = base_water_level
                
                mlflow.log_params(params_to_log)
                print(f"[PINN] >> 参数已记录: {params_to_log}")
                
                # 计算并记录水位下降量
                water_level_decrease = net_water_withdrawal / BASIN_AREA
                mlflow.log_metric(f"{cycle_prefix}water_level_decrease", water_level_decrease)
                
                print(f"[PINN] 使用PINN模型计算水位...")
                print(f"[PINN] 输入参数: rainfall={DEFAULT_RAINFALL}, temperature={DEFAULT_TEMPERATURE}, net_water_withdrawal={net_water_withdrawal}")
                print(f"[PINN] 流域面积: {BASIN_AREA} 平方米, 预计水位下降: {water_level_decrease:.6f} 米")
                
                water_level = None # 初始化水位
                # 使用模型预测水位
                if self.pinn_model is not None:
                    try:
                        # 调用增强的模型进行预测
                        water_level = self.pinn_model(DEFAULT_RAINFALL, DEFAULT_TEMPERATURE, net_water_withdrawal)
                        print(f"[PINN] >> 模型预测水位: {water_level}")
                    except Exception as e:
                        print(f"[PINN][ERROR] 模型预测出错: {e}")
                        traceback.print_exc() # 打印堆栈信息
                        # 使用简单公式作为备选
                        water_level = base_water_level - water_level_decrease
                        print(f"[PINN] 使用备选公式计算水位: {water_level}")
                else:
                    # 如果模型未加载，使用简单公式
                    water_level = base_water_level - water_level_decrease
                    print(f"[PINN] 模型未加载，使用简单公式计算水位: {water_level}")
                
                # 步骤 6: 记录指标
                print("[PINN] 步骤 6: 正在记录指标到 MLflow...")
                metrics_to_log = {f"{cycle_prefix}water_level": water_level}
                mlflow.log_metrics(metrics_to_log)
                print(f"[PINN] >> 指标已记录: {metrics_to_log}")
                
                # 保存到任务状态
                task['previous_water_level'] = water_level  # 保存当前水位作为下一轮的基准
                task['water_level'] = water_level
                task['cycle_count'] = cycle_count + 1  # 更新循环计数
                
                # 步骤 7: 发送环境更新到environment_updates队列
                print("[PINN] 步骤 7: 正在准备将环境更新发送回 RabbitMQ (environment_updates)...")
                self.send_to_environment_updates(task_id, mlflow_run_id, water_level)
                print("[PINN] >> 环境更新已发送。")
                
                # 发送最终结果到simulation_results队列
                print("[PINN] 步骤 7: 正在准备将模拟结果发送回 RabbitMQ (simulation_results)...")
                self.send_to_simulation_results(task_id, water_level)
                print("[PINN] >> 模拟结果已发送。")
                
                # 创建详细报告
                print("[PINN] 正在创建任务报告...")
                self.create_task_report(task_id)
                print("[PINN] 任务报告创建完成。")
                
            print("[PINN] MLflow Run 已成功结束。")
                
        except Exception as e:
            print("\n" + "!"*50)
            print(f"[PINN][FATAL_ERROR] 在预测水位时发生了一个未预料的严重错误: {e}")
            print("--- TRACEBACK ---")
            traceback.print_exc()
            print("!"*50 + "\n")
            # 记录失败
            if 'mlflow_run_id' in task: # 只有当 mlflow_run_id 存在时才尝试记录
                try:
                    with mlflow.start_run(run_id=task['mlflow_run_id'], nested=True) as run:
                        mlflow.log_metric("error", 1)
                        print(f"[PINN] 已在 MLflow Run {task['mlflow_run_id']} 中记录错误指标。")
                except Exception as inner_e:
                    print(f"[PINN][ERROR] 尝试记录 MLflow 错误指标时再次出错: {inner_e}")
                    traceback.print_exc()
    
    def send_to_environment_updates(self, task_id, mlflow_run_id, water_level):
        """发送环境更新到environment_updates队列"""
        try:
            print(f"     [>] 发送水位结果到RabbitMQ的environment_updates队列")
            
            # 创建消息
            message = {
                "taskId": task_id,
                "mlflowRunId": mlflow_run_id,
                "waterLevel": water_level
            }
            
            # 发送消息
            self.channel.basic_publish(
                exchange='',
                routing_key='environment_updates',
                body=json.dumps(message)
            )
            
            print(f"     [>] 成功发送水位结果到environment_updates队列")
            
            # 记录发送时间
            self.active_tasks[task_id]['env_update_sent_time'] = time.time()
            
        except Exception as e:
            print(f"     [!] 发送环境更新出错: {e}")
    
    def send_to_simulation_results(self, task_id, water_level):
        """发送最终结果到simulation_results队列"""
        try:
            print(f"     [>] 发送水位结果到RabbitMQ的simulation_results队列")
            
            # 创建消息
            message = {
                "taskId": task_id,
                "waterLevel": water_level
            }
            
            # 发送消息
            self.channel.basic_publish(
                exchange='',
                routing_key='simulation_results',
                body=json.dumps(message)
            )
            
            print(f"     [>] 成功发送水位结果到simulation_results队列")
            
            # 记录发送时间
            self.active_tasks[task_id]['results_sent_time'] = time.time()
            
        except Exception as e:
            print(f"     [!] 发送最终结果出错: {e}")
    
    def create_task_report(self, task_id):
        """为任务创建详细报告并记录到MLflow"""
        task = self.active_tasks.get(task_id)
        if not task:
            return
        
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(prefix="pinn_report_", suffix=".txt", delete=False) as temp_file:
                result_file_path = temp_file.name
                
                # 计算处理时间
                processing_time = time.time() - task['start_time']
                
                # 创建报告内容
                result_content = f"""Task ID: {task['task_id']}
Input Parameters:
- Net Water Withdrawal: {task.get('net_water_withdrawal')} units
- Rainfall: {DEFAULT_RAINFALL} mm (default)
- Temperature: {DEFAULT_TEMPERATURE} °C (default)
Output:
- Water Level: {task.get('water_level')} m
Processing Time: {processing_time:.2f} seconds
Status: {'Success' if 'water_level' in task else 'Failed'}
Timestamp: {datetime.datetime.now().isoformat()}
"""
                temp_file.write(result_content.encode('utf-8'))
            
            # 记录到MLflow
            with mlflow.start_run(run_id=task['mlflow_run_id'], nested=True):
                # 记录处理时间
                mlflow.log_metric("processing_time", processing_time)
                
                # 记录报告文件
                print(f"     [MLflow] Logging artifact: {result_file_path}")
                mlflow.log_artifact(result_file_path)
                
                # 记录成功状态
                mlflow.log_metric("success", 1)
            
            # 删除临时文件
            os.unlink(result_file_path)
            
            print(f"     [MLflow] Task report completed for task {task_id}")
            
        except Exception as e:
            print(f"     [!] Error creating task report: {e}")
    
    def start_consuming(self):
        """开始消费消息"""
        # 订阅agent_actions队列
        self.channel.basic_consume(
            queue='agent_actions',
            on_message_callback=self.handle_agent_action,
            auto_ack=True
        )
        
        print("[*] Started consuming messages. To exit press CTRL+C")
    
    def process_events(self, time_limit=1):
        """处理事件循环"""
        self.connection.process_data_events(time_limit=time_limit)
    
    def close(self):
        """关闭连接"""
        if self.connection and self.connection.is_open:
            print("--- Closing connection to RabbitMQ. ---")
            self.connection.close()

def main():
    # 在脚本启动时打印版本号，方便调试
    print("--- consumer.py script started (v0.2.0) ---")
    print(f"--- MLflow Tracking URI: {MLFLOW_TRACKING_URI} ---")
    print(f"--- 将加载模型: {MODEL_NAME} 版本: {MODEL_VERSION} ---")
    print(f"--- 流域面积: {BASIN_AREA} 平方米 ---")
    
    killer = GracefulKiller()
    # 从环境变量读取 RabbitMQ 主机名，如果不存在则使用默认值 'rabbitmq-svc'
    rabbitmq_host = os.getenv('RABBITMQ_HOST', 'rabbitmq-svc')
    
    pinn_engine = None
    
    # 使用循环来处理连接失败和重试
    while not killer.kill_now:
        try:
            # 创建并初始化PINNEngine
            pinn_engine = PINNEngine(rabbitmq_host=rabbitmq_host)
            
            # 连接到RabbitMQ
            pinn_engine.connect()
            
            # 开始消费消息
            pinn_engine.start_consuming()
            
            # 使用非阻塞的循环来处理事件，以便能响应终止信号
            while not killer.kill_now:
                # 处理网络事件，超时时间为1秒
                pinn_engine.process_events(time_limit=1)
                
        except pika.exceptions.AMQPConnectionError as e:
            # 如果连接失败，并且没有收到终止信号，则等待5秒后重试
            if killer.kill_now:
                break
            print(f" [!] Connection to RabbitMQ failed: {e}. Retrying in 5 seconds...")
            time.sleep(5)
        except Exception as e:
            # 捕获其他未知异常
            if killer.kill_now:
                break
            print(f" [!] An unexpected error occurred: {e}. Retrying in 5 seconds...")
            time.sleep(5)
        finally:
            # 关闭连接
            if pinn_engine:
                pinn_engine.close()
    
    print("--- consumer.py script ended. ---")

if __name__ == '__main__':
    main()