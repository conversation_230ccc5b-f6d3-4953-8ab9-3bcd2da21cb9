@import './base.css';

/* 主题变量 */
:root {
  /* 基础变量 */
  --base-font-size: 14px;
  --scale-factor: 1;
  --zoom-level: 100%;

  /* 动画变量 */
  --transition-duration: 0.3s;
  --transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 动画控制 */
.animations-enabled * {
  transition: all var(--transition-duration) var(--transition-timing);
}

.animations-disabled * {
  transition: none !important;
}

/* 缩放支持 */
.el-button,
.el-input,
.el-select,
.el-card {
  transform: scale(var(--scale-factor));
  transform-origin: top left;
}

/* 紧凑模式 */
.compact-mode .el-card {
  margin-bottom: 12px;
}

.compact-mode .el-form-item {
  margin-bottom: 16px;
}

.compact-mode .setting-row {
  padding: 12px 0;
}

#app {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  font-weight: normal;
  font-size: var(--base-font-size);
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    display: block;
  }

  #app {
    display: block;
    width: 100%;
    padding: 0;
  }
}
