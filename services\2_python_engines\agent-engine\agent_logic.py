import requests

class ResidentAgent:
    """
    ResidentAgent类代表一个居民区代理，用于模拟居民的用水行为。
    """
    
    def __init__(self, city_name="Rivertown"):
        """
        初始化ResidentAgent。
        
        参数:
            city_name (str): 城市名称，用于查询该城市的政策
        """
        # 定义每人每天的用水量（立方米/人/天）
        # 参考中国城市居民平均用水量约为0.2立方米/人/天(200升)
        self.daily_water_usage_per_person = 0.2  # 200升/人/天 = 0.2立方米/人/天
        # 夜间用水量约为白天的30%
        self.night_usage_factor = 0.3  # 夜间用水量是白天的30%
        # 城市名称
        self.city_name = city_name
        # 知识服务地址
        self.knowledge_service_url = f"http://knowledge-service-svc/api/policies/{city_name}"
        # 环境状态
        self.current_water_level = 5.0  # 初始水位，单位为米（水库平均水深）
        self.water_level_threshold = 4.2  # 水位预警阈值，单位为米（低于此值开始节水）
        self.water_level_critical = 3.5  # 水位严重预警阈值，单位为米（低于此值强制严格节水）
        # 环境适应参数
        self.adaptation_factor = 1.0  # 适应因子，初始为1.0（不调整）
        # 最低适应因子，即使在最严重干旱情况下，也需保证基本生活用水
        self.min_adaptation_factor = 0.6  # 最低节水60%，确保基本生活用水
        # 政策状态跟踪
        self.applied_policy_name = None
        self.policy_triggered = False
        self.trigger_condition_met = False
        
    def get_water_saving_factor(self):
        """
        从knowledge-service获取节水因子，并根据水位条件触发政策
        
        返回:
            float: 节水因子，如果请求失败则返回默认值1.0
        """
        try:
            print(f"正在查询城市 '{self.city_name}' 的政策...")
            # 发送GET请求到knowledge-service
            response = requests.get(self.knowledge_service_url, timeout=5)
            
            # 检查响应状态
            if response.status_code == 200:
                policies = response.json()
                print(f"获取到 {len(policies)} 条政策")
                
                # 查找包含water_saving_factor的政策
                for policy in policies:
                    if 'waterSavingFactor' in policy:
                        policy_name = policy.get('name', 'Unknown')
                        water_saving_factor = float(policy['waterSavingFactor'])
                        
                        # 检查是否应该触发这个政策
                        should_trigger = self.check_policy_trigger_conditions(policy)
                        
                        if should_trigger:
                            self.policy_triggered = True
                            self.applied_policy_name = policy_name
                            self.trigger_condition_met = True
                            print(f"✅ 触发政策 '{policy_name}'，节水因子: {water_saving_factor}")
                            return water_saving_factor
                        else:
                            print(f"⚠️ 政策 '{policy_name}' 存在但未触发（条件不满足）")
                
                # 如果没有找到适用的政策或条件不满足
                self.policy_triggered = False
                self.applied_policy_name = None
                self.trigger_condition_met = False
                print("ℹ️ 未触发任何节水政策，使用默认节水因子 1.0")
                return 1.0
            else:
                print(f"获取政策失败，HTTP状态码: {response.status_code}")
                return 1.0
                
        except Exception as e:
            print(f"获取节水因子时出错: {e}")
            return 1.0
    
    def check_policy_trigger_conditions(self, policy):
        """
        检查政策触发条件
        
        参数:
            policy (dict): 政策信息
            
        返回:
            bool: 是否应该触发政策
        """
        # 检查政策类型
        policy_type = policy.get('type', '').lower()
        policy_name = policy.get('name', '').lower()
        
        # 对于 DroughtAlert 政策，检查水位条件
        if 'drought' in policy_name.lower() or 'droughtalert' in policy_name.lower():
            # 检查水位是否低于预警阈值
            if self.current_water_level < self.water_level_threshold:
                print(f"水位 {self.current_water_level:.2f}m 低于预警阈值 {self.water_level_threshold}m，满足 DroughtAlert 触发条件")
                return True
            else:
                print(f"水位 {self.current_water_level:.2f}m 高于预警阈值 {self.water_level_threshold}m，不满足 DroughtAlert 触发条件")
                return False
        
        # 对于其他类型的政策，可以根据需要添加更多条件
        # 例如：检查降雨量、温度等环境条件
        
        # 默认情况下，如果政策存在就触发（保守策略）
        print(f"政策 '{policy.get('name', 'Unknown')}' 无特定触发条件，默认触发")
        return True
    
    def update_environment_state(self, water_level):
        """
        更新环境状态并调整适应因子
        
        参数:
            water_level (float): 最新的水位数据，单位为米
        """
        previous_water_level = self.current_water_level
        self.current_water_level = water_level
        
        print(f"更新环境状态: 水位从 {previous_water_level:.2f}m 变为 {water_level:.2f}m")
        
        # 根据水位变化调整适应因子
        if water_level < self.water_level_critical:
            # 水位低于严重预警阈值，实施最严格节水措施
            severity = (self.water_level_critical - water_level) / self.water_level_critical
            # 适应因子在最低值和0.8之间调整
            self.adaptation_factor = max(self.min_adaptation_factor, 0.8 - severity * 0.2)
            print(f"水位低于严重预警阈值 {self.water_level_critical}m，调整适应因子为 {self.adaptation_factor:.2f}，实施严格节水")
        elif water_level < self.water_level_threshold:
            # 水位低于预警阈值但高于严重预警，实施中度节水
            severity = (self.water_level_threshold - water_level) / (self.water_level_threshold - self.water_level_critical)
            # 适应因子在0.8和0.95之间调整
            self.adaptation_factor = 0.95 - severity * 0.15
            print(f"水位低于预警阈值 {self.water_level_threshold}m，调整适应因子为 {self.adaptation_factor:.2f}，实施中度节水")
        else:
            # 水位正常，逐渐恢复正常用水，每次增加5%，直到完全恢复
            self.adaptation_factor = min(1.0, self.adaptation_factor + 0.05)
            print(f"水位正常，适应因子调整为 {self.adaptation_factor:.2f}")
        
    def decide_water_usage(self, population, time_of_day):
        """
        根据人口数量和一天中的时间决定用水量。
        """
        try:
            # 决策前自动更新适应因子
            self.update_environment_state(self.current_water_level)
        except Exception as e:
            print(f"[异常] 更新适应因子时出错: {e}")
            self.adaptation_factor = 1.0

        print(f"=== 开始决策用水量 ===")
        print(f"城市: {self.city_name}")
        print(f"当前水位: {self.current_water_level}米 (预警阈值: {self.water_level_threshold}米, 严重预警: {self.water_level_critical}米)")

        # 获取节水因子
        try:
            water_saving_factor = self.get_water_saving_factor()
        except Exception as e:
            print(f"[异常] 获取节水因子时出错: {e}")
            water_saving_factor = 1.0
        print(f"应用节水因子: {water_saving_factor}")
        print(f"应用环境适应因子: {self.adaptation_factor}")

        # 记录政策状态
        print(f"政策触发状态: {self.policy_triggered}")
        print(f"应用的政策名称: {self.applied_policy_name or '无'}")
        print(f"触发条件是否满足: {self.trigger_condition_met}")

        # 计算当前时间段的人均用水量（立方米/人）
        if time_of_day == 'day':
            hourly_factor = 16/24
            per_person_usage = self.daily_water_usage_per_person * hourly_factor
        else:
            hourly_factor = 8/24
            per_person_usage = self.daily_water_usage_per_person * hourly_factor * self.night_usage_factor

        # 总净取水量
        net_withdrawal = population * per_person_usage * water_saving_factor * self.adaptation_factor

        print(f"决策依据: 人口={population}, 时段={time_of_day}, 人均用水={per_person_usage:.4f}立方米")
        print(f"应用因子: 节水因子={water_saving_factor}, 适应因子={self.adaptation_factor}")
        print(f"最终计算: {population} × {per_person_usage:.4f} × {water_saving_factor} × {self.adaptation_factor} = {net_withdrawal:.2f}立方米")
        print(f"=== 决策完成 ===")

        return net_withdrawal 