apiVersion: apps/v1
kind: Deployment
metadata:
  name: knowledge-service-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: knowledge-service
  template:
    metadata:
      labels:
        app: knowledge-service
    spec:
      containers:
      - name: knowledge-service
        image: athena/knowledge-service:0.0.8
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8080
        env:
        - name: NEO4J_URI
          value: bolt://neo4j-svc:7687
        - name: NEO4J_USERNAME
          valueFrom:
            secretKeyRef:
              name: neo4j-secret
              key: NEO4J_USERNAME
        - name: NEO4J_PASSWORD
          valueFrom:
            secretKeyRef:
              name: neo4j-secret
              key: NEO4J_PASSWORD
---
apiVersion: v1
kind: Service
metadata:
  name: knowledge-service-svc
spec:
  type: NodePort
  selector:
    app: knowledge-service
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
