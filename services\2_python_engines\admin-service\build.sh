#!/bin/bash

# 构建和部署 admin-service 脚本

set -e

# 版本号
VERSION="0.0.1"
IMAGE_NAME="athena/admin-service"
IMAGE_TAG="athena/admin-service:0.0.1"
TAR_FILE="admin-service-0.0.1.tar"

echo "🚀 开始构建 admin-service v${VERSION}..."

# 构建 Docker 镜像 (支持多平台)
echo "📦 构建 Docker 镜像..."
docker buildx build --platform linux/amd64 -t athena/admin-service:0.0.1 . --load

# 保存镜像为 tar 文件
echo "💾 保存镜像到 tar 文件..."
docker save athena/admin-service:0.0.1 -o admin-service-0.0.1.tar

# 复制镜像到 minikube
echo "📤 复制镜像到 minikube..."
minikube cp admin-service-0.0.1.tar /home/<USER>/

# 在 minikube 中加载镜像
echo "📥 在 minikube 中加载镜像..."
minikube ssh "docker load -i /home/<USER>/admin-service-0.0.1.tar"

# 清理本地 tar 文件
echo "🧹 清理本地 tar 文件..."
rm -f ${TAR_FILE}

echo "🔧 应用 Kubernetes 配置..."

# 应用 RBAC 配置
echo "👤 创建 ServiceAccount 和权限..."
kubectl apply -f k8s/rbac.yaml

# 应用 Deployment 和 Service
echo "🚀 部署服务..."
kubectl apply -f k8s/deployment.yaml

# 等待部署完成
echo "⏳ 等待部署完成..."
kubectl rollout status deployment/admin-service --timeout=300s

# 检查服务状态
echo "✅ 检查服务状态..."
kubectl get pods -l app=admin-service
kubectl get svc admin-service-svc

echo "🎉 admin-service v${VERSION} 部署完成！"

# 显示服务信息
echo ""
echo "📋 服务信息:"
echo "- Image: ${IMAGE_TAG}"
echo "- Service: admin-service-svc"
echo "- Port: 80"
echo "- Health Check: http://admin-service-svc/health"
echo ""
echo "🔍 查看日志: kubectl logs -l app=admin-service -f"
