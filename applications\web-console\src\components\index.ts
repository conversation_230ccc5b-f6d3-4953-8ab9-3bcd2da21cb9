import type { App } from 'vue'

// 通用组件
import AdvancedTable from './Common/AdvancedTable.vue'
import AdvancedSearch from './Common/AdvancedSearch.vue'

// 图表组件
import BaseChart from './Charts/BaseChart.vue'
import StatCard from './Charts/StatCard.vue'

// 布局组件
import MainLayout from './Layout/MainLayout.vue'

// 组件列表
const components = {
  AdvancedTable,
  AdvancedSearch,
  BaseChart,
  StatCard,
  MainLayout
}

// 全局注册组件
export const registerGlobalComponents = (app: App) => {
  Object.entries(components).forEach(([name, component]) => {
    app.component(name, component)
  })
}

// 按需导出
export {
  AdvancedTable,
  AdvancedSearch,
  BaseChart,
  StatCard,
  MainLayout
}

export default components
