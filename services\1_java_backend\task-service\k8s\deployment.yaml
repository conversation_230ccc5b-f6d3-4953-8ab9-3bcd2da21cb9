apiVersion: apps/v1
kind: Deployment
metadata:
  name: task-service-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: task-service
  template:
    metadata:
      labels:
        app: task-service
    spec:
      containers:
      - name: task-service
        image: athena/task-service:0.0.13
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8080
        env:
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: postgres-config
              key: POSTGRES_HOST
        - name: DB_NAME
          valueFrom:
            configMapKeyRef:
              name: postgres-config
              key: POSTGRES_DB
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_PASSWORD
---
apiVersion: v1
kind: Service
metadata:
  name: task-service-svc
spec:
  type: NodePort
  selector:
    app: task-service
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP 