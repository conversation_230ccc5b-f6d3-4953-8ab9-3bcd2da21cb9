# Agent Executor Service

Agent Executor Service 是 Athena 平台的智能代理执行服务，提供了与平台内部服务进行异步通信的工具函数，专为 LLM Function Calling 功能设计。

## 🛠️ 工具函数

### 1. `run_simulation(city_name, rainfall, temperature, population)`

**功能**: 创建新的水资源模拟任务

**参数**:
- `city_name` (str): 城市名称
- `rainfall` (float): 降雨量参数
- `temperature` (float): 温度参数  
- `population` (int): 人口数量参数

**返回**: 包含任务ID等信息的字典

**示例**:
```python
result = await run_simulation(
    city_name="北京",
    rainfall=50.5,
    temperature=25.0,
    population=2000000
)
# 返回: {"id": 123, "name": "北京水资源模拟", "status": "PENDING", ...}
```

### 2. `get_task_status(task_id)`

**功能**: 获取指定任务的状态信息

**参数**:
- `task_id` (int): 任务ID

**返回**: 包含任务详细信息的字典

**示例**:
```python
result = await get_task_status(task_id=123)
# 返回: {"id": 123, "status": "COMPLETED", "waterLevel": 85.5, ...}
```

### 3. `get_simulation_report(task_id, user_question=None)`

**功能**: 获取模拟任务的AI分析报告

**参数**:
- `task_id` (int): 任务ID
- `user_question` (str, optional): 用户的具体问题

**返回**: 完整的分析报告文本

**示例**:
```python
# 全面分析
report = await get_simulation_report(task_id=123)

# 针对特定问题的分析
report = await get_simulation_report(
    task_id=123, 
    user_question="为什么最终水位这么低？"
)
```

## 🧪 测试端点

服务提供了测试端点来验证工具函数的功能：

### POST `/test-tools/run-simulation`
```json
{
    "city_name": "上海",
    "rainfall": 45.0,
    "temperature": 28.0,
    "population": 2500000
}
```

### POST `/test-tools/get-task-status`
```json
{
    "task_id": 123
}
```

### POST `/test-tools/get-simulation-report`
```json
{
    "task_id": 123,
    "user_question": "分析一下水位变化的原因"
}
```

## 🔧 服务配置

### 环境变量
- `TASK_SERVICE_URL`: 任务服务地址 (默认: http://task-service-svc:80)
- `LLM_SERVICE_URL`: LLM服务地址 (默认: http://llm-service:80)
- `OPENAI_API_KEY`: OpenAI API密钥 (可选)

### 服务依赖
- **task-service**: 提供任务管理功能
- **llm-service**: 提供AI分析能力
- **PostgreSQL**: 任务数据存储
- **MLflow**: 实验跟踪
- **Neo4j**: 知识图谱

## 🚀 Function Calling 集成

这些工具函数专为 OpenAI Function Calling 设计，可以直接用于构建智能代理：

```python
tools = [
    {
        "type": "function",
        "function": {
            "name": "run_simulation",
            "description": "创建新的水资源模拟任务",
            "parameters": {
                "type": "object",
                "properties": {
                    "city_name": {"type": "string", "description": "城市名称"},
                    "rainfall": {"type": "number", "description": "降雨量参数"},
                    "temperature": {"type": "number", "description": "温度参数"},
                    "population": {"type": "integer", "description": "人口数量参数"}
                },
                "required": ["city_name", "rainfall", "temperature", "population"]
            }
        }
    }
    # ... 其他工具定义
]
```

## 📊 健康检查

- **GET `/health`**: 服务健康状态检查
- **GET `/`**: 服务信息和可用端点列表

## 🔍 日志记录

服务提供详细的日志记录，包括：
- 工具函数调用日志
- 服务间通信日志
- 错误和异常日志
- 性能监控日志

所有日志都使用结构化格式，便于监控和调试。

## 🤖 Agent 循环功能

### 核心特性
- **完整的 Agent 循环**: 实现思考(Think) → 决策(Decide) → 行动(Act) → 观察(Observe) 的完整循环
- **流式响应**: 实时显示 Agent 的思考和执行过程
- **智能工具选择**: 基于用户需求自动选择合适的工具
- **错误恢复**: 工具执行失败时的优雅处理

### 使用示例

#### 1. 创建模拟任务
```bash
curl -X POST "http://localhost:8000/execute-command" \
  -H "Content-Type: application/json" \
  -d '{"command": "我想为北京创建一个水资源模拟，降雨量50mm，温度25度，人口2000万"}'
```

#### 2. 查询任务状态
```bash
curl -X POST "http://localhost:8000/execute-command" \
  -H "Content-Type: application/json" \
  -d '{"command": "请查询任务ID 123 的状态"}'
```

#### 3. 获取分析报告
```bash
curl -X POST "http://localhost:8000/execute-command" \
  -H "Content-Type: application/json" \
  -d '{"command": "请分析任务123的结果，我想了解为什么水位这么低"}'
```

### Agent 循环流程

1. **思考阶段**: Agent 分析用户请求，决定需要执行什么操作
2. **决策阶段**: 检查是否需要调用工具，选择合适的工具和参数
3. **行动阶段**: 异步执行选定的工具函数
4. **观察阶段**: 分析工具执行结果，决定下一步操作
5. **循环或结束**: 根据任务完成情况继续循环或返回最终结果

## 📋 OpenAI Function Calling 工具定义

可以通过 `GET /tools-schema` 端点获取完整的工具定义：

```json
{
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "run_simulation",
        "description": "创建新的水资源模拟任务。当用户想要运行模拟、创建新任务或开始水资源分析时使用此工具。",
        "parameters": {
          "type": "object",
          "properties": {
            "city_name": {
              "type": "string",
              "description": "要模拟的城市名称，例如：北京、上海、深圳等"
            },
            "rainfall": {
              "type": "number",
              "description": "降雨量参数（毫米），影响水资源供给，通常范围在0-200之间"
            },
            "temperature": {
              "type": "number",
              "description": "温度参数（摄氏度），影响蒸发率和用水需求，通常范围在-10到45之间"
            },
            "population": {
              "type": "integer",
              "description": "城市人口数量，影响用水需求，例如：1000000表示100万人口"
            }
          },
          "required": ["city_name", "rainfall", "temperature", "population"]
        }
      }
    }
  ]
}
```

## 🔄 流式响应格式

Agent 执行过程中会返回结构化的流式响应：

```
🤖 正在处理您的请求: 我想为北京创建一个水资源模拟

💭 **第 1 轮思考中...**
🔧 **正在执行 1 个工具调用...**
   ⚡ 调用工具: run_simulation
   ✅ 工具执行成功

💭 **第 2 轮思考中...**
✅ **任务完成！**

已成功为北京创建水资源模拟任务！任务详情如下：
- 任务ID: 123
- 城市: 北京
- 状态: PENDING
- 参数: 降雨量50mm, 温度25°C, 人口2000万
```

## ⚙️ 配置说明

### LLM API 配置（二选一）
- `OPENAI_API_KEY`: OpenAI API 密钥（用于 GPT-4 Function Calling）
- `DASHSCOPE_API_KEY`: 阿里云通义千问 API 密钥（用于 Qwen Function Calling）

### 可选环境变量
- `QWEN_BASE_URL`: 通义千问 API 地址（默认: https://dashscope.aliyuncs.com/compatible-mode/v1）
- `TASK_SERVICE_URL`: 任务服务地址（默认: http://task-service-svc:80）
- `LLM_SERVICE_URL`: LLM服务地址（默认: http://llm-service:80）
- `KNOWLEDGE_SERVICE_URL`: 知识服务地址（默认: http://knowledge-service-svc:80）
- `MLFLOW_TRACKING_URI`: MLflow 服务地址（默认: http://mlflow-svc:5000）

### Agent 配置
- 最大循环次数: 5轮
- 支持的模型:
  - OpenAI: gpt-4-turbo-preview
  - 通义千问: qwen-plus
- 温度参数: 0.7
- 工具选择: auto

### Kubernetes 部署
- **Service 类型**: NodePort（支持外部访问）
- **端口映射**: 80 → 8000
- **资源限制**: 256Mi 内存, 200m CPU
- **健康检查**: /health 端点
- **Secret 依赖**: llm-api-secret（包含 DASHSCOPE_API_KEY）
