package com.hhusen.athena.taskservice;

import org.mlflow.tracking.MlflowClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MLflowConfig {
    @Value("${mlflow.tracking.uri}")
    private String mlflowTrackingUri;

    @Bean
    public MlflowClient mlflowClient() {
        System.setProperty("mlflow.tracking.uri", mlflowTrackingUri);
        return new MlflowClient(mlflowTrackingUri);
    }
} 