package com.hhusen.athena.knowledgeservice.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.hhusen.athena.knowledgeservice.model.Policy;
import com.hhusen.athena.knowledgeservice.service.PolicyService;

@RestController
@RequestMapping("/api")
public class PolicyController {
    
    private final PolicyService policyService;
    
    @Autowired
    public PolicyController(PolicyService policyService) {
        this.policyService = policyService;
    }
    
    @GetMapping("/policies/{cityName}")
    public ResponseEntity<List<Policy>> getPoliciesByCity(@PathVariable String cityName) {
        List<Policy> policies = policyService.getPoliciesByCity(cityName);
        return ResponseEntity.ok(policies);
    }

    @PostMapping("/policies")
    public ResponseEntity<Policy> createPolicy(@RequestBody Map<String, Object> policyData) {
        Policy policy = policyService.createPolicy(policyData);
        return ResponseEntity.ok(policy);
    }

    @PutMapping("/policies/{policyId}")
    public ResponseEntity<Policy> updatePolicy(@PathVariable String policyId, @RequestBody Map<String, Object> policyData) {
        Policy policy = policyService.updatePolicy(policyId, policyData);
        return ResponseEntity.ok(policy);
    }

    @DeleteMapping("/policies/{policyId}")
    public ResponseEntity<Void> deletePolicy(@PathVariable String policyId) {
        policyService.deletePolicy(policyId);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/cities")
    public ResponseEntity<List<Map<String, Object>>> getCities() {
        List<Map<String, Object>> cities = policyService.getCities();
        return ResponseEntity.ok(cities);
    }

    @GetMapping("/graph/structure")
    public ResponseEntity<Map<String, Object>> getGraphStructure() {
        Map<String, Object> structure = policyService.getGraphStructure();
        return ResponseEntity.ok(structure);
    }
}