package com.hhusen.athena.knowledgeservice.controller;

import com.hhusen.athena.knowledgeservice.model.Policy;
import com.hhusen.athena.knowledgeservice.service.PolicyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api")
public class PolicyController {
    
    private final PolicyService policyService;
    
    @Autowired
    public PolicyController(PolicyService policyService) {
        this.policyService = policyService;
    }
    
    @GetMapping("/policies/{cityName}")
    public ResponseEntity<List<Policy>> getPoliciesByCity(@PathVariable String cityName) {
        List<Policy> policies = policyService.getPoliciesByCity(cityName);
        return ResponseEntity.ok(policies);
    }
} 