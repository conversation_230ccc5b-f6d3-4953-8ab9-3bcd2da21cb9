# Athena Core 全流程部署指南
启动minikube：
minikube start --image-repository=registry.cn-hangzhou.aliyuncs.com/google_containers
部署各类密钥：
kubectl apply -f services/1_java_backend/knowledge-service/k8s/neo4j-secret.yaml
kubectl apply -f services/1_java_backend/task-service/k8s/minio-secret.yaml
kubectl apply -f services/2_python_engines/llm-service/k8s/llm-secrets.yaml
部署基本服务：
[postgres:14-alpine]
docker save postgres:14-alpine -o postgres-14-alpine.tar
minikube cp postgres-14-alpine.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/postgres-14-alpine.tar"
kubectl apply -f services/1_java_backend/task-service/k8s/postgres.yaml
可能需要用到的删除命令：kubectl delete pod postgres-statefulset-0 --grace-period=0 --force

[neo4j:5-community]
docker save neo4j:5-community -o neo4j-5-community.tar
minikube cp neo4j-5-community.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/neo4j-5-community.tar"
kubectl apply -f services/1_java_backend/task-service/k8s/neo4j.yaml
可能需要用到的删除命令：kubectl delete pod neo4j-statefulset-0 --grace-period=0 --force


[rabbitmq:3.12-management-alpine]
docker save rabbitmq:3.12-management-alpine -o rabbitmq-3.12-management-alpine.tar
minikube cp rabbitmq-3.12-management-alpine.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/rabbitmq-3.12-management-alpine.tar"
kubectl apply -f services/1_java_backend/task-service/k8s/rabbitmq.yaml
需要用到的删除命令：kubectl delete pod rabbitmq-statefulset-0 --grace-period=0 --force

[minio/minio:latest]
docker save minio/minio:latest -o minio-minio-latest.tar
minikube cp minio-minio-latest.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/minio-minio-latest.tar"
kubectl apply -f services/1_java_backend/task-service/k8s/minio.yaml
需要用到的删除命令：kubectl delete pod minio-statefulset-0 --grace-period=0 --force

[ghcr.io/mlflow/mlflow:latest]
docker save ghcr.io/mlflow/mlflow:latest -o ghcr.io-mlflow-mlflow-latest.tar
minikube cp ghcr.io-mlflow-mlflow-latest.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/ghcr.io-mlflow-mlflow-latest.tar" 
kubectl apply -f services/1_java_backend/task-service/k8s/mlflow.yaml
需要用到的删除命令：kubectl delete pod mlflow-statefulset-0 --grace-period=0 --force

[ghcr.io/chroma-core/chroma:0.4.24]
docker save ghcr.io/chroma-core/chroma:0.4.24 -o ghcr.io-chroma-core-chroma-0.4.24.tar
minikube cp ghcr.io-chroma-core-chroma-0.4.24.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/ghcr.io-chroma-core-chroma-0.4.24.tar"
kubectl apply -f services/2_python_engines/llm-service/k8s/chromadb.yaml
需要用到的删除命令：kubectl delete pod chromadb-statefulset-0 --grace-period=0 --force
重建 ChromaDB RAG 知识库：
kubectl port-forward svc/chromadb-svc 8000:8000
python offline_training/ingest_docs.py

部署模型服务：
[knowledge-service]
docker save athena/knowledge-service:0.0.8 -o athena-knowledge-service-0.0.8.tar
minikube cp athena-knowledge-service-0.0.8.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/athena-knowledge-service-0.0.8.tar"
kubectl apply -f services/1_java_backend/knowledge-service/k8s/deployment.yaml
验证：
kubectl port-forward <> 7474:7474 7687:7687
访问 http://localhost:7474，使用用户名 neo4j 和密码 supersecret 登录。
显示已有数据

[task-service]
docker save athena/task-service:0.0.13 -o athena-task-service-0.0.13.tar
minikube cp athena-task-service-0.0.13.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/athena-task-service-0.0.13.tar"
kubectl apply -f services/1_java_backend/task-service/k8s/deployment.yaml

[user-service]
docker save athena/user-service:0.0.2 -o athena-user-service-0.0.2.tar
minikube cp athena-user-service-0.0.2.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/athena-user-service-0.0.2.tar"
kubectl apply -f services/1_java_backend/userservice/k8s/deployment.yaml
kubectl apply -f services/1_java_backend/userservice/k8s/service.yaml

[llm-service]
docker save athena/llm-service:0.0.18 -o athena-llm-service-0.0.18.tar
minikube cp athena-llm-service-0.0.18.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/athena-llm-service-0.0.18.tar"
kubectl apply -f services/2_python_engines/llm-service/k8s/deployment.yaml

[pinn-engine]
docker save athena/pinn-engine:0.0.16 -o athena-pinn-engine-0.0.16.tar
minikube cp athena-pinn-engine-0.0.16.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/athena-pinn-engine-0.0.16.tar"
kubectl apply -f services/2_python_engines/pinn-engine/k8s/deployment.yaml

[agent-engine]
docker save athena/agent-engine:0.0.13 -o athena-agent-engine-0.0.13.tar
minikube cp athena-agent-engine-0.0.13.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/athena-agent-engine-0.0.13.tar"
kubectl apply -f services/2_python_engines/agent-engine/k8s/deployment.yaml

[agent-executor-service]
docker save athena/agent-executor-service:0.0.4 -o athena-agent-executor-service-0.0.4.tar
minikube cp athena-agent-executor-service-0.0.4.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/athena-agent-executor-service-0.0.4.tar"
kubectl apply -f services/2_python_engines/agent-executor-service/k8s/deployment.yaml

验证模拟：
kubectl port-forward svc/task-service-svc 8080:80
kubectl port-forward svc/mlflow-svc 5000:5000
python test_policy_trigger.py

[api-gateway]
docker save athena/api-gateway:0.0.5 -o api-gateway-0.0.5.tar
minikube cp api-gateway-0.0.5.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/api-gateway-0.0.5.tar"
kubectl apply -f services/0_gateway/apigateway/k8s/deployment.yaml
kubectl apply -f services/0_gateway/apigateway/k8s/service.yaml

[admin-service]
docker save athena/admin-service:0.0.1 -o athena-admin-service-0.0.1.tar
minikube cp athena-admin-service-0.0.1.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/athena-admin-service-0.0.1.tar"
kubectl apply -f services/2_python_engines/admin-service/k8s/rbac.yaml
kubectl apply -f services/2_python_engines/admin-service/k8s/deployment.yaml

