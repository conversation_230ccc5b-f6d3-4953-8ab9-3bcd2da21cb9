# Agent Executor Service Windows 调试脚本
param(
    [string]$ServiceUrl = ""
)

# 获取服务地址
if (-not $ServiceUrl) {
    Write-Host "正在获取服务地址..."
    try {
        $NODE_IP = kubectl get nodes -o jsonpath="{.items[0].status.addresses[?(@.type=='InternalIP')].address}"
        $NODE_PORT = kubectl get svc agent-executor-service -o jsonpath="{.spec.ports[0].nodePort}"
        $ServiceUrl = "http://${NODE_IP}:${NODE_PORT}"
        Write-Host "自动检测到服务地址: $ServiceUrl"
    }
    catch {
        Write-Host "无法自动获取服务地址，请手动指定"
        Write-Host "用法: .\debug-windows.ps1 -ServiceUrl 'http://************:30080'"
        exit 1
    }
}

Write-Host "🔍 Agent Executor Service 调试工具"
Write-Host "服务地址: $ServiceUrl"
Write-Host "=" * 60

# 1. 基础连通性测试
Write-Host "`n1. 🏥 基础连通性测试"
Write-Host "-" * 30

try {
    $health = curl.exe "$ServiceUrl/health" --silent
    Write-Host "✅ 健康检查: $health"
} catch {
    Write-Host "❌ 健康检查失败: $_"
}

try {
    $info = curl.exe "$ServiceUrl/" --silent | ConvertFrom-Json
    Write-Host "✅ 服务版本: $($info.version)"
    Write-Host "✅ LLM 配置: $($info.llm_configuration | ConvertTo-Json -Compress)"
} catch {
    Write-Host "❌ 服务信息获取失败: $_"
}

# 2. LLM 调试测试
Write-Host "`n2. 🧠 LLM 调试测试"
Write-Host "-" * 30

$debugCommands = @(
    "你好，请介绍一下你的功能",
    "我想为北京创建一个水资源模拟，降雨量50mm，温度25度，人口2000万",
    "请查询任务ID 1 的状态"
)

foreach ($command in $debugCommands) {
    Write-Host "`n🔍 测试命令: $command"
    
    $body = @{
        command = $command
    } | ConvertTo-Json -Compress
    
    try {
        $response = curl.exe -X POST "$ServiceUrl/debug-llm" `
            -H "Content-Type: application/json" `
            -d $body --silent | ConvertFrom-Json
        
        Write-Host "   模型: $($response.model)"
        Write-Host "   有工具调用: $($response.response.has_tool_calls)"
        
        if ($response.response.tool_calls) {
            Write-Host "   工具调用:"
            foreach ($tc in $response.response.tool_calls) {
                Write-Host "     - $($tc.function.name): $($tc.function.arguments)"
            }
        }
        
        if ($response.response.content) {
            Write-Host "   响应内容: $($response.response.content)"
        }
        
        if ($response.error) {
            Write-Host "   ❌ 错误: $($response.error)"
        }
        
    } catch {
        Write-Host "   ❌ 调试失败: $_"
    }
}

# 3. 工具函数测试
Write-Host "`n3. 🔧 工具函数测试"
Write-Host "-" * 30

Write-Host "`n🧪 测试 run_simulation 工具"
$simBody = @{
    city_name = "测试城市"
    rainfall = 50.0
    temperature = 25.0
    population = 100000
} | ConvertTo-Json -Compress

try {
    $simResponse = curl.exe -X POST "$ServiceUrl/test-tools/run-simulation" `
        -H "Content-Type: application/json" `
        -d $simBody --silent
    Write-Host "✅ 模拟任务创建: $simResponse"
} catch {
    Write-Host "❌ 模拟任务创建失败: $_"
}

# 4. Agent 循环测试
Write-Host "`n4. 🤖 Agent 循环测试"
Write-Host "-" * 30

$agentCommand = "请为测试城市创建一个简单的模拟，人口10000，降雨量50，温度25"
Write-Host "`n🚀 执行 Agent 命令: $agentCommand"

$agentBody = @{
    command = $agentCommand
} | ConvertTo-Json -Compress

try {
    Write-Host "📡 开始流式响应..."
    curl.exe -N -X POST "$ServiceUrl/execute-command" `
        -H "Content-Type: application/json" `
        -d $agentBody
} catch {
    Write-Host "❌ Agent 执行失败: $_"
}

# 5. 日志检查建议
Write-Host "`n5. 📋 日志检查建议"
Write-Host "-" * 30
Write-Host "如果上述测试有问题，请在另一个终端运行以下命令查看日志:"
Write-Host "kubectl logs -l app=agent-executor-service -f"
Write-Host ""
Write-Host "检查依赖服务状态:"
Write-Host "kubectl get pods | findstr -E `"(task-service|llm-service|agent-executor)`""
Write-Host ""
Write-Host "检查 Secret 配置:"
Write-Host "kubectl get secret llm-api-secret -o yaml"

Write-Host "`n" + "=" * 60
Write-Host "🎉 调试完成！"
Write-Host "=" * 60
