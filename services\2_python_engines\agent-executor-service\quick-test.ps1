# 快速测试脚本 - 修复后验证
param(
    [string]$ServiceUrl = "http://127.0.0.1:58345"
)

Write-Host "🚀 快速测试 Agent Executor Service"
Write-Host "服务地址: $ServiceUrl"
Write-Host "=" * 50

# 1. 健康检查
Write-Host "`n1. 🏥 健康检查"
try {
    $health = curl.exe "$ServiceUrl/health" --silent
    Write-Host "✅ $health"
} catch {
    Write-Host "❌ 健康检查失败: $_"
    exit 1
}

# 2. 服务信息
Write-Host "`n2. 📋 服务信息"
try {
    $info = curl.exe "$ServiceUrl/" --silent | ConvertFrom-Json
    Write-Host "✅ 版本: $($info.version)"
    Write-Host "✅ LLM 提供商: $($info.llm_configuration.active_provider)"
    Write-Host "✅ 客户端初始化: $($info.llm_configuration.client_initialized)"
} catch {
    Write-Host "❌ 服务信息获取失败: $_"
}

# 3. LLM 调试测试
Write-Host "`n3. 🧠 LLM 调试测试"
$debugBody = '{"command": "我想为北京创建一个水资源模拟，降雨量50mm，温度25度，人口2000万"}'

try {
    Write-Host "发送调试请求..."
    $debugResponse = curl.exe -X POST "$ServiceUrl/debug-llm" `
        -H "Content-Type: application/json" `
        -d $debugBody --silent | ConvertFrom-Json
    
    Write-Host "✅ 模型: $($debugResponse.model)"
    Write-Host "✅ 有工具调用: $($debugResponse.response.has_tool_calls)"
    
    if ($debugResponse.response.tool_calls) {
        Write-Host "✅ 工具调用详情:"
        foreach ($tc in $debugResponse.response.tool_calls) {
            Write-Host "   - $($tc.function.name): $($tc.function.arguments)"
        }
    }
    
    if ($debugResponse.error) {
        Write-Host "❌ 调试错误: $($debugResponse.error)"
    }
} catch {
    Write-Host "❌ LLM 调试失败: $_"
}

# 4. 简单 Agent 测试
Write-Host "`n4. 🤖 简单 Agent 测试"
$simpleBody = '{"command": "你好，请介绍一下你的功能"}'

try {
    Write-Host "发送简单命令..."
    curl.exe -N -X POST "$ServiceUrl/execute-command" `
        -H "Content-Type: application/json" `
        -d $simpleBody
    Write-Host "`n✅ 简单命令测试完成"
} catch {
    Write-Host "❌ 简单命令失败: $_"
}

# 5. 工具调用 Agent 测试
Write-Host "`n5. 🔧 工具调用 Agent 测试"
$toolBody = '{"command": "请为测试城市创建一个简单的模拟，人口10000，降雨量50，温度25"}'

try {
    Write-Host "发送工具调用命令..."
    curl.exe -N -X POST "$ServiceUrl/execute-command" `
        -H "Content-Type: application/json" `
        -d $toolBody
    Write-Host "`n✅ 工具调用测试完成"
} catch {
    Write-Host "❌ 工具调用失败: $_"
}

# 6. 复杂 Agent 测试（带等待机制）
Write-Host "`n6. 🎯 复杂 Agent 测试（带等待机制）"
$complexBody = '{"command": "请对人口为 50000、降雨量为 110、温度为 28.5 的 Rivertown 运行模拟。完成后，告诉我最终的水位并提供简要分析。"}'

try {
    Write-Host "发送复杂命令（包含等待机制）..."
    curl.exe -N -X POST "$ServiceUrl/execute-command" `
        -H "Content-Type: application/json" `
        -d $complexBody
    Write-Host "`n✅ 复杂命令测试完成"
} catch {
    Write-Host "❌ 复杂命令失败: $_"
}

# 7. 测试新的等待工具
Write-Host "`n7. 🔧 测试等待工具"
$waitBody = '{"command": "请等待任务ID 1 完成，然后告诉我结果"}'

try {
    Write-Host "测试等待工具..."
    curl.exe -N -X POST "$ServiceUrl/execute-command" `
        -H "Content-Type: application/json" `
        -d $waitBody
    Write-Host "`n✅ 等待工具测试完成"
} catch {
    Write-Host "❌ 等待工具测试失败: $_"
}

Write-Host "`n" + "=" * 50
Write-Host "🎉 测试完成！"

# 提示查看日志
Write-Host "`n💡 如果有问题，请查看服务日志:"
Write-Host "kubectl logs -l app=agent-executor-service -f"
