<template>
  <div class="tasks-container">
    <!-- 顶部搜索和操作栏 -->
    <el-card class="filter-card">
      <div class="filter-form">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="任务名称">
            <el-input v-model="searchForm.taskName" placeholder="请输入任务名称" clearable />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
              <el-option label="待处理" value="pending" />
              <el-option label="运行中" value="running" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="canceled" />
              <el-option label="失败" value="failed" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建人">
            <el-input v-model="searchForm.creator" placeholder="请输入创建人" clearable />
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 任务统计 -->
    <div class="task-stats">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="4" :xl="4" v-for="(stat, index) in taskStats" :key="index">
          <el-card class="stat-card" :body-style="{ padding: '16px' }">
            <div class="stat-content">
              <div class="stat-icon" :class="stat.iconClass">
                <el-icon><component :is="stat.icon" /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stat.count }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 任务列表 -->
    <el-card class="list-card">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <el-icon class="header-icon"><Document /></el-icon>
            <span>任务列表</span>
            <el-tag type="info" class="count-tag">{{ taskData.length }} 项</el-tag>
          </div>
          <div class="header-actions">
            <el-button type="primary" :icon="Plus" @click="handleCreate">创建任务</el-button>
            <el-dropdown trigger="click" @command="handleBatchCommand">
              <el-button plain>
                批量操作 <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="cancel">
                    <el-icon><Close /></el-icon> 取消任务
                  </el-dropdown-item>
                  <el-dropdown-item command="restart">
                    <el-icon><RefreshRight /></el-icon> 重新运行
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>
                    <el-icon><Delete /></el-icon> 批量删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>
      
      <!-- 任务表格 -->
      <el-table
        :data="taskData"
        border
        stripe
        style="width: 100%"
        v-loading="loading"
        :header-cell-style="{ background: '#f5f7fa' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="id" label="任务ID" width="80" sortable />
        <el-table-column prop="taskName" label="任务名称" min-width="150">
          <template #default="{ row }">
            <div class="task-name-cell">
              <span class="task-name" :title="row.taskName">{{ row.taskName }}</span>
              <el-tag v-if="row.priority === 'high'" size="small" type="danger" effect="dark">高优先级</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              :effect="row.status === 'running' ? 'dark' : 'light'"
            >
              <div class="status-tag">
                <div v-if="row.status === 'running'" class="status-dot pulsing"></div>
                <span>{{ getStatusText(row.status) }}</span>
              </div>
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="taskType" label="任务类型" width="100">
          <template #default="{ row }">
            <el-tag type="info" effect="plain">{{ row.taskType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="创建人" width="100" />
        <el-table-column prop="progress" label="进度" width="150">
          <template #default="{ row }">
            <div class="progress-cell">
              <el-progress 
                :percentage="row.progress" 
                :status="getProgressStatus(row.status, row.progress)"
                :stroke-width="10"
                :format="percentFormat"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" sortable width="180" />
        <el-table-column prop="updateTime" label="更新时间" sortable width="180" />
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="{ row }">
            <div class="action-btns">
              <el-button 
                v-if="row.status === 'pending' || row.status === 'failed'"
                type="success" 
                size="small" 
                text 
                :icon="VideoPlay"
                @click="handleRun(row)"
              >
                启动
              </el-button>
              <el-button
                v-if="row.status === 'running'"
                type="danger"
                size="small"
                text
                :icon="VideoPause"
                @click="handlePause(row)"
              >
                暂停
              </el-button>
              <el-button 
                v-if="row.status !== 'completed' && row.status !== 'canceled'"
                type="info" 
                size="small" 
                text 
                :icon="Close" 
                @click="handleCancel(row)"
              >
                取消
              </el-button>
              <el-button 
                type="primary" 
                size="small" 
                text 
                :icon="View" 
                @click="handleViewDetail(row)"
              >
                详情
              </el-button>
              <el-dropdown>
                <el-button size="small" text>
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleViewLogs(row)">
                      <el-icon><Document /></el-icon> 查看日志
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleDuplicate(row)">
                      <el-icon><CopyDocument /></el-icon> 复制任务
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleDownloadResult(row)">
                      <el-icon><Download /></el-icon> 下载结果
                    </el-dropdown-item>
                    <el-dropdown-item divided @click="handleDelete(row)" style="color: #F56C6C;">
                      <el-icon><Delete /></el-icon> 删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 创建任务表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="创建任务"
      width="650px"
    >
      <el-form
        ref="taskFormRef"
        :model="taskForm"
        :rules="taskRules"
        label-width="100px"
      >
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="taskForm.taskName" placeholder="请输入任务名称" />
        </el-form-item>
        
        <el-form-item label="任务类型" prop="taskType">
          <el-select v-model="taskForm.taskType" placeholder="请选择任务类型">
            <el-option label="模型训练" value="training" />
            <el-option label="预测推理" value="inference" />
            <el-option label="数据处理" value="data-processing" />
            <el-option label="系统诊断" value="diagnostic" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input v-model="taskForm.description" type="textarea" :rows="3" placeholder="请输入任务描述" />
        </el-form-item>
        
        <el-form-item label="优先级">
          <el-radio-group v-model="taskForm.priority">
            <el-radio label="low">低</el-radio>
            <el-radio label="normal">中</el-radio>
            <el-radio label="high">高</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="参数设置">
          <div class="params-editor">
            <el-input v-model="taskForm.params" type="textarea" :rows="4" placeholder="以 JSON 格式输入任务参数" />
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitTaskForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, 
  Refresh, 
  Plus, 
  Document, 
  CopyDocument,
  Delete, 
  View, 
  Close, 
  ArrowDown, 
  Download,
  VideoPlay,
  VideoPause,
  RefreshRight,
  Timer,
  Finished,
  WarningFilled,
  CircleCheck
} from '@element-plus/icons-vue'

// 表格加载状态
const loading = ref(false)
const dateRange = ref([])

// 搜索表单
const searchForm = reactive({
  taskName: '',
  status: '',
  creator: ''
})

// 统计数据
const taskStats = [
  { label: '全部任务', count: 35, icon: 'Document', iconClass: 'all-icon' },
  { label: '运行中', count: 8, icon: 'Timer', iconClass: 'running-icon' },
  { label: '待处理', count: 12, icon: 'Timer', iconClass: 'pending-icon' },
  { label: '已完成', count: 13, icon: 'CircleCheck', iconClass: 'completed-icon' },
  { label: '已取消', count: 1, icon: 'Close', iconClass: 'canceled-icon' },
  { label: '失败', count: 1, icon: 'WarningFilled', iconClass: 'failed-icon' }
]

// 任务数据
const taskData = ref([
  {
    id: 'TASK-2501',
    taskName: '流体模型训练 (PINN-Hydro-v2)',
    status: 'running',
    progress: 65,
    taskType: '模型训练',
    creator: 'admin',
    createTime: '2025-01-17 09:30:00',
    updateTime: '2025-01-17 10:15:00',
    priority: 'high',
    description: '使用物理信息神经网络进行流体模型训练',
    params: JSON.stringify({
      epochs: 200,
      batch_size: 64,
      learning_rate: 0.001,
      model_type: "pinn"
    }, null, 2)
  },
  {
    id: 'TASK-2500',
    taskName: '政策知识图谱更新',
    status: 'completed',
    progress: 100,
    taskType: '数据处理',
    creator: 'user001',
    createTime: '2025-01-16 16:20:00',
    updateTime: '2025-01-16 16:45:00',
    priority: 'normal',
    description: '更新城市区域政策知识图谱',
    params: '{}'
  },
  {
    id: 'TASK-2499',
    taskName: '洪水风险预测 (北区)',
    status: 'pending',
    progress: 0,
    taskType: '预测推理',
    creator: 'user002',
    createTime: '2025-01-16 14:50:00',
    updateTime: '2025-01-16 14:50:00',
    priority: 'normal',
    description: '使用训练好的模型进行北区洪水风险预测',
    params: '{}'
  },
  {
    id: 'TASK-2498',
    taskName: '系统性能诊断',
    status: 'failed',
    progress: 35,
    taskType: '系统诊断',
    creator: 'admin',
    createTime: '2025-01-15 11:20:00',
    updateTime: '2025-01-15 11:40:00',
    priority: 'low',
    description: '系统性能诊断任务',
    params: '{}'
  },
  {
    id: 'TASK-2497',
    taskName: '历史数据清洗',
    status: 'canceled',
    progress: 50,
    taskType: '数据处理',
    creator: 'user001',
    createTime: '2025-01-15 09:15:00',
    updateTime: '2025-01-15 09:30:00',
    priority: 'normal',
    description: '清洗历史数据',
    params: '{}'
  }
])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(5)

// 选中行
const selectedRows = ref([])

// 任务表单对话框
const dialogVisible = ref(false)
const taskFormRef = ref()
const taskForm = reactive({
  taskName: '',
  taskType: '',
  description: '',
  priority: 'normal',
  params: '{}'
})

// 表单验证规则
const taskRules = {
  taskName: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  taskType: [
    { required: true, message: '请选择任务类型', trigger: 'change' }
  ]
}

// 生命周期钩子
onMounted(() => {
  fetchTaskData()
})

// 获取任务数据
const fetchTaskData = () => {
  loading.value = true
  // 模拟API请求延迟
  setTimeout(() => {
    loading.value = false
  }, 500)
}

// 状态文本映射
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待处理',
    'running': '运行中',
    'completed': '已完成',
    'canceled': '已取消',
    'failed': '失败'
  }
  return statusMap[status] || status
}

// 状态类型映射
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'pending': 'info',
    'running': 'primary',
    'completed': 'success',
    'canceled': 'info',
    'failed': 'danger'
  }
  return typeMap[status] || ''
}

// 进度状态映射
const getProgressStatus = (status: string, progress: number) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  if (status === 'canceled') return ''
  return progress < 100 ? '' : 'success'
}

// 进度百分比格式化
const percentFormat = (percentage: number) => {
  return percentage === 100 ? '完成' : `${percentage}%`
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchTaskData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.taskName = ''
  searchForm.status = ''
  searchForm.creator = ''
  dateRange.value = []
  handleSearch()
}

// 表格选择行变化
const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchTaskData()
}

// 页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchTaskData()
}

// 创建任务
const handleCreate = () => {
  taskForm.taskName = ''
  taskForm.taskType = ''
  taskForm.description = ''
  taskForm.priority = 'normal'
  taskForm.params = '{}'
  dialogVisible.value = true
}

// 查看任务详情
const handleViewDetail = (row: any) => {
  ElMessage.info(`查看任务详情：${row.taskName}`)
}

// 查看任务日志
const handleViewLogs = (row: any) => {
  ElMessage.info(`查看任务日志：${row.taskName}`)
}

// 复制任务
const handleDuplicate = (row: any) => {
  ElMessage.info(`复制任务：${row.taskName}`)
}

// 下载结果
const handleDownloadResult = (row: any) => {
  ElMessage.info(`下载任务结果：${row.taskName}`)
}

// 启动任务
const handleRun = (row: any) => {
  ElMessage.success(`任务 ${row.taskName} 已启动`)
  row.status = 'running'
  row.updateTime = new Date().toLocaleString()
}

// 暂停任务
const handlePause = (row: any) => {
  ElMessage.warning(`任务 ${row.taskName} 已暂停`)
}

// 取消任务
const handleCancel = (row: any) => {
  ElMessageBox.confirm(`确认取消任务：${row.taskName}？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    row.status = 'canceled'
    row.updateTime = new Date().toLocaleString()
    ElMessage.info(`任务已取消`)
  }).catch(() => {})
}

// 删除任务
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确认删除任务：${row.taskName}？此操作不可恢复！`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'danger'
  }).then(() => {
    const index = taskData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      taskData.value.splice(index, 1)
      total.value--
      ElMessage.success('删除成功')
    }
  }).catch(() => {})
}

// 批量操作
const handleBatchCommand = (command: string) => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择任务')
    return
  }
  
  const actionMap: Record<string, string> = {
    'cancel': '取消',
    'restart': '重新启动',
    'delete': '删除'
  }
  
  ElMessageBox.confirm(`确认对选中的 ${selectedRows.value.length} 个任务进行${actionMap[command]}操作？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: command === 'delete' ? 'danger' : 'warning'
  }).then(() => {
    ElMessage.success(`已对 ${selectedRows.value.length} 个任务执行${actionMap[command]}操作`)
  }).catch(() => {})
}

// 提交任务表单
const submitTaskForm = () => {
  taskFormRef.value.validate((valid: boolean) => {
    if (!valid) return
    
    // 模拟提交
    const newTask = {
      id: `TASK-${Math.floor(Math.random() * 1000 + 3000)}`,
      taskName: taskForm.taskName,
      status: 'pending',
      progress: 0,
      taskType: taskForm.taskType,
      creator: 'admin',
      createTime: new Date().toLocaleString(),
      updateTime: new Date().toLocaleString(),
      priority: taskForm.priority,
      description: taskForm.description,
      params: taskForm.params
    }
    
    taskData.value.unshift(newTask)
    total.value++
    dialogVisible.value = false
    
    ElMessage.success('任务创建成功')
  })
}
</script>

<style scoped>
.tasks-container {
  width: 100%;
}

.filter-card {
  margin-bottom: 16px;
  background-color: #fff;
}

.task-stats {
  margin-bottom: 16px;
}

.stat-card {
  margin-bottom: 16px;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.all-icon {
  background: #909399;
}

.running-icon {
  background: #409eff;
}

.pending-icon {
  background: #e6a23c;
}

.completed-icon {
  background: #67c23a;
}

.canceled-icon {
  background: #909399;
}

.failed-icon {
  background: #f56c6c;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.list-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.header-icon {
  margin-right: 8px;
}

.count-tag {
  margin-left: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.task-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.progress-cell {
  padding: 0 5px;
}

.status-tag {
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
}

.status-dot.pulsing {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

.action-btns {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

.action-btns .el-button {
  padding-left: 5px;
  padding-right: 5px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.params-editor {
  font-family: monospace;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .search-form {
    flex-wrap: wrap;
  }
  
  .el-form--inline .el-form-item {
    margin-right: 10px;
  }
}

@media (max-width: 992px) {
  .header-actions {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .action-btns {
    flex-wrap: wrap;
    justify-content: flex-end;
  }
}

@media (max-width: 576px) {
  .stat-content {
    flex-direction: column;
  text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }
  
  .header-actions .el-button {
    flex: 1;
  }
}
</style>
