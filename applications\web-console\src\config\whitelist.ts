// 用户白名单配置
export interface WhitelistUser {
  username: string
  displayName?: string
  role?: string
  department?: string
  email?: string
  enabled: boolean
  createdAt?: string
  lastLogin?: string
}

// 白名单用户列表
export const WHITELIST_USERS: WhitelistUser[] = [
  {
    username: 'admin',
    displayName: '系统管理员',
    role: 'admin',
    department: 'IT部门',
    email: '<EMAIL>',
    enabled: true,
    createdAt: '2024-01-01',
  },
  {
    username: 'operator',
    displayName: '运维工程师',
    role: 'operator',
    department: 'IT部门',
    email: '<EMAIL>',
    enabled: true,
    createdAt: '2024-01-01',
  },
  {
    username: 'viewer',
    displayName: '只读用户',
    role: 'viewer',
    department: '业务部门',
    email: '<EMAIL>',
    enabled: true,
    createdAt: '2024-01-01',
  },
  // 可以添加更多用户
  // {
  //   username: 'john.doe',
  //   displayName: '<PERSON>',
  //   role: 'developer',
  //   department: '开发部门',
  //   email: '<EMAIL>',
  //   enabled: true,
  //   createdAt: '2024-01-01',
  // },
]

// 白名单配置
export const WHITELIST_CONFIG = {
  // 是否启用白名单验证
  enabled: true,
  
  // 是否允许大小写不敏感匹配
  caseInsensitive: false,
  
  // 是否记录登录尝试
  logAttempts: true,
  
  // 失败尝试的最大次数（可选，用于后续扩展）
  maxFailedAttempts: 5,
  
  // 锁定时间（分钟）
  lockoutDuration: 30,
}

/**
 * 检查用户是否在白名单中
 * @param username 用户名
 * @returns 白名单用户信息或 null
 */
export function checkWhitelist(username: string): WhitelistUser | null {
  if (!WHITELIST_CONFIG.enabled) {
    return {
      username,
      enabled: true,
      role: 'user'
    }
  }

  const searchUsername = WHITELIST_CONFIG.caseInsensitive 
    ? username.toLowerCase() 
    : username

  const user = WHITELIST_USERS.find(user => {
    const userUsername = WHITELIST_CONFIG.caseInsensitive 
      ? user.username.toLowerCase() 
      : user.username
    return userUsername === searchUsername && user.enabled
  })

  return user || null
}

/**
 * 获取所有启用的白名单用户
 * @returns 启用的用户列表
 */
export function getEnabledUsers(): WhitelistUser[] {
  return WHITELIST_USERS.filter(user => user.enabled)
}

/**
 * 获取用户角色
 * @param username 用户名
 * @returns 用户角色
 */
export function getUserRole(username: string): string {
  const user = checkWhitelist(username)
  return user?.role || 'guest'
}

/**
 * 记录登录尝试
 * @param username 用户名
 * @param success 是否成功
 * @param ip IP地址（可选）
 */
export function logLoginAttempt(username: string, success: boolean, ip?: string) {
  if (!WHITELIST_CONFIG.logAttempts) return

  const timestamp = new Date().toISOString()
  const logEntry = {
    timestamp,
    username,
    success,
    ip: ip || 'unknown',
    userAgent: navigator.userAgent
  }

  // 存储到 localStorage（生产环境中应该发送到服务器）
  const logs = JSON.parse(localStorage.getItem('login_attempts') || '[]')
  logs.push(logEntry)
  
  // 只保留最近100条记录
  if (logs.length > 100) {
    logs.splice(0, logs.length - 100)
  }
  
  localStorage.setItem('login_attempts', JSON.stringify(logs))
  
  console.log(`登录尝试记录: ${username} - ${success ? '成功' : '失败'} - ${timestamp}`)
}

/**
 * 获取登录尝试记录
 * @returns 登录尝试记录列表
 */
export function getLoginAttempts() {
  return JSON.parse(localStorage.getItem('login_attempts') || '[]')
}

/**
 * 清除登录尝试记录
 */
export function clearLoginAttempts() {
  localStorage.removeItem('login_attempts')
}

/**
 * 更新用户最后登录时间
 * @param username 用户名
 */
export function updateLastLogin(username: string) {
  const user = WHITELIST_USERS.find(u => u.username === username)
  if (user) {
    user.lastLogin = new Date().toISOString()
  }
}
