<template>
  <div class="base-chart" :class="chartClass">
    <div v-if="title" class="chart-header">
      <h3 class="chart-title">{{ title }}</h3>
      <div v-if="showToolbar" class="chart-toolbar">
        <el-button-group size="small">
          <el-button :icon="Refresh" @click="handleRefresh" :loading="loading">
            刷新
          </el-button>
          <el-button :icon="Download" @click="handleExport">
            导出
          </el-button>
          <el-dropdown @command="handleToolbarCommand">
            <el-button :icon="MoreFilled" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="fullscreen">全屏</el-dropdown-item>
                <el-dropdown-item command="settings">设置</el-dropdown-item>
                <el-dropdown-item divided command="reset">重置</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-button-group>
      </div>
    </div>
    
    <div class="chart-container" :style="containerStyle">
      <div 
        ref="chartRef" 
        class="chart-canvas"
        :style="{ width: '100%', height: height }"
      ></div>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="chart-loading">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <span>{{ loadingText }}</span>
      </div>
      
      <!-- 空数据状态 -->
      <div v-if="!loading && isEmpty" class="chart-empty">
        <el-icon class="empty-icon"><DataBoard /></el-icon>
        <span>{{ emptyText }}</span>
      </div>
      
      <!-- 错误状态 -->
      <div v-if="error" class="chart-error">
        <el-icon class="error-icon"><Warning /></el-icon>
        <span>{{ error }}</span>
        <el-button size="small" @click="handleRefresh">重试</el-button>
      </div>
    </div>
    
    <!-- 图例 -->
    <div v-if="showLegend && legendData.length > 0" class="chart-legend">
      <div 
        v-for="(item, index) in legendData" 
        :key="index"
        class="legend-item"
        @click="handleLegendClick(item, index)"
      >
        <span 
          class="legend-color" 
          :style="{ backgroundColor: item.color }"
        ></span>
        <span class="legend-label">{{ item.name }}</span>
        <span v-if="item.value !== undefined" class="legend-value">
          {{ formatValue(item.value) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { 
  Refresh, 
  Download, 
  MoreFilled, 
  Loading, 
  DataBoard, 
  Warning 
} from '@element-plus/icons-vue'

export interface ChartData {
  labels?: string[]
  datasets?: any[]
  series?: any[]
  [key: string]: any
}

export interface LegendItem {
  name: string
  color: string
  value?: number | string
  visible?: boolean
}

interface Props {
  title?: string
  data?: ChartData
  options?: any
  type?: string
  width?: string | number
  height?: string | number
  loading?: boolean
  loadingText?: string
  emptyText?: string
  error?: string
  showToolbar?: boolean
  showLegend?: boolean
  legendData?: LegendItem[]
  responsive?: boolean
  animation?: boolean
  theme?: 'light' | 'dark' | 'auto'
}

const props = withDefaults(defineProps<Props>(), {
  height: '300px',
  loadingText: '加载中...',
  emptyText: '暂无数据',
  showToolbar: true,
  showLegend: false,
  legendData: () => [],
  responsive: true,
  animation: true,
  theme: 'auto'
})

const emit = defineEmits<{
  refresh: []
  export: [type: string]
  legendClick: [item: LegendItem, index: number]
  chartClick: [event: any]
  chartReady: [chart: any]
}>()

// 响应式数据
const chartRef = ref<HTMLElement>()
const chartInstance = ref<any>(null)
const resizeObserver = ref<ResizeObserver>()

// 计算属性
const isEmpty = computed(() => {
  if (!props.data) return true
  
  // 检查不同数据格式
  if (props.data.datasets) {
    return props.data.datasets.length === 0 || 
           props.data.datasets.every(dataset => !dataset.data || dataset.data.length === 0)
  }
  
  if (props.data.series) {
    return props.data.series.length === 0 ||
           props.data.series.every(series => !series.data || series.data.length === 0)
  }
  
  return Object.keys(props.data).length === 0
})

const chartClass = computed(() => ({
  'chart-loading-state': props.loading,
  'chart-empty-state': isEmpty.value,
  'chart-error-state': !!props.error,
  [`chart-theme-${props.theme}`]: true
}))

const containerStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  position: 'relative' as const
}))

// 方法
const initChart = async () => {
  if (!chartRef.value || !props.data || isEmpty.value) return

  try {
    // 这里可以集成不同的图表库
    // 例如: ECharts, Chart.js, D3.js 等
    await createChart()
  } catch (error) {
    console.error('Chart initialization failed:', error)
    emit('refresh')
  }
}

const createChart = async () => {
  // 模拟图表创建
  // 实际实现中这里会根据图表类型创建相应的图表实例
  
  const mockChart = {
    resize: () => {},
    dispose: () => {},
    setOption: (options: any) => {},
    getDataURL: () => 'data:image/png;base64,mock'
  }
  
  chartInstance.value = mockChart
  emit('chartReady', mockChart)
}

const updateChart = () => {
  if (!chartInstance.value || !props.data) return
  
  // 更新图表数据和配置
  const options = {
    ...props.options,
    animation: props.animation
  }
  
  chartInstance.value.setOption?.(options)
}

const resizeChart = () => {
  if (chartInstance.value && props.responsive) {
    chartInstance.value.resize?.()
  }
}

const destroyChart = () => {
  if (chartInstance.value) {
    chartInstance.value.dispose?.()
    chartInstance.value = null
  }
}

const handleRefresh = () => {
  emit('refresh')
}

const handleExport = () => {
  if (chartInstance.value) {
    const dataURL = chartInstance.value.getDataURL?.()
    if (dataURL) {
      const link = document.createElement('a')
      link.download = `${props.title || 'chart'}.png`
      link.href = dataURL
      link.click()
    }
  }
  emit('export', 'png')
}

const handleToolbarCommand = (command: string) => {
  switch (command) {
    case 'fullscreen':
      toggleFullscreen()
      break
    case 'settings':
      // 打开设置面板
      break
    case 'reset':
      // 重置图表
      initChart()
      break
  }
}

const handleLegendClick = (item: LegendItem, index: number) => {
  emit('legendClick', item, index)
}

const toggleFullscreen = () => {
  const element = chartRef.value?.parentElement
  if (!element) return
  
  if (!document.fullscreenElement) {
    element.requestFullscreen?.()
  } else {
    document.exitFullscreen?.()
  }
}

const formatValue = (value: number | string) => {
  if (typeof value === 'number') {
    return new Intl.NumberFormat().format(value)
  }
  return value
}

const setupResizeObserver = () => {
  if (!props.responsive || !chartRef.value) return
  
  resizeObserver.value = new ResizeObserver(() => {
    resizeChart()
  })
  
  resizeObserver.value.observe(chartRef.value)
}

// 监听器
watch(() => props.data, () => {
  nextTick(() => {
    if (isEmpty.value) {
      destroyChart()
    } else {
      updateChart()
    }
  })
}, { deep: true })

watch(() => props.options, () => {
  updateChart()
}, { deep: true })

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
    setupResizeObserver()
  })
})

onUnmounted(() => {
  destroyChart()
  resizeObserver.value?.disconnect()
})

// 暴露方法
defineExpose({
  refresh: initChart,
  resize: resizeChart,
  export: handleExport,
  getChart: () => chartInstance.value
})
</script>

<style scoped>
.base-chart {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  overflow: hidden;
  transition: all 0.3s ease;
}

.base-chart:hover {
  box-shadow: var(--shadow-md);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-secondary);
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.chart-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-container {
  position: relative;
  padding: 20px;
}

.chart-canvas {
  position: relative;
  z-index: 1;
}

.chart-loading,
.chart-empty,
.chart-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: var(--text-secondary);
  z-index: 2;
}

.loading-icon,
.empty-icon,
.error-icon {
  font-size: 32px;
  opacity: 0.6;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

.chart-loading span,
.chart-empty span,
.chart-error span {
  font-size: 14px;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px 20px;
  border-top: 1px solid var(--border-light);
  background: var(--bg-secondary);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.legend-item:hover {
  background: var(--bg-tertiary);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
}

.legend-label {
  font-size: 14px;
  color: var(--text-primary);
}

.legend-value {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

/* 主题样式 */
.chart-theme-dark {
  --chart-bg: #1f2937;
  --chart-text: #f9fafb;
  --chart-border: #374151;
}

.chart-theme-light {
  --chart-bg: #ffffff;
  --chart-text: #1f2937;
  --chart-border: #e5e7eb;
}

/* 状态样式 */
.chart-loading-state .chart-canvas {
  opacity: 0.3;
  pointer-events: none;
}

.chart-empty-state .chart-canvas,
.chart-error-state .chart-canvas {
  opacity: 0.1;
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .chart-toolbar {
    width: 100%;
    justify-content: flex-end;
  }

  .chart-container {
    padding: 16px;
  }

  .chart-legend {
    padding: 12px 16px;
    gap: 12px;
  }

  .legend-item {
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .chart-header {
    padding: 12px 16px;
  }

  .chart-title {
    font-size: 14px;
  }

  .chart-container {
    padding: 12px;
  }

  .chart-legend {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* 全屏样式 */
:fullscreen .base-chart {
  width: 100vw !important;
  height: 100vh !important;
  border-radius: 0;
  border: none;
}

:fullscreen .chart-container {
  height: calc(100vh - 120px);
}

:fullscreen .chart-canvas {
  height: 100% !important;
}
</style>
