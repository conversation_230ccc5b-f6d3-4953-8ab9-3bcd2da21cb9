apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: neo4j-pvc
spec:
  # Minikube 默认提供 'standard' 存储类，它会自动创建 PersistentVolume
  storageClassName: standard
  accessModes:
    # ReadWriteOnce 意味着这个卷一次只能被一个节点挂载为读写模式，
    # 这对于单实例的数据库来说是标准配置。
    - ReadWriteOnce
  resources:
    requests:
      # 请求 2Gi 的存储空间，你可以根据需要调整大小。
      storage: 2Gi

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: neo4j-deployment
  labels:
    app: neo4j
spec:
  replicas: 1
  selector:
    matchLabels:
      app: neo4j
  template:
    metadata:
      labels:
        app: neo4j
    spec:
      securityContext:
        fsGroup: 7474
      containers:
        - name: neo4j
          # 使用官方的 Neo4j 5 Community Edition 镜像
          image: neo4j:5-community
          # 确保拉取策略，以便在镜像有更新时能拉取到
          imagePullPolicy: IfNotPresent
          ports:
            # Bolt 是 Neo4j 的二进制协议，用于驱动程序连接
            - containerPort: 7687
              name: bolt
            # HTTP 用于浏览器和 REST API
            - containerPort: 7474
              name: http
          env:
            - name: NEO4J_AUTH
              value: "neo4j/supersecret" # 用户名是 neo4j，密码是 supersecret
            # 接受许可协议
            - name: NEO4J_ACCEPT_LICENSE_AGREEMENT
              value: "yes"
            - name: NEO4J_server_config_strict__validation_enabled
              value: "false"
          volumeMounts:
            # 将持久化卷挂载到容器内的 /data 目录，这是 Neo4j 存储数据库文件的地方。
            - name: neo4j-data
              mountPath: /data
      volumes:
        # 声明一个卷，它的数据源是我们上面定义的 PVC
        - name: neo4j-data
          persistentVolumeClaim:
            claimName: neo4j-pvc

---
# ===================================================================
#  Neo4j Service
#  为 Neo4j Pod 提供一个稳定、统一的网络入口。
# ===================================================================
apiVersion: v1
kind: Service
metadata:
  name: neo4j-svc
spec:
  # 使用 NodePort 类型，这样我们可以从 Minikube 外部访问 Neo4j Browser UI 进行调试。
  # 在生产环境中，对于数据库通常会使用 ClusterIP。
  type: NodePort
  selector:
    # 这个 selector 必须和 Deployment template 中的 labels 匹配
    app: neo4j
  ports:
    - name: http
      protocol: TCP
      # Service 在集群内部暴露的端口
      port: 7474
      # 容器实际监听的端口
      targetPort: 7474
    - name: bolt
      protocol: TCP
      port: 7687
      targetPort: 7687