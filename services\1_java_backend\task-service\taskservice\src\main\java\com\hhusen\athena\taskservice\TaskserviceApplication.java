package com.hhusen.athena.taskservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.mlflow.tracking.MlflowClient;
import org.mlflow.api.proto.Service.RunInfo;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Optional;

@RestController
@SpringBootApplication
@RequestMapping("/tasks")
public class TaskserviceApplication {

	@Autowired
	private TaskRepository taskRepository;

	@Autowired
	private RabbitTemplate rabbitTemplate;
	
	@Autowired
	private MlflowClient mlflowClient;
	
	@Value("${mlflow.tracking.uri}")
	private String mlflowTrackingUri;

	public static void main(String[] args) {
		SpringApplication.run(TaskserviceApplication.class, args);
	}

	@GetMapping("/")
	public String home() {
		return "Hello from Athena Task Service!";
	}

	@GetMapping("/health")
	public String health() {
		return "OK";
	}

	@PostMapping
	public Task createTask(@RequestBody Task task) {
		// 创建MLflow实验运行
		String experimentId = "0"; // 使用默认实验ID
		RunInfo runInfo = mlflowClient.createRun(experimentId);
		String runId = runInfo.getRunId();
		
		// 将MLflow运行ID设置到Task对象中
		task.setMlflowRunId(runId);
		
		// 确保task对象包含rainfall、temperature和population字段
		// rainfall、temperature和population字段已经从请求体中的Task对象获取
		
		// 保存Task到数据库
		Task savedTask = taskRepository.save(task);
		
		// 创建消息，包含MLflow运行ID、taskId、rainfall、temperature、population、time_of_day和initialWaterLevel
		Map<String, Object> message = new HashMap<>();
		message.put("id", savedTask.getId());
		message.put("name", savedTask.getName());
		message.put("mlflowRunId", savedTask.getMlflowRunId());
		message.put("rainfall", savedTask.getRainfall());
		message.put("temperature", savedTask.getTemperature());
		message.put("population", savedTask.getPopulation());
		message.put("cityName", savedTask.getCityName()); // 添加城市名称
		message.put("initialWaterLevel", savedTask.getInitialWaterLevel()); // 添加初始水位
		message.put("timeOfDay", "day");
		
		// 发送消息到RabbitMQ
		rabbitTemplate.convertAndSend("simulation_tasks", message);
		
		return savedTask;
	}
	
	@PutMapping("/{id}/water-level")
	public Task updateWaterLevel(@PathVariable Long id, @RequestBody Map<String, Double> payload) {
		Double waterLevel = payload.get("waterLevel");
		
		Optional<Task> taskOptional = taskRepository.findById(id);
		if (!taskOptional.isPresent()) {
			throw new RuntimeException("Task not found with id: " + id);
		}
		
		Task task = taskOptional.get();
		task.setWaterLevel(waterLevel);
		
		// 更新状态为已完成
		task.setStatus("COMPLETED");
		
		return taskRepository.save(task);
	}

	@GetMapping
	public List<Task> getAllTasks() {
		return taskRepository.findAll();
	}
	
	@GetMapping("/{id}")
	public Task getTaskById(@PathVariable Long id) {
		Optional<Task> taskOptional = taskRepository.findById(id);
		if (!taskOptional.isPresent()) {
			throw new RuntimeException("Task not found with id: " + id);
		}
		return taskOptional.get();
	}
}
