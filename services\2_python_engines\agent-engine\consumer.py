import pika
import sys
import os
import time
import signal
import json
import mlflow
import tempfile
import datetime
from agent_logic import ResidentAgent

# 设置MLflow Tracking Server的URI
MLFLOW_TRACKING_URI = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-svc:5000')
mlflow.set_tracking_uri(MLFLOW_TRACKING_URI)

# Java后端服务地址
TASK_SERVICE_URL = os.getenv('TASK_SERVICE_URL', 'http://taskservice-svc:8080')

# 最大决策循环次数
MAX_DECISION_CYCLES = int(os.getenv('MAX_DECISION_CYCLES', '5'))

class GracefulKiller:
    """
    这个类用于捕获系统的终止信号 (SIGINT, SIGTERM)，
    以便让程序能够优雅地关闭，而不是被强制杀死。
    这对于在 Kubernetes 或 Docker 中运行至关重要。
    """
    kill_now = False
    def __init__(self):
        signal.signal(signal.SIGINT, self.exit_gracefully)
        signal.signal(signal.SIGTERM, self.exit_gracefully)

    def exit_gracefully(self, signum, frame):
        print(f"--- Received signal {signum}, shutting down gracefully. ---")
        self.kill_now = True

class AgentEngine:
    """
    智能体引擎类，处理RabbitMQ消息，调用智能体逻辑，并管理消息流
    """
    def __init__(self, rabbitmq_host='rabbitmq-svc'):
        # 初始化ResidentAgent（将在接收到消息时根据cityName重新初始化）
        self.agent = None
        print("--- AgentEngine 已初始化，等待接收城市信息 ---")
        
        # 保存RabbitMQ连接信息
        self.rabbitmq_host = rabbitmq_host
        self.connection = None
        self.channel = None
        
        # 任务状态跟踪
        self.active_tasks = {}  # 用于跟踪活跃的任务 {task_id: task_info}
        
    def connect(self):
        """建立到RabbitMQ的连接"""
        print(f"[*] Trying to connect to RabbitMQ at {self.rabbitmq_host}...")
        self.connection = pika.BlockingConnection(
            pika.ConnectionParameters(
                host=self.rabbitmq_host,
                heartbeat=600,
                blocked_connection_timeout=300
            )
        )
        self.channel = self.connection.channel()
        
        # 声明所需的队列
        self.channel.queue_declare(queue='simulation_tasks', durable=True)
        self.channel.queue_declare(queue='agent_actions', durable=True)
        self.channel.queue_declare(queue='environment_updates', durable=True)
        
        print('[*] Connection successful. Waiting for messages.')
    
    def handle_simulation_task(self, ch, method, properties, body):
        """处理从simulation_tasks队列接收到的消息"""
        print(" [x] Received a message from simulation_tasks queue.")
        try:
            # 解析消息
            message_str = body.decode('utf-8')
            message = json.loads(message_str)
            print(f"     [+] Parsed JSON: {message}")
            
            # 提取任务信息
            task_id = message.get('id', 'N/A')
            task_name = message.get('name', 'N/A')
            mlflow_run_id = message.get('mlflowRunId')
            population = message.get('population')
            time_of_day = message.get('timeOfDay', 'day')
            city_name = message.get('cityName', 'Rivertown')  # 新增城市名称，默认为Rivertown
            initial_water_level = message.get('initialWaterLevel')  # 新增初始水位
            
            print(f"     [>] Processing Task ID: {task_id}, Name: {task_name}")
            print(f"     [>] Input Parameters: Population: {population}, Time of Day: {time_of_day}, City: {city_name}, Initial Water Level: {initial_water_level}")
            
            # 根据城市名称初始化ResidentAgent
            self.agent = ResidentAgent(city_name)
            print(f"     [>] 已为城市 '{city_name}' 初始化ResidentAgent")
            
            # 如果提供了初始水位，设置到智能体中
            if initial_water_level is not None:
                self.agent.current_water_level = float(initial_water_level)
                print(f"     [>] 设置初始水位为: {initial_water_level}m")
                # 新增：立即根据初始水位调整适应因子
                self.agent.update_environment_state(float(initial_water_level))
            
            # 保存任务信息到活跃任务字典
            self.active_tasks[task_id] = {
                'task_id': task_id,
                'task_name': task_name,
                'mlflow_run_id': mlflow_run_id,
                'population': population,
                'time_of_day': time_of_day,
                'city_name': city_name,  # 新增城市名称
                'start_time': time.time()
            }
            
            # 确保当前没有活跃的run
            if mlflow.active_run():
                mlflow.end_run()
                
            # 使用MLflow记录实验
            with mlflow.start_run(run_id=mlflow_run_id) as run:
                print(f"     [MLflow] Started run with ID: {run.info.run_id}")
                
                # 记录参数 - 使用独特的参数名
                mlflow.log_param("task_id", task_id)
                mlflow.log_param("task_name", task_name)
                mlflow.log_param("agent_processed_at", datetime.datetime.now().isoformat())
                if population is not None:
                    mlflow.log_param("population", population)
                mlflow.log_param("time_of_day", time_of_day)
                mlflow.log_param("city_name", city_name)  # 新增城市名称
                
                # 调用智能体计算用水量
                self.calculate_water_usage(task_id)
                
        except json.JSONDecodeError as e:
            print(f"     [!] Failed to decode JSON: {e}")
            print(f"     [!] Raw message body: {body}")
        except Exception as e:
            print(f"     [!] Error processing message: {e}")
    
    def calculate_water_usage(self, task_id):
        """调用智能体逻辑计算用水量并发送到agent_actions队列"""
        task = self.active_tasks.get(task_id)
        if not task:
            print(f"     [!] Task {task_id} not found in active tasks")
            return
        
        try:
            # 获取任务信息
            population = task['population']
            time_of_day = task['time_of_day']
            mlflow_run_id = task['mlflow_run_id']
            city_name = task['city_name']  # 新增城市名称
            cycle_count = task.get('cycle_count', 0)
            current_water_level = task.get('water_level', self.agent.current_water_level)
            
            print(f"     [>] 使用ResidentAgent计算用水量...")
            print(f"     [>] 决策循环 {cycle_count}, 人口: {population}, 时间: {time_of_day}, 城市: {city_name}, 当前水位: {current_water_level}")
            
            # 调用智能体逻辑计算用水量
            net_water_withdrawal = self.agent.decide_water_usage(population, time_of_day)
            print(f"     [>] 计算得到净取水量: {net_water_withdrawal}")
            
            # 确保当前没有活跃的run
            if mlflow.active_run():
                mlflow.end_run()
            
            # 使用前缀区分不同循环的参数
            cycle_prefix = f"cycle_{cycle_count}_" if cycle_count > 0 else ""
                
            # 使用现有的run_id记录指标
            with mlflow.start_run(run_id=mlflow_run_id):
                # 记录循环信息
                mlflow.log_metric(f"{cycle_prefix}net_water_withdrawal", net_water_withdrawal)
                mlflow.log_param(f"{cycle_prefix}adaptation_factor", self.agent.adaptation_factor)
                mlflow.log_param(f"{cycle_prefix}current_water_level", current_water_level)
                
                # 记录政策状态
                mlflow.log_param(f"{cycle_prefix}policy_triggered", self.agent.policy_triggered)
                mlflow.log_param(f"{cycle_prefix}applied_policy_name", self.agent.applied_policy_name or "none")
                mlflow.log_param(f"{cycle_prefix}trigger_condition_met", self.agent.trigger_condition_met)
            
            # 发送结果到agent_actions队列
            self.send_to_agent_actions(task_id, mlflow_run_id, net_water_withdrawal)
            
            # 记录到任务状态
            task['net_water_withdrawal'] = net_water_withdrawal
            # 保存特定循环的用水量数据，用于报告
            task[f'net_water_withdrawal_cycle_{cycle_count}'] = net_water_withdrawal
            
        except Exception as e:
            print(f"     [!] 计算用水量出错: {e}")
            # 记录失败
            # 确保当前没有活跃的run
            if mlflow.active_run():
                mlflow.end_run()
                
            with mlflow.start_run(run_id=task['mlflow_run_id']):
                mlflow.log_metric("error", 1)
    
    def send_to_agent_actions(self, task_id, mlflow_run_id, net_water_withdrawal):
        """发送消息到agent_actions队列"""
        try:
            print(f"     [>] 发送净取水量到RabbitMQ的agent_actions队列")
            
            # 创建消息
            message = {
                "taskId": task_id,
                "mlflowRunId": mlflow_run_id,
                "netWaterWithdrawal": net_water_withdrawal
            }
            
            # 发送消息
            self.channel.basic_publish(
                exchange='',
                routing_key='agent_actions',
                body=json.dumps(message)
            )
            
            print(f"     [>] 成功发送净取水量到agent_actions队列")
            
            # 记录发送时间
            self.active_tasks[task_id]['action_sent_time'] = time.time()
            
        except Exception as e:
            print(f"     [!] 发送净取水量出错: {e}")
    
    def handle_environment_update(self, ch, method, properties, body):
        """处理从environment_updates队列接收到的消息"""
        print(" [x] Received a message from environment_updates queue.")
        try:
            # 解析消息
            message_str = body.decode('utf-8')
            message = json.loads(message_str)
            print(f"     [+] Parsed JSON: {message}")
            
            # 提取信息
            task_id = message.get('taskId', 'N/A')
            mlflow_run_id = message.get('mlflowRunId')
            water_level = message.get('waterLevel')
            
            print(f"     [>] 收到环境更新：Task ID: {task_id}, Water Level: {water_level}")
            
            # 检查任务是否存在
            if task_id in self.active_tasks:
                task = self.active_tasks[task_id]
                
                # 更新任务状态
                task['water_level'] = water_level
                # 保存特定循环的水位数据，用于报告
                cycle_count = task.get('cycle_count', 0)
                task[f'water_level_cycle_{cycle_count}'] = water_level
                task['update_received_time'] = time.time()
                
                # 确保当前没有活跃的run
                if mlflow.active_run():
                    mlflow.end_run()
                
                # 获取当前循环计数
                cycle_prefix = f"cycle_{cycle_count}_" if cycle_count > 0 else ""
                
                # 使用MLflow记录水位
                with mlflow.start_run(run_id=mlflow_run_id):
                    mlflow.log_metric(f"{cycle_prefix}water_level_received", water_level)
                    mlflow.log_param(f"{cycle_prefix}env_update_received_at", datetime.datetime.now().isoformat())
                
                # 创建结果报告
                self.create_task_report(task_id)
                
                # 更新智能体的环境状态
                self.agent.update_environment_state(water_level)
                
                # 保存更新后的适应因子，用于报告
                task[f'adaptation_factor_cycle_{cycle_count}'] = self.agent.adaptation_factor
                
                # 基于更新后的环境状态开始下一轮决策
                self.start_next_decision_cycle(task_id)
            else:
                print(f"     [!] Task {task_id} not found in active tasks")
                
        except json.JSONDecodeError as e:
            print(f"     [!] Failed to decode JSON: {e}")
            print(f"     [!] Raw message body: {body}")
        except Exception as e:
            print(f"     [!] Error processing message: {e}")
    
    def start_next_decision_cycle(self, task_id):
        """基于更新后的环境状态开始下一轮决策循环"""
        task = self.active_tasks.get(task_id)
        if not task:
            return
            
        try:
            print(f"     [>] 开始下一轮决策循环，Task ID: {task_id}")
            
            # 获取任务信息
            population = task['population']
            time_of_day = task['time_of_day']
            mlflow_run_id = task['mlflow_run_id']
            city_name = task['city_name'] # 新增城市名称
            cycle_count = task.get('cycle_count', 0) + 1
            
            # 更新循环计数
            task['cycle_count'] = cycle_count
            
            # 如果循环次数超过限制，则停止
            if cycle_count > MAX_DECISION_CYCLES:
                print(f"     [>] 已达到最大循环次数 {MAX_DECISION_CYCLES}，停止决策循环")
                return
                
            print(f"     [>] 决策循环 {cycle_count}/{MAX_DECISION_CYCLES}")
            
            # 使用MLflow记录循环信息
            with mlflow.start_run(run_id=mlflow_run_id):
                mlflow.log_metric("decision_cycle", cycle_count)
                mlflow.log_param(f"cycle_{cycle_count}_start_time", datetime.datetime.now().isoformat())
            
            # 基于更新后的环境状态重新计算用水量
            self.calculate_water_usage(task_id)
            
        except Exception as e:
            print(f"     [!] 启动下一轮决策循环出错: {e}")
            # 记录失败
            with mlflow.start_run(run_id=task['mlflow_run_id']):
                mlflow.log_metric("error", 1)
    
    def create_task_report(self, task_id):
        """为任务创建详细报告并记录到MLflow"""
        task = self.active_tasks.get(task_id)
        if not task:
            return
        
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(prefix="agent_report_", suffix=".txt", delete=False) as temp_file:
                result_file_path = temp_file.name
                
                # 计算处理时间
                processing_time = time.time() - task['start_time']
                cycle_count = task.get('cycle_count', 0)
                
                # 创建报告内容
                result_content = f"""Task ID: {task['task_id']}
Task Name: {task.get('task_name', 'N/A')}
Input Parameters:
- Population: {task['population']}
- Time of Day: {task['time_of_day']}
- City: {task['city_name']}
Agent Output:
- Net Water Withdrawal: {task.get('net_water_withdrawal')} units
Environment Response:
- Water Level: {task.get('water_level')} m
Processing Time: {processing_time:.2f} seconds
Status: {'Success' if 'water_level' in task else 'Pending'}
Cycle Count: {cycle_count}
Adaptation Factor: {self.agent.adaptation_factor:.2f}
Policy Status: {self.agent.policy_triggered} - {self.agent.applied_policy_name or 'None'}
Timestamp: {datetime.datetime.now().isoformat()}
"""
                # 添加多轮交互的历史记录
                if cycle_count > 0:
                    result_content += "\nDecision Cycle History:\n"
                    for i in range(1, cycle_count + 1):
                        result_content += f"Cycle {i}:\n"
                        if i > 1:
                            result_content += f"- Previous Water Level: {task.get(f'water_level_cycle_{i-1}', 'N/A')} m\n"
                        result_content += f"- Adaptation Factor: {task.get(f'adaptation_factor_cycle_{i}', 'N/A')}\n"
                        result_content += f"- Net Water Withdrawal: {task.get(f'net_water_withdrawal_cycle_{i}', 'N/A')} units\n"
                        result_content += f"- Resulting Water Level: {task.get(f'water_level_cycle_{i}', 'N/A')} m\n"
                
                temp_file.write(result_content.encode('utf-8'))
            
            # 确保当前没有活跃的run
            if mlflow.active_run():
                mlflow.end_run()
                
            # 记录到MLflow
            with mlflow.start_run(run_id=task['mlflow_run_id']):
                # 记录处理时间
                mlflow.log_metric("processing_time", processing_time)
                
                # 记录循环次数
                mlflow.log_metric("cycle_count", cycle_count)
                
                # 记录最终适应因子
                mlflow.log_metric("final_adaptation_factor", self.agent.adaptation_factor)
                
                # 记录政策状态
                mlflow.log_param("final_policy_triggered", self.agent.policy_triggered)
                mlflow.log_param("final_applied_policy_name", self.agent.applied_policy_name or "none")
                mlflow.log_param("final_trigger_condition_met", self.agent.trigger_condition_met)
                
                # 记录报告文件
                print(f"     [MLflow] Logging artifact: {result_file_path}")
                mlflow.log_artifact(result_file_path)
                
                # 记录成功状态
                mlflow.log_metric("success", 1 if 'water_level' in task else 0)
            
            # 删除临时文件
            os.unlink(result_file_path)
            
            print(f"     [MLflow] Task report completed for task {task_id}")
            
        except Exception as e:
            print(f"     [!] Error creating task report: {e}")
    
    def start_consuming(self):
        """开始消费消息"""
        # 订阅simulation_tasks队列
        self.channel.basic_consume(
            queue='simulation_tasks',
            on_message_callback=self.handle_simulation_task,
            auto_ack=True
        )
        
        # 订阅environment_updates队列
        self.channel.basic_consume(
            queue='environment_updates',
            on_message_callback=self.handle_environment_update,
            auto_ack=True
        )
        
        print("[*] Started consuming messages. To exit press CTRL+C")
    
    def process_events(self, time_limit=1):
        """处理事件循环"""
        self.connection.process_data_events(time_limit=time_limit)
    
    def close(self):
        """关闭连接"""
        if self.connection and self.connection.is_open:
            print("--- Closing connection to RabbitMQ. ---")
            self.connection.close()

def main():
    # 在脚本启动时打印版本号，方便调试
    print("--- consumer.py script started (v0.1.1) ---")
    print(f"--- MLflow Tracking URI: {MLFLOW_TRACKING_URI} ---")
    
    killer = GracefulKiller()
    # 从环境变量读取 RabbitMQ 主机名，如果不存在则使用默认值 'rabbitmq-svc'
    rabbitmq_host = os.getenv('RABBITMQ_HOST', 'rabbitmq-svc')
    
    agent_engine = None
    
    # 使用循环来处理连接失败和重试
    while not killer.kill_now:
        try:
            # 创建并初始化AgentEngine
            agent_engine = AgentEngine(rabbitmq_host=rabbitmq_host)
            
            # 连接到RabbitMQ
            agent_engine.connect()
            
            # 开始消费消息
            agent_engine.start_consuming()
            
            # 使用非阻塞的循环来处理事件，以便能响应终止信号
            while not killer.kill_now:
                # 处理网络事件，超时时间为1秒
                agent_engine.process_events(time_limit=1)
                
        except pika.exceptions.AMQPConnectionError as e:
            # 如果连接失败，并且没有收到终止信号，则等待5秒后重试
            if killer.kill_now:
                break
            print(f" [!] Connection to RabbitMQ failed: {e}. Retrying in 5 seconds...")
            time.sleep(5)
        except Exception as e:
            # 捕获其他未知异常
            if killer.kill_now:
                break
            print(f" [!] An unexpected error occurred: {e}. Retrying in 5 seconds...")
            time.sleep(5)
        finally:
            # 关闭连接
            if agent_engine:
                agent_engine.close()
    
    print("--- consumer.py script ended. ---")

if __name__ == '__main__':
    main() 