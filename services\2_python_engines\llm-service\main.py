import os
from fastapi import FastAP<PERSON>
from pydantic import BaseModel
from fastapi.responses import StreamingResponse
import asyncio

import mlflow
from mlflow.tracking import MlflowClient
import requests
import json
from openai import OpenAI
import chromadb
from langchain_community.embeddings import HuggingFaceEmbeddings
from typing import Optional
import logging

# 配置日志记录
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 设置MLflow和requests的日志级别为DEBUG
logging.getLogger('mlflow').setLevel(logging.DEBUG)
logging.getLogger('requests').setLevel(logging.DEBUG)
logging.getLogger('urllib3').setLevel(logging.DEBUG)

# 创建自定义logger
logger = logging.getLogger('llm-service')
logger.setLevel(logging.DEBUG)

# 从环境变量读取配置
MLFLOW_TRACKING_URI = os.getenv("MLFLOW_TRACKING_URI", "http://mlflow-svc:5000")
KNOWLEDGE_SERVICE_URL = os.getenv("KNOWLEDGE_SERVICE_URL", "http://knowledge-service:80")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
DASHSCOPE_API_KEY = os.getenv("DASHSCOPE_API_KEY", "")
QWEN_BASE_URL = os.getenv("QWEN_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
TASK_SERVICE_URL = os.getenv("TASK_SERVICE_URL", "http://task-service-svc:80")

# 1. 服务连接信息

# 2. MLflow 客户端初始化
mlflow.set_tracking_uri(MLFLOW_TRACKING_URI)
mlflow_client = MlflowClient()

# 3. 通义千问客户端初始化 - 使用OpenAI兼容模式
# 使用测试成功的配置
qwen_client = OpenAI(
    api_key=DASHSCOPE_API_KEY,
    base_url=QWEN_BASE_URL  # 已更新为正确的兼容模式URL
)

# 4. 开源嵌入模型初始化
embeddings_model = HuggingFaceEmbeddings(
    model_name="/app/models/all-MiniLM-L6-v2"
)

# 5. ChromaDB 客户端初始化
chroma_client = chromadb.HttpClient(host="chromadb-svc", port=8000)
knowledge_collection = chroma_client.get_or_create_collection(name="athena_knowledge_base")

def fetch_task_data(task_id: int):
    """
    从task-service API获取指定task_id的任务数据，包含重试机制
    """
    import time
    
    # 重试配置
    max_retries = 5
    retry_interval = 2  # 秒
    
    for attempt in range(max_retries):
        try:
            logger.info(f"正在从 task-service 获取任务ID为 {task_id} 的数据... (第 {attempt + 1}/{max_retries} 次尝试)")
            
            response = requests.get(f"{TASK_SERVICE_URL}/tasks/{task_id}", timeout=10)
            response.raise_for_status()
            
            task_data = response.json()
            logger.info(f"成功从 task-service 获取到任务数据: {task_data}")
            
            # 检查是否包含 mlflowRunId
            mlflow_run_id = task_data.get("mlflowRunId")
            if mlflow_run_id and mlflow_run_id.strip():
                logger.info(f"任务数据中包含有效的 mlflowRunId: {mlflow_run_id}")
                return task_data
            else:
                logger.warning(f"任务数据中缺少或 mlflowRunId 为空，将在 {retry_interval} 秒后重试...")
                if attempt < max_retries - 1:  # 不是最后一次尝试
                    time.sleep(retry_interval)
                    continue
                else:
                    logger.error(f"在 {max_retries} 次尝试后仍未获取到有效的 mlflowRunId")
                    return task_data  # 返回不完整的数据，让上层处理
                    
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 404:
                logger.error(f"任务ID {task_id} 不存在: {e}")
                return None
            logger.error(f"从 task-service 获取数据时发生HTTP错误: {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_interval)
                continue
            else:
                logger.error(f"在 {max_retries} 次尝试后仍无法从 task-service 获取数据")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"无法连接到 task-service: {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_interval)
                continue
            else:
                logger.error(f"在 {max_retries} 次尝试后仍无法连接到 task-service")
                return None
                
        except Exception as e:
            logger.error(f"获取任务数据时发生未知错误: {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_interval)
                continue
            else:
                logger.error(f"在 {max_retries} 次尝试后仍无法获取任务数据")
                return None
    
    return None

def fetch_mlflow_data(run_id: str):
    """
    从MLflow获取指定run_id的参数和指标
    """
    try:
        logger.info(f"尝试获取MLflow运行数据，run_id: {run_id}")
        
        # 首先通过search_runs查找特定run_id，这种方式更可靠
        try:
            runs = mlflow_client.search_runs(
                experiment_ids=["0"],  # 默认使用ID=0的实验
                filter_string=f"run_id = '{run_id}'",
                max_results=1
            )
            
            if runs:
                run = runs[0]  # 使用search_runs返回的结果
                logger.info(f"通过search_runs成功找到run_id为{run_id}的运行记录")
            else:
                logger.warning(f"通过search_runs未找到run_id为{run_id}的运行记录，尝试直接获取")
                # 回退到直接获取
                run = mlflow_client.get_run(run_id)
        except Exception as search_error:
            logger.warning(f"通过search_runs查找run_id时出错: {search_error}，尝试直接获取")
            # 回退到直接获取
            run = mlflow_client.get_run(run_id)
        
        # 记录获取到的数据结构以便调试
        logger.info(f"成功获取run_id为{run_id}的数据")
        logger.debug(f"获取到的MLflow运行数据: {run}")
        
        # 安全地提取参数
        params = {}
        if hasattr(run.data, 'params') and run.data.params:
            params = run.data.params
            logger.info(f"参数: 发现{len(params)}个参数")
            # 记录部分关键参数供调试
            key_params = {k: v for k, v in params.items() if not k.startswith("cycle_")}
            logger.info(f"关键参数: {key_params}")
        else:
            logger.warning("警告: MLflow运行记录中没有参数数据")
        
        # 安全地提取指标
        metrics = {}
        if hasattr(run.data, 'metrics') and run.data.metrics:
            metrics = {k: v for k, v in run.data.metrics.items()}
            logger.info(f"指标: 发现{len(metrics)}个指标")
            # 记录部分关键指标供调试
            key_metrics = {k: v for k, v in metrics.items() if not k.startswith("cycle_")}
            logger.info(f"关键指标: {key_metrics}")
        else:
            logger.warning("警告: MLflow运行记录中没有指标数据")
        
        # 提取多轮交互数据（以cycle_前缀开头的参数和指标）
        cycle_params = {}
        cycle_metrics = {}
        
        for k, v in params.items():
            if k.startswith("cycle_"):
                parts = k.split("_")
                if len(parts) > 1:
                    cycle_number = parts[1]
                    if cycle_number not in cycle_params:
                        cycle_params[cycle_number] = {}
                    cycle_params[cycle_number][k] = v
        
        for k, v in metrics.items():
            if k.startswith("cycle_"):
                parts = k.split("_")
                if len(parts) > 1:
                    cycle_number = parts[1]
                    if cycle_number not in cycle_metrics:
                        cycle_metrics[cycle_number] = {}
                    cycle_metrics[cycle_number][k] = v
        
        if cycle_params or cycle_metrics:
            logger.info(f"发现多轮交互数据，循环数: {max(len(cycle_params), len(cycle_metrics))}")
        
        result = {
            "params": params,
            "metrics": metrics,
            "cycle_data": {
                "params": cycle_params,
                "metrics": cycle_metrics
            },
            "tags": run.data.tags if hasattr(run.data, 'tags') else {},
            "start_time": run.info.start_time if hasattr(run.info, 'start_time') else None,
            "end_time": run.info.end_time if hasattr(run.info, 'end_time') else None
        }
        
        logger.debug(f"处理后的MLflow数据结构: {result}")
        return result
    except Exception as e:
        logger.error(f"获取MLflow数据错误: {e}", exc_info=True)
        # 返回空数据结构而不是模拟数据，以准确反映实际情况
        return {
            "params": {},
            "metrics": {},
            "cycle_data": {
                "params": {},
                "metrics": {}
            },
            "tags": {},
            "start_time": None,
            "end_time": None
        }

def fetch_knowledge_data(city_name: str):
    """
    从knowledge-service API获取指定城市的政策信息
    """
    try:
        response = requests.get(f"{KNOWLEDGE_SERVICE_URL}/api/policies/{city_name}")
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"获取知识服务数据错误: {e}")
        # 返回模拟数据用于测试
        return {
            "name": "节水政策",
            "water_saving_factor": 0.8,
            "description": "减少用水量的强制性政策",
            "city": city_name
        }

def build_enhanced_prompt(context: dict) -> str:
    """
    构建增强的提示词，包含思维链(CoT)、符号推理和RAG检索的证据
    
    Args:
        context: 包含任务、MLflow、知识数据和RAG证据的字典
        
    Returns:
        str: 格式化的提示字符串
    """
    # 确保context中包含所有必要的键，防止KeyError
    task = context.get("task", {}) or {}
    mlflow_data = context.get("mlflow_data", {}) or {}
    knowledge_data = context.get("knowledge_data", {}) or {}
    rag_evidence = context.get("rag_evidence", "")
    user_question = context.get("user_question", "无特定问题，请进行全面分析。")
    
    # 从mlflow_data中提取参数和指标
    params = mlflow_data.get("params", {}) or {}
    metrics = mlflow_data.get("metrics", {}) or {}
    cycle_data = mlflow_data.get("cycle_data", {"params": {}, "metrics": {}})
    
    # 检查是否有多轮交互数据
    has_cycle_data = bool(cycle_data and (cycle_data.get("params") or cycle_data.get("metrics")))
    
    # 格式化多轮交互数据
    cycle_data_str = ""
    if has_cycle_data:
        cycle_data_str = "多轮交互数据:\n"
        for cycle_num in sorted(set(list(cycle_data.get("params", {}).keys()) + list(cycle_data.get("metrics", {}).keys()))):
            cycle_data_str += f"循环 {cycle_num}:\n"
            
            # 添加该循环的参数
            if cycle_num in cycle_data.get("params", {}):
                cycle_params = cycle_data["params"][cycle_num]
                if cycle_params:
                    cycle_data_str += "  参数:\n"
                    for k, v in cycle_params.items():
                        cycle_data_str += f"    {k}: {v}\n"
            
            # 添加该循环的指标
            if cycle_num in cycle_data.get("metrics", {}):
                cycle_metrics = cycle_data["metrics"][cycle_num]
                if cycle_metrics:
                    cycle_data_str += "  指标:\n"
                    for k, v in cycle_metrics.items():
                        cycle_data_str += f"    {k}: {v}\n"
            
            cycle_data_str += "\n"
    
    # 构建提示模板
    prompt = f"""
**角色与指令**:
您是"雅典娜"(Athena)，一个为认知数字孪生平台服务的顶尖AI分析师。您的分析必须严谨、客观且完全基于所提供的数据。在您的回答中，必须明确地区分"数据事实"（直接从数据源中获取的信息）和"推断性结论"（您基于事实进行的逻辑分析）。

**## 1. 分析任务拆解**
- **核心目标**: 对任务ID为 `{task.get('id', '未知')}` 的模拟运行进行深度因果分析。
- **模拟场景**: `{task.get('name', '未知')}`
- **用户具体关注点**: `{user_question}`

**## 2. 证据全景图**
以下是本次分析可用的全部证据，请注意检查各项数据是否存在或缺失。

*   **[数据源: PostgreSQL] 任务基础信息:**
    ```json
    {{
        "人口数量": {task.get('population', '未知')},
        "任务状态": "{task.get('status', '未知')}"
    }}
    ```

*   **[数据源: MLflow] 模拟详细参数与结果:**
    ```json
    {{
        "输入参数": {json.dumps(params, ensure_ascii=False, indent=2)},
        "输出指标": {json.dumps(metrics, ensure_ascii=False, indent=2)}
    }}
    ```

{f'''*   **[数据源: MLflow] 多轮交互数据:**
    ```
{cycle_data_str}
    ```
''' if has_cycle_data else ''}

*   **[数据源: Neo4j 知识图谱] 应用的领域知识/规则:**
    ```json
    {{
        "政策": {json.dumps(knowledge_data, ensure_ascii=False, indent=2) if knowledge_data else '未应用特定政策'}
    }}
    ```

*   **[数据源: RAG 向量检索] 相关背景资料:**
    ```text
    ---检索到的上下文---
    {rag_evidence or '未找到相关背景资料。'}
    ---上下文结束---
    ```

**## 3. 逻辑推理与分析过程 (您的思考过程)**
请严格遵循以下三阶段分析法：

*   **阶段一：事实陈述 (Factual Statement)**
    - 客观总结所有可用的结构化数据。例如：模拟的输入参数是什么？（MLflow.params），最终的关键指标是多少？（MLflow.metrics），是否应用了特定政策？（Neo4j）。
    - **禁止在此阶段进行任何推断。**
    - 明确指出数据中的缺失部分（如MLflow数据为空对象）。

*   **阶段二：因果归因 (Causal Attribution)**
    - **这是分析的核心。** 请将事实联系起来，构建因果链。
    - 知识图谱中的政策如何解释MLflow中的指标变化？例如，`节水因子`是否直接影响了`净取水量`？
    - RAG检索到的背景资料是否能为某个异常或关键的指标提供解释？（例如，历史报告中提到的某事件可能影响了本次模拟的初始条件）。
    - 如果用户提出了问题，请将分析重点聚焦于回答该问题。
    - 如果发现MLflow数据缺失，请分析可能的原因，如数据未正确记录或API访问问题。

*   **阶段三：综合评估 (Synthesized Evaluation)**
    - 结合所有证据，对模拟结果和政策效果给出一个整体评估。
    - 指出分析的置信度。是否存在数据缺失或证据矛盾之处？（例如，结果指标很高，但政策和背景资料都无法完全解释其原因）。
    - 特别关注多轮交互数据（如果存在），分析各个循环之间的变化趋势。

**## 4. 最终分析报告**
请根据您的推理过程，生成一份结构清晰的报告。

### **1. 核心发现 (Executive Summary)**
(用1-3个要点，直接明了地总结最重要的结论。如果用户有特定问题，在此处直接回答。)

### **2. 详细分析 (Detailed Analysis)**
(详细阐述您在"阶段二"和"阶段三"中的推理过程，展示数据和结论之间的逻辑联系。如果数据存在重大缺失，明确指出并提供可能的解释。)

### **3. 局限性与建议 (Limitations & Recommendations)**
(客观指出本次分析的局限性，例如关键数据的缺失。如果MLflow数据为空，建议检查数据收集机制或API连接问题。提出对未来模拟或数据收集的改进建议。)
"""
    return prompt

app = FastAPI()

class ExplainRequest(BaseModel):
    task_id: int
    user_question: Optional[str] = None

@app.post("/explain")
async def explain_task(request: ExplainRequest):
    # 1. 获取任务基本信息
    task_data = fetch_task_data(request.task_id)
    
    context = {
        "task": task_data,
        "mlflow_data": None,
        "knowledge_data": None
    }
    
    # 2. 如果找到任务数据且有 mlflowRunId，获取 MLflow 数据
    if task_data and "mlflowRunId" in task_data and task_data["mlflowRunId"]:
        mlflow_data = fetch_mlflow_data(task_data["mlflowRunId"])
        context["mlflow_data"] = mlflow_data
    
    # 3. 获取知识库数据（暂时硬编码为 'Rivertown'）
    knowledge_data = fetch_knowledge_data('Rivertown')
    context["knowledge_data"] = knowledge_data
    
    # 4. 生成提示（在调试阶段，我们返回context和prompt两部分）
    prompt = build_enhanced_prompt(context)
    
    # 5. 返回聚合的上下文数据和生成的提示（用于调试）
    return {
        "context": context,
        "prompt": prompt
    }

@app.post("/explain-stream")
async def explain_task_stream(request: ExplainRequest):
    """
    返回对模拟任务分析的流式响应
    """
    return StreamingResponse(
        stream_generator(request),
        media_type='text/event-stream'
    )

async def stream_generator(request: ExplainRequest):
    """
    生成流式响应的异步生成器
    
    Args:
        request: 包含task_id和可选user_question的请求对象
    """
    # 1. 数据聚合阶段：获取所有结构化数据
    task_data = fetch_task_data(request.task_id)
    if not task_data:
        yield f"错误：未找到任务ID为{request.task_id}的数据"
        return
    
    context = {
        "task": task_data,
        "mlflow_data": None,
        "knowledge_data": None,
        "rag_evidence": ""
    }
    
    # 如果有用户问题，添加到context中
    if request.user_question and request.user_question.strip():
        context["user_question"] = request.user_question
    
    if "mlflowRunId" in task_data and task_data["mlflowRunId"]:
        mlflow_data = fetch_mlflow_data(task_data["mlflowRunId"])
        if not mlflow_data:
            yield f"错误：无法获取MLflow运行ID为{task_data['mlflowRunId']}的数据"
            return
        context["mlflow_data"] = mlflow_data
    
    knowledge_data = fetch_knowledge_data('Rivertown')
    if not knowledge_data:
        yield f"错误：无法获取城市'Rivertown'的知识数据"
        return
    context["knowledge_data"] = knowledge_data
    
    # 2. RAG检索阶段：根据用户问题或默认查询获取相关上下文
    rag_query_text = ""
    if request.user_question and request.user_question.strip():
        rag_query_text = request.user_question
    else:
        rag_query_text = f"Analysis of simulation task: {context['task']['name']}"
    
    rag_context = await retrieve_rag_context(
        query=rag_query_text,
        collection=knowledge_collection,
        embedding_model=embeddings_model
    )
    context["rag_evidence"] = rag_context
    
    # 3. 构建Prompt阶段：使用增强的prompt构建函数
    prompt = build_enhanced_prompt(context)
    
    # 4. 流式生成阶段：调用LLM并流式返回结果
    async for chunk in query_qwen_stream(prompt):
        yield chunk

async def query_qwen_stream(prompt: str):
    """
    使用通义千问API进行流式生成
    
    Args:
        prompt: 完整的提示词
    """
    try:
        print("开始调用通义千问API...")
        completion = qwen_client.chat.completions.create(
            model="qwen-plus-2025-07-14",  # 使用适当的模型名称
            messages=[{"role": "user", "content": prompt}],
            stream=True
        )
        
        print("流式响应连接已建立")
        for chunk in completion:
            if hasattr(chunk, 'choices') and chunk.choices:
                if chunk.choices[0].delta and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
            await asyncio.sleep(0.01)
    except Exception as e:
        error_msg = f"查询通义千问API时出错: {e}"
        print(error_msg)
        yield error_msg

class SearchRequest(BaseModel):
    query: str
    top_k: int = 5

@app.post("/search")
async def search_knowledge(request: SearchRequest):
    """
    使用向量搜索从知识库中检索与查询相关的内容
    """
    try:
        # 使用嵌入模型将查询转换为向量
        query_embedding = embeddings_model.embed_query(request.query)
        
        # 使用ChromaDB进行向量搜索
        results = knowledge_collection.query(
            query_embeddings=[query_embedding],
            n_results=request.top_k
        )
        
        return {
            "query": request.query,
            "results": results
        }
    except Exception as e:
        print(f"向量搜索错误: {e}")
        return {
            "error": str(e),
            "query": request.query,
            "results": []
        }

async def retrieve_rag_context(query: str, collection, embedding_model, n_results: int = 3) -> str:
    """
    从知识库中检索与查询相关的上下文
    
    Args:
        query: 用户查询
        collection: ChromaDB集合对象
        embedding_model: 嵌入模型对象
        n_results: 要检索的结果数量
        
    Returns:
        str: 拼接后的上下文字符串
    """
    print(f"[RAG] Retrieving context for query: \"{query}\"")
    
    # 使用collection.query()方法进行相似性搜索
    results = collection.query(
        query_texts=[query],
        n_results=n_results
    )
    
    # 从查询结果中提取documents列表
    documents = results.get("documents", [[]])
    
    # 如果documents列表不为空，将所有文档块用分隔符连接
    if documents and documents[0]:
        context = "\n---\n".join(documents[0])
        print(f"[RAG] Retrieved {len(documents[0])} relevant documents")
        return context
    
    print("[RAG] No relevant documents found")
    return ""

def check_mlflow_connectivity():
    """检查MLflow连通性并列出可用实验"""
    try:
        logger.info(f"检查MLflow连通性，URI: {MLFLOW_TRACKING_URI}")
            
        # 列出实验
        experiments = mlflow_client.search_experiments()
        logger.info(f"发现 {len(experiments)} 个MLflow实验:")
        for exp in experiments:
            logger.info(f"  - 实验ID: {exp.experiment_id}, 名称: {exp.name}")
        
        # 列出第一个实验中的runs
        if experiments:
            first_exp_id = experiments[0].experiment_id
            runs = mlflow_client.search_runs(experiment_ids=[first_exp_id], max_results=5)
            logger.info(f"实验 {first_exp_id} 中发现 {len(runs)} 个run:")
            for run in runs:
                logger.info(f"  - Run ID: {run.info.run_id}, 状态: {run.info.status}")
                if hasattr(run.data, 'params') and run.data.params:
                    logger.debug(f"    参数: {run.data.params}")
                if hasattr(run.data, 'metrics') and run.data.metrics:
                    logger.debug(f"    指标: {run.data.metrics}")
        
        return True
    except Exception as e:
        logger.error(f"检查MLflow连通性时出错: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    import uvicorn
    
    # 启动前检查MLflow连通性
    check_mlflow_connectivity()
    
    uvicorn.run(app, host="0.0.0.0", port=8000) 