apiVersion: apps/v1
kind: Deployment
metadata:
  name: rabbitmq-deployment
  labels:
    app: rabbitmq
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq
  template:
    metadata:
      labels:
        app: rabbitmq
    spec:
      containers:
      - name: rabbitmq
        image: rabbitmq:3.12-management-alpine
        ports:
        - containerPort: 5672
        - containerPort: 15672
---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-svc
spec:
  selector:
    app: rabbitmq
  type: ClusterIP
  ports:
  - protocol: TCP
    port: 5672
    targetPort: 5672
    name: amqp
  - protocol: TCP
    port: 15672
    targetPort: 15672
    name: management
