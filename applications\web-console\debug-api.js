// API 调试脚本
// 在浏览器控制台中运行此脚本来测试 API 连接

console.log('🔍 开始 API 调试...')

// 1. 测试基本连接
async function testBasicConnection() {
  console.log('\n1. 测试基本连接...')
  try {
    const response = await fetch('/api/admin/pods')
    console.log('状态码:', response.status)
    console.log('状态文本:', response.statusText)
    console.log('响应头:', Object.fromEntries(response.headers.entries()))
    
    if (response.status === 401) {
      console.log('❌ 需要认证 - 请先登录')
      return false
    } else if (response.ok) {
      console.log('✅ 基本连接正常')
      return true
    } else {
      console.log('❌ 连接失败')
      return false
    }
  } catch (error) {
    console.error('❌ 连接错误:', error)
    return false
  }
}

// 2. 测试认证连接
async function testAuthenticatedConnection() {
  console.log('\n2. 测试认证连接...')
  const token = localStorage.getItem('token')
  
  if (!token) {
    console.log('❌ 没有找到 token，请先登录')
    return false
  }
  
  console.log('Token 前缀:', token.substring(0, 20) + '...')
  
  try {
    const response = await fetch('/api/admin/pods', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    console.log('状态码:', response.status)
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ 认证连接成功')
      console.log('Pod 数量:', data.length)
      return true
    } else {
      console.log('❌ 认证失败')
      const text = await response.text()
      console.log('错误响应:', text)
      return false
    }
  } catch (error) {
    console.error('❌ 认证连接错误:', error)
    return false
  }
}

// 3. 测试日志 API
async function testLogAPI() {
  console.log('\n3. 测试日志 API...')
  const token = localStorage.getItem('token')
  
  if (!token) {
    console.log('❌ 没有 token')
    return false
  }
  
  try {
    // 先获取 Pod 列表
    const podsResponse = await fetch('/api/admin/pods', {
      headers: { 'Authorization': `Bearer ${token}` }
    })
    
    if (!podsResponse.ok) {
      console.log('❌ 无法获取 Pod 列表')
      return false
    }
    
    const pods = await podsResponse.json()
    if (pods.length === 0) {
      console.log('❌ 没有找到 Pod')
      return false
    }
    
    const testPod = pods[0]
    console.log('测试 Pod:', testPod.name, 'in', testPod.namespace)
    
    // 测试静态日志
    const logUrl = `/api/admin/pods/${testPod.namespace}/${testPod.name}/logs?follow=false&tail_lines=10`
    console.log('日志 URL:', logUrl)
    
    const logResponse = await fetch(logUrl, {
      headers: { 'Authorization': `Bearer ${token}` }
    })
    
    console.log('日志响应状态:', logResponse.status)
    
    if (logResponse.ok) {
      const logs = await logResponse.text()
      console.log('✅ 日志 API 正常')
      console.log('日志长度:', logs.length)
      console.log('日志预览:', logs.substring(0, 200) + '...')
      return true
    } else {
      console.log('❌ 日志 API 失败')
      const errorText = await logResponse.text()
      console.log('错误:', errorText)
      return false
    }
  } catch (error) {
    console.error('❌ 日志 API 错误:', error)
    return false
  }
}

// 4. 测试流式日志
async function testStreamingLogs() {
  console.log('\n4. 测试流式日志...')
  const token = localStorage.getItem('token')
  
  if (!token) {
    console.log('❌ 没有 token')
    return false
  }
  
  try {
    // 获取第一个 Pod
    const podsResponse = await fetch('/api/admin/pods', {
      headers: { 'Authorization': `Bearer ${token}` }
    })
    
    const pods = await podsResponse.json()
    const testPod = pods[0]
    
    const streamUrl = `/api/admin/pods/${testPod.namespace}/${testPod.name}/logs?follow=true&tail_lines=5`
    console.log('流式日志 URL:', streamUrl)
    
    const response = await fetch(streamUrl, {
      headers: { 'Authorization': `Bearer ${token}` }
    })
    
    if (!response.ok) {
      console.log('❌ 流式日志请求失败:', response.status)
      return false
    }
    
    if (!response.body) {
      console.log('❌ 没有响应体')
      return false
    }
    
    console.log('✅ 流式日志连接成功，开始读取...')
    
    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let chunks = 0
    
    // 读取前几个块进行测试
    for (let i = 0; i < 3; i++) {
      try {
        const { done, value } = await reader.read()
        if (done) break
        
        chunks++
        const chunk = decoder.decode(value)
        console.log(`块 ${chunks}:`, chunk.substring(0, 100) + '...')
      } catch (readError) {
        console.error('读取错误:', readError)
        break
      }
    }
    
    reader.cancel()
    console.log('✅ 流式日志测试完成，共读取', chunks, '个块')
    return true
    
  } catch (error) {
    console.error('❌ 流式日志错误:', error)
    return false
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始完整 API 测试...')
  
  const results = {
    basic: await testBasicConnection(),
    auth: await testAuthenticatedConnection(),
    logs: await testLogAPI(),
    streaming: await testStreamingLogs()
  }
  
  console.log('\n📊 测试结果汇总:')
  console.log('基本连接:', results.basic ? '✅' : '❌')
  console.log('认证连接:', results.auth ? '✅' : '❌')
  console.log('日志 API:', results.logs ? '✅' : '❌')
  console.log('流式日志:', results.streaming ? '✅' : '❌')
  
  const allPassed = Object.values(results).every(r => r)
  console.log('\n总体结果:', allPassed ? '✅ 全部通过' : '❌ 存在问题')
  
  if (!allPassed) {
    console.log('\n🔧 故障排除建议:')
    if (!results.basic) console.log('- 检查 API Gateway 是否运行: kubectl get pods -l app=api-gateway')
    if (!results.auth) console.log('- 检查登录状态和 token 有效性')
    if (!results.logs) console.log('- 检查 admin-service 是否运行: kubectl get pods -l app=admin-service')
    if (!results.streaming) console.log('- 检查浏览器是否支持 ReadableStream API')
  }
}

// 导出函数供手动调用
window.debugAPI = {
  testBasicConnection,
  testAuthenticatedConnection,
  testLogAPI,
  testStreamingLogs,
  runAllTests
}

console.log('\n🛠️  调试函数已加载到 window.debugAPI')
console.log('使用方法:')
console.log('- debugAPI.runAllTests() - 运行所有测试')
console.log('- debugAPI.testLogAPI() - 只测试日志 API')
console.log('- debugAPI.testStreamingLogs() - 只测试流式日志')

// 自动运行测试
runAllTests()
