<template>
  <div class="roles-container">
    <!-- 顶部搜索和操作栏 -->
    <el-card class="filter-card fade-in">
      <div class="filter-form">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="角色名称">
            <el-input v-model="searchForm.roleName" placeholder="请输入角色名称" clearable />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
              <el-option label="启用" value="active" />
              <el-option label="禁用" value="disabled" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 角色统计 -->
    <div class="role-stats slide-in-right">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6" v-for="(stat, index) in roleStats" :key="index">
          <el-card class="stat-card hover-lift" :body-style="{ padding: '20px' }">
            <div class="stat-content">
              <div class="stat-icon" :class="stat.iconClass">
                <el-icon><component :is="stat.icon" /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stat.count }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 角色列表 -->
    <el-card class="list-card fade-in">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <el-icon class="header-icon"><UserFilled /></el-icon>
            <span>角色列表</span>
            <el-tag type="info" class="count-tag">{{ roleData.length }} 个</el-tag>
          </div>
          <div class="header-actions">
            <el-button type="primary" :icon="Plus" @click="handleCreate">新建角色</el-button>
          </div>
        </div>
      </template>
      
      <!-- 角色表格 -->
      <el-table
        :data="roleData"
        border
        stripe
        style="width: 100%"
        v-loading="loading"
        :header-cell-style="{ background: '#f5f7fa' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="id" label="角色ID" width="100" sortable />
        <el-table-column prop="roleName" label="角色名称" min-width="150">
          <template #default="{ row }">
            <div class="role-name-cell">
              <el-icon class="role-icon" :style="{ color: row.color }">
                <component :is="row.icon" />
              </el-icon>
              <span class="role-name">{{ row.roleName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="roleCode" label="角色编码" width="120" />
        <el-table-column prop="description" label="角色描述" min-width="200" />
        <el-table-column prop="userCount" label="用户数量" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="primary" effect="plain">{{ row.userCount }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="row.status === 'active' ? 'success' : 'danger'"
              effect="dark"
            >
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" sortable width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-btns">
              <el-button 
                type="primary" 
                size="small" 
                text 
                :icon="Edit" 
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button 
                type="primary" 
                size="small" 
                text 
                :icon="Key" 
                @click="handlePermissions(row)"
              >
                权限
              </el-button>
              <el-dropdown>
                <el-button size="small" text>
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleCopy(row)">
                      <el-icon><CopyDocument /></el-icon> 复制角色
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleUsers(row)">
                      <el-icon><User /></el-icon> 查看用户
                    </el-dropdown-item>
                    <el-dropdown-item divided @click="handleDelete(row)" style="color: #F56C6C;">
                      <el-icon><Delete /></el-icon> 删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 角色表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑角色' : '新建角色'"
      width="580px"
    >
      <el-form
        ref="roleFormRef"
        :model="roleForm"
        :rules="roleRules"
        label-width="100px"
      >
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="roleForm.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        
        <el-form-item label="角色编码" prop="roleCode">
          <el-input v-model="roleForm.roleCode" placeholder="请输入角色编码" />
        </el-form-item>
        
        <el-form-item label="角色图标">
          <el-select v-model="roleForm.icon" placeholder="选择图标">
            <el-option label="管理员" value="UserFilled" />
            <el-option label="用户" value="User" />
            <el-option label="访客" value="View" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="角色颜色">
          <el-color-picker v-model="roleForm.color" />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-radio-group v-model="roleForm.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="disabled">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="角色描述">
          <el-input v-model="roleForm.description" type="textarea" :rows="3" placeholder="请输入角色描述" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Search, 
  Refresh, 
  Edit, 
  Delete, 
  Key,
  User,
  UserFilled,
  ArrowDown,
  CopyDocument,
  View
} from '@element-plus/icons-vue'

// 表格加载状态
const loading = ref(false)

// 搜索表单
const searchForm = reactive({
  roleName: '',
  status: ''
})

// 统计数据
const roleStats = [
  { label: '全部角色', count: 8, icon: 'UserFilled', iconClass: 'all-icon' },
  { label: '启用角色', count: 6, icon: 'CircleCheck', iconClass: 'active-icon' },
  { label: '禁用角色', count: 2, icon: 'CircleClose', iconClass: 'disabled-icon' },
  { label: '系统角色', count: 3, icon: 'Lock', iconClass: 'system-icon' }
]

// 角色数据
const roleData = ref([
  {
    id: 1,
    roleName: '超级管理员',
    roleCode: 'super_admin',
    description: '系统最高权限管理员，拥有所有功能权限',
    userCount: 1,
    status: 'active',
    createTime: '2024-01-01 09:00:00',
    icon: 'UserFilled',
    color: '#f56c6c'
  },
  {
    id: 2,
    roleName: '系统管理员',
    roleCode: 'admin',
    description: '系统管理员，负责用户和权限管理',
    userCount: 3,
    status: 'active',
    createTime: '2024-01-01 09:00:00',
    icon: 'UserFilled',
    color: '#409eff'
  },
  {
    id: 3,
    roleName: '业务管理员',
    roleCode: 'business_admin',
    description: '业务管理员，负责业务数据管理',
    userCount: 5,
    status: 'active',
    createTime: '2024-01-05 10:00:00',
    icon: 'User',
    color: '#67c23a'
  },
  {
    id: 4,
    roleName: '普通用户',
    roleCode: 'user',
    description: '普通用户，具有基本查看和操作权限',
    userCount: 25,
    status: 'active',
    createTime: '2024-01-05 10:00:00',
    icon: 'User',
    color: '#909399'
  },
  {
    id: 5,
    roleName: '访客',
    roleCode: 'guest',
    description: '访客用户，只有查看权限',
    userCount: 8,
    status: 'disabled',
    createTime: '2024-01-10 14:00:00',
    icon: 'View',
    color: '#c0c4cc'
  }
])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(5)

// 选中行
const selectedRows = ref([])

// 对话框相关
const dialogVisible = ref(false)
const isEdit = ref(false)

// 表单引用
const roleFormRef = ref()

// 角色表单
const roleForm = reactive({
  id: null,
  roleName: '',
  roleCode: '',
  description: '',
  status: 'active',
  icon: 'User',
  color: '#409eff'
})

// 表单验证规则
const roleRules = {
  roleName: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  roleCode: [
    { required: true, message: '请输入角色编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '角色编码只能包含字母、数字和下划线，且以字母或下划线开头', trigger: 'blur' }
  ]
}

// 页面加载时
onMounted(() => {
  fetchRoleData()
})

// 模拟获取角色数据
const fetchRoleData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 500)
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchRoleData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.roleName = ''
  searchForm.status = ''
  handleSearch()
}

// 表格选择行变化
const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchRoleData()
}

// 页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchRoleData()
}

// 新建角色
const handleCreate = () => {
  isEdit.value = false
  Object.keys(roleForm).forEach(key => {
    if (key === 'status') {
      roleForm[key] = 'active'
    } else if (key === 'icon') {
      roleForm[key] = 'User'
    } else if (key === 'color') {
      roleForm[key] = '#409eff'
    } else {
      roleForm[key] = ''
    }
  })
  roleForm.id = null
  dialogVisible.value = true
}

// 编辑角色
const handleEdit = (row: any) => {
  isEdit.value = true
  Object.keys(roleForm).forEach(key => {
    roleForm[key] = row[key]
  })
  dialogVisible.value = true
}

// 权限管理
const handlePermissions = (row: any) => {
  ElMessage.info(`配置角色权限：${row.roleName}`)
}

// 复制角色
const handleCopy = (row: any) => {
  ElMessage.info(`复制角色：${row.roleName}`)
}

// 查看用户
const handleUsers = (row: any) => {
  ElMessage.info(`查看角色用户：${row.roleName}`)
}

// 删除角色
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确认删除角色：${row.roleName}？此操作不可恢复！`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'danger'
  }).then(() => {
    const index = roleData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      roleData.value.splice(index, 1)
      total.value--
      ElMessage.success('删除成功')
    }
  }).catch(() => {})
}

// 提交表单
const submitForm = () => {
  roleFormRef.value.validate((valid: boolean) => {
    if (!valid) return
    
    if (isEdit.value) {
      // 编辑现有角色
      const index = roleData.value.findIndex(item => item.id === roleForm.id)
      if (index !== -1) {
        roleData.value[index] = { ...roleForm }
        ElMessage.success('角色信息已更新')
      }
    } else {
      // 添加新角色
      const newRole = { ...roleForm }
      newRole.id = roleData.value.length + 1
      newRole.createTime = new Date().toLocaleString()
      newRole.userCount = 0
      roleData.value.push(newRole)
      total.value++
      ElMessage.success('角色添加成功')
    }
    
    dialogVisible.value = false
  })
}
</script>

<style scoped>
.roles-container {
  width: 100%;
}

.filter-card {
  margin-bottom: 16px;
  background-color: var(--bg-primary);
}

.role-stats {
  margin-bottom: 16px;
}

.stat-card {
  margin-bottom: 16px;
  transition: all 0.3s ease;
  background: var(--bg-primary);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.all-icon {
  background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
}

.active-icon {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
}

.disabled-icon {
  background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
}

.system-icon {
  background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.list-card {
  margin-bottom: 16px;
  background-color: var(--bg-primary);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: var(--text-primary);
}

.header-icon {
  margin-right: 8px;
  color: var(--primary-color);
}

.count-tag {
  margin-left: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.role-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.role-icon {
  font-size: 18px;
}

.role-name {
  font-weight: 500;
  color: var(--text-primary);
}

.action-btns {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  gap: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .search-form {
    flex-wrap: wrap;
  }

  .el-form--inline .el-form-item {
    margin-right: 10px;
  }
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }
}

@media (max-width: 576px) {
  .header-actions .el-button {
    flex: 1;
  }

  .pagination-container {
    justify-content: center;
  }
}
</style>
