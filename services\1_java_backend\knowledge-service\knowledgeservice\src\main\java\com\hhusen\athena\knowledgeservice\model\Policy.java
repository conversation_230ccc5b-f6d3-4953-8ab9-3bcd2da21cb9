package com.hhusen.athena.knowledgeservice.model;

import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Property;
import org.springframework.data.neo4j.core.schema.Relationship;

@Node("Policy")
public class Policy {
    @Id
    private String id;
    
    @Property("name")
    private String name;
    
    @Property("description")
    private String description;
    
    @Property("water_saving_factor")
    private Double waterSavingFactor;

    @Property("start_date")
    private String startDate;

    @Property("end_date")
    private String endDate;

    @Relationship(type = "IMPLEMENTED_IN")
    private City city;
    
    public Policy() {
    }
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Double getWaterSavingFactor() {
        return waterSavingFactor;
    }

    public void setWaterSavingFactor(Double waterSavingFactor) {
        this.waterSavingFactor = waterSavingFactor;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public City getCity() {
        return city;
    }

    public void setCity(City city) {
        this.city = city;
    }
}