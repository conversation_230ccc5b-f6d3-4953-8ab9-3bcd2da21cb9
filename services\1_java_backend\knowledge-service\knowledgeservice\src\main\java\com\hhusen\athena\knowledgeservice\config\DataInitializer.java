package com.hhusen.athena.knowledgeservice.config;

import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.neo4j.core.Neo4jClient;

import java.util.Map;
import java.util.UUID;
import java.util.HashMap;

@Configuration
public class DataInitializer {

    private final Neo4jClient neo4jClient;
    
    public DataInitializer(Neo4jClient neo4jClient) {
        this.neo4jClient = neo4jClient;
    }
    
    @Bean
    public CommandLineRunner initDatabase() {
        return args -> {
            // 清除现有数据
            neo4jClient.query("MATCH (n) DETACH DELETE n").run();
            
            // 创建城市节点
            neo4jClient.query(
                "CREATE (c:City {name: $name, population: $population, region: $region}) RETURN c"
            )
            .bindAll(Map.of(
                "name", "Rivertown",
                "population", 100000L,  // 使用Long类型
                "region", "Central"
            ))
            .run();
            
            // 创建政策节点
            String policyId = UUID.randomUUID().toString();
            
            // 直接在政策节点上设置水位因子属性，而不是使用嵌套Map
            neo4jClient.query(
                "CREATE (p:Policy {id: $id, name: $name, description: $description, " +
                "water_saving_factor: $waterSavingFactor, " +
                "start_date: $startDate, end_date: $endDate}) " +
                "WITH p " +
                "MATCH (c:City {name: $cityName}) " +
                "CREATE (p)-[:IMPLEMENTED_IN]->(c) " +
                "RETURN p"
            )
            .bindAll(Map.of(
                "id", policyId,
                "name", "DroughtAlert",
                "description", "节水政策，在干旱期间减少用水量",
                "waterSavingFactor", 0.8,
                "startDate", "2025-06-01",
                "endDate", "2025-09-30",
                "cityName", "Rivertown"
            ))
            .run();
            
            System.out.println("数据初始化完成！");
        };
    }
} 