apiVersion: apps/v1
kind: Deployment
metadata:
  name: llm-service
  labels:
    app: llm-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: llm-service
  template:
    metadata:
      labels:
        app: llm-service
    spec:
      containers:
      - name: llm-service
        image: athena/llm-service:0.0.18
        ports:
        - containerPort: 8000
        env:
        - name: DASHSCOPE_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-secret
              key: DASHSCOPE_API_KEY
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_PASSWORD
        - name: DATABASE_URL
          value: "********************************************************************/athena"
        - name: M<PERSON><PERSON>OW_TRACKING_URI
          value: "http://mlflow-svc:5000"
        - name: K<PERSON><PERSON>LEDGE_SERVICE_URL
          value: "http://knowledge-service-svc:80"
        - name: CHROMA_TELEMETRY_ANALYTICS_ENABLED
          value: "False"
        - name: CHROMA_ANALYTICS_ENABLED
          value: "False"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        # # --- 添加以下两行用于调试 ---
        # command: ["/bin/bash"]
        # args: ["-c", "while true; do sleep 3600; done"]
        # # --- 调试完毕后请移除这两行或注释掉 ---
---
apiVersion: v1
kind: Service
metadata:
  name: llm-service
spec:
  selector:
    app: llm-service
  ports:
  - port: 80
    targetPort: 8000
  type: ClusterIP