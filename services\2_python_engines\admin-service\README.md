# Admin Service

Kubernetes 管理服务，提供 Pod 监控、日志查看、Deployment 重启和扩缩容等功能。

## 功能特性

- 🔍 **Pod 监控**: 实时查看集群中所有 Pod 的状态
- 📋 **日志查看**: 流式查看 Pod 日志，支持多容器选择
- 🔄 **重启服务**: 一键重启 Deployment
- 📊 **扩缩容**: 动态调整 Deployment 副本数
- 🔐 **安全代理**: 通过 ServiceAccount 安全访问 K8s API

## API 端点

### Pod 管理
- `GET /api/admin/pods` - 获取所有 Pod 列表
- `GET /api/admin/pods/{namespace}/{pod_name}/logs` - 获取 Pod 日志（支持流式）

### Deployment 管理
- `POST /api/admin/deployments/{namespace}/{deployment_name}/restart` - 重启 Deployment
- `POST /api/admin/deployments/{namespace}/{deployment_name}/scale` - 扩缩容 Deployment

### 健康检查
- `GET /health` - 服务健康检查

## 快速开始

### 1. 构建和部署

```bash
# 构建并部署到 minikube
./build.sh
```

### 2. 更新版本

```bash
# 更新到新版本
./update-version.sh 0.0.2

# 重新构建部署
./build.sh
```

### 3. 查看服务状态

```bash
# 查看 Pod 状态
kubectl get pods -l app=admin-service

# 查看服务日志
kubectl logs -l app=admin-service -f

# 查看服务详情
kubectl describe svc admin-service-svc
```

## 权限配置

服务使用以下 Kubernetes 权限：

- **ServiceAccount**: `admin-service-sa`
- **ClusterRole**: `admin-service-role`
- **权限范围**:
  - `pods`: get, list, watch
  - `pods/log`: get, list, watch
  - `deployments`: get, list, patch, update
  - `namespaces`: get, list

## 开发环境

### 本地开发

```bash
# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 环境变量

- `PORT`: 服务端口 (默认: 8000)

## 文件结构

```
admin-service/
├── main.py                 # FastAPI 应用主文件
├── requirements.txt        # Python 依赖
├── Dockerfile             # Docker 镜像构建文件
├── build.sh              # 构建部署脚本
├── update-version.sh     # 版本更新脚本
├── k8s/
│   ├── rbac.yaml         # RBAC 权限配置
│   └── deployment.yaml   # Kubernetes 部署配置
└── README.md            # 项目说明
```

## 故障排除

### 1. 权限问题

如果遇到权限错误，检查 ServiceAccount 和 RBAC 配置：

```bash
kubectl get serviceaccount admin-service-sa
kubectl get clusterrole admin-service-role
kubectl get clusterrolebinding admin-service-binding
```

### 2. 镜像拉取问题

确保镜像已正确加载到 minikube：

```bash
minikube ssh "docker images | grep admin-service"
```

### 3. 服务连接问题

检查服务和端点：

```bash
kubectl get svc admin-service-svc
kubectl get endpoints admin-service-svc
```

### 4. 查看详细日志

```bash
kubectl logs -l app=admin-service --tail=100
```

## 版本历史

- **v0.0.1**: 初始版本
  - Pod 列表查看
  - 流式日志查看
  - Deployment 重启
  - Deployment 扩缩容

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
