/**
 * 设置功能验证工具
 * 用于验证系统设置的各项功能是否正常工作
 */

export interface ValidationResult {
  success: boolean
  message: string
  details?: any
}

export class SettingsValidator {
  /**
   * 验证主题设置功能
   */
  static validateThemeSettings(): ValidationResult {
    try {
      const root = document.documentElement
      
      // 检查CSS变量是否存在
      const primaryColor = getComputedStyle(root).getPropertyValue('--primary-color')
      const fontSize = getComputedStyle(root).getPropertyValue('--base-font-size')
      const scaleFactor = getComputedStyle(root).getPropertyValue('--scale-factor')
      
      if (!primaryColor || !fontSize) {
        return {
          success: false,
          message: '主题CSS变量未正确设置',
          details: { primaryColor, fontSize, scaleFactor }
        }
      }
      
      return {
        success: true,
        message: '主题设置功能正常',
        details: { primaryColor, fontSize, scaleFactor }
      }
    } catch (error) {
      return {
        success: false,
        message: '主题设置验证失败',
        details: error
      }
    }
  }

  /**
   * 验证localStorage存储功能
   */
  static validateLocalStorage(): ValidationResult {
    try {
      const testKey = 'settings-test'
      const testValue = 'test-value'
      
      // 测试写入
      localStorage.setItem(testKey, testValue)
      
      // 测试读取
      const retrievedValue = localStorage.getItem(testKey)
      
      // 清理测试数据
      localStorage.removeItem(testKey)
      
      if (retrievedValue !== testValue) {
        return {
          success: false,
          message: 'localStorage读写功能异常'
        }
      }
      
      // 检查关键设置项
      const themeMode = localStorage.getItem('theme-mode')
      const colorScheme = localStorage.getItem('color-scheme')
      
      return {
        success: true,
        message: 'localStorage功能正常',
        details: { themeMode, colorScheme }
      }
    } catch (error) {
      return {
        success: false,
        message: 'localStorage验证失败',
        details: error
      }
    }
  }

  /**
   * 验证通知权限
   */
  static validateNotificationPermission(): ValidationResult {
    try {
      if (!('Notification' in window)) {
        return {
          success: false,
          message: '浏览器不支持通知功能'
        }
      }
      
      const permission = Notification.permission
      
      return {
        success: true,
        message: '通知功能可用',
        details: { permission }
      }
    } catch (error) {
      return {
        success: false,
        message: '通知权限验证失败',
        details: error
      }
    }
  }

  /**
   * 验证文件操作功能
   */
  static validateFileOperations(): ValidationResult {
    try {
      // 检查Blob和URL API
      if (!window.Blob || !window.URL) {
        return {
          success: false,
          message: '浏览器不支持文件操作API'
        }
      }
      
      // 测试创建Blob
      const testBlob = new Blob(['test'], { type: 'text/plain' })
      const testUrl = URL.createObjectURL(testBlob)
      URL.revokeObjectURL(testUrl)
      
      // 检查FileReader API
      if (!window.FileReader) {
        return {
          success: false,
          message: '浏览器不支持文件读取API'
        }
      }
      
      return {
        success: true,
        message: '文件操作功能正常'
      }
    } catch (error) {
      return {
        success: false,
        message: '文件操作验证失败',
        details: error
      }
    }
  }

  /**
   * 验证响应式设计
   */
  static validateResponsiveDesign(): ValidationResult {
    try {
      const width = window.innerWidth
      const height = window.innerHeight
      
      // 检查媒体查询支持
      if (!window.matchMedia) {
        return {
          success: false,
          message: '浏览器不支持媒体查询API'
        }
      }
      
      const isMobile = window.matchMedia('(max-width: 768px)').matches
      const isTablet = window.matchMedia('(max-width: 1024px)').matches
      
      return {
        success: true,
        message: '响应式设计功能正常',
        details: { width, height, isMobile, isTablet }
      }
    } catch (error) {
      return {
        success: false,
        message: '响应式设计验证失败',
        details: error
      }
    }
  }

  /**
   * 运行所有验证
   */
  static runAllValidations(): { [key: string]: ValidationResult } {
    return {
      theme: this.validateThemeSettings(),
      localStorage: this.validateLocalStorage(),
      notification: this.validateNotificationPermission(),
      fileOperations: this.validateFileOperations(),
      responsive: this.validateResponsiveDesign()
    }
  }

  /**
   * 生成验证报告
   */
  static generateReport(): string {
    const results = this.runAllValidations()
    const lines: string[] = []
    
    lines.push('=== 系统设置功能验证报告 ===')
    lines.push(`验证时间: ${new Date().toLocaleString()}`)
    lines.push('')
    
    Object.entries(results).forEach(([category, result]) => {
      const status = result.success ? '✅ 通过' : '❌ 失败'
      lines.push(`${category}: ${status} - ${result.message}`)
      
      if (result.details) {
        lines.push(`  详情: ${JSON.stringify(result.details, null, 2)}`)
      }
      lines.push('')
    })
    
    const successCount = Object.values(results).filter(r => r.success).length
    const totalCount = Object.values(results).length
    
    lines.push(`总体结果: ${successCount}/${totalCount} 项验证通过`)
    
    return lines.join('\n')
  }
}

// 开发环境下自动运行验证
if (import.meta.env.DEV) {
  console.log('🔍 运行设置功能验证...')
  console.log(SettingsValidator.generateReport())
}

export default SettingsValidator
