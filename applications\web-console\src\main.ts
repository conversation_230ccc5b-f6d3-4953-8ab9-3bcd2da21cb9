import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import ElementPlus from 'element-plus'

import App from './App.vue'
import router from './router'
import { i18n, initI18n } from './locales'
import { setupGlobalErrorHandler } from './utils/errorHandler'

// 创建应用实例
const app = createApp(App)

// 创建Pinia实例
const pinia = createPinia()

// 注册插件
app.use(pinia)
app.use(router)
app.use(ElementPlus)
app.use(i18n)

// 初始化系统
initI18n()
setupGlobalErrorHandler()

// 挂载应用
app.mount('#app')
