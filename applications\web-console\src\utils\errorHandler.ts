import { ElMessage, ElNotification, ElMessageBox } from 'element-plus'
import { useNotificationStore } from '@/stores/notification'
import { useLoadingStore } from '@/stores/loading'

export interface ErrorInfo {
  code?: string | number
  message: string
  details?: any
  timestamp: Date
  url?: string
  userAgent?: string
  userId?: string
  stack?: string
}

export interface ErrorHandlerOptions {
  showMessage?: boolean
  showNotification?: boolean
  logToConsole?: boolean
  reportToServer?: boolean
  silent?: boolean
  customHandler?: (error: ErrorInfo) => void
}

class ErrorHandler {
  private errorQueue: ErrorInfo[] = []
  private maxQueueSize = 100
  private reportEndpoint = '/api/errors'

  /**
   * 处理错误
   */
  handle(error: Error | string | any, options: ErrorHandlerOptions = {}) {
    const errorInfo = this.normalizeError(error)
    
    // 默认选项
    const defaultOptions: ErrorHandlerOptions = {
      showMessage: true,
      showNotification: false,
      logToConsole: true,
      reportToServer: false,
      silent: false
    }
    
    const finalOptions = { ...defaultOptions, ...options }

    // 添加到错误队列
    this.addToQueue(errorInfo)

    // 静默模式直接返回
    if (finalOptions.silent) {
      return
    }

    // 控制台日志
    if (finalOptions.logToConsole) {
      this.logToConsole(errorInfo)
    }

    // 显示消息
    if (finalOptions.showMessage) {
      this.showMessage(errorInfo)
    }

    // 显示通知
    if (finalOptions.showNotification) {
      this.showNotification(errorInfo)
    }

    // 上报服务器
    if (finalOptions.reportToServer) {
      this.reportToServer(errorInfo)
    }

    // 自定义处理器
    if (finalOptions.customHandler) {
      finalOptions.customHandler(errorInfo)
    }
  }

  /**
   * 处理网络错误
   */
  handleNetworkError(error: any, options: ErrorHandlerOptions = {}) {
    let message = '网络错误，请检查网络连接'
    
    if (error.response) {
      // 服务器响应错误
      const status = error.response.status
      switch (status) {
        case 400:
          message = '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          break
        case 403:
          message = '权限不足'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        default:
          message = `服务器错误 (${status})`
      }
    } else if (error.request) {
      // 请求发送失败
      message = '网络连接失败，请检查网络'
    }

    this.handle(new Error(message), {
      ...options,
      reportToServer: true
    })
  }

  /**
   * 处理业务错误
   */
  handleBusinessError(code: string | number, message: string, details?: any) {
    const error = new Error(message)
    const errorInfo: ErrorInfo = {
      code,
      message,
      details,
      timestamp: new Date()
    }

    this.handle(errorInfo, {
      showMessage: true,
      logToConsole: true,
      reportToServer: false
    })
  }

  /**
   * 处理验证错误
   */
  handleValidationError(errors: Record<string, string[]>) {
    const messages = Object.entries(errors)
      .map(([field, msgs]) => `${field}: ${msgs.join(', ')}`)
      .join('\n')

    this.handle(new Error(`验证失败:\n${messages}`), {
      showMessage: true,
      showNotification: false
    })
  }

  /**
   * 处理权限错误
   */
  handlePermissionError(action?: string) {
    const message = action ? `没有权限执行操作: ${action}` : '权限不足'
    
    this.handle(new Error(message), {
      showMessage: true,
      showNotification: true
    })
  }

  /**
   * 确认对话框错误处理
   */
  async handleWithConfirm(
    operation: () => Promise<any>,
    confirmMessage: string = '确认执行此操作？',
    errorMessage: string = '操作失败'
  ) {
    try {
      await ElMessageBox.confirm(confirmMessage, '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const loadingStore = useLoadingStore()
      const loadingId = loadingStore.startLoading('operation', { message: '处理中...' })

      try {
        const result = await operation()
        ElMessage.success('操作成功')
        return result
      } finally {
        loadingStore.stopLoading(loadingId)
      }
    } catch (error: any) {
      if (error !== 'cancel') {
        this.handle(new Error(errorMessage), {
          showMessage: true,
          reportToServer: true
        })
      }
      throw error
    }
  }

  /**
   * 重试机制
   */
  async retry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: any

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error
        
        if (i === maxRetries) {
          break
        }

        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
      }
    }

    this.handle(lastError, {
      showMessage: true,
      reportToServer: true
    })
    throw lastError
  }

  /**
   * 标准化错误对象
   */
  private normalizeError(error: any): ErrorInfo {
    if (typeof error === 'string') {
      return {
        message: error,
        timestamp: new Date()
      }
    }

    if (error instanceof Error) {
      return {
        message: error.message,
        stack: error.stack,
        timestamp: new Date(),
        url: window.location.href,
        userAgent: navigator.userAgent
      }
    }

    if (error && typeof error === 'object') {
      return {
        code: error.code,
        message: error.message || '未知错误',
        details: error.details || error,
        timestamp: new Date(),
        url: window.location.href,
        userAgent: navigator.userAgent
      }
    }

    return {
      message: '未知错误',
      details: error,
      timestamp: new Date()
    }
  }

  /**
   * 添加到错误队列
   */
  private addToQueue(errorInfo: ErrorInfo) {
    this.errorQueue.unshift(errorInfo)
    
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue = this.errorQueue.slice(0, this.maxQueueSize)
    }
  }

  /**
   * 控制台日志
   */
  private logToConsole(errorInfo: ErrorInfo) {
    console.group('🚨 Error Handler')
    console.error('Message:', errorInfo.message)
    if (errorInfo.code) console.error('Code:', errorInfo.code)
    if (errorInfo.details) console.error('Details:', errorInfo.details)
    if (errorInfo.stack) console.error('Stack:', errorInfo.stack)
    console.error('Timestamp:', errorInfo.timestamp)
    console.groupEnd()
  }

  /**
   * 显示错误消息
   */
  private showMessage(errorInfo: ErrorInfo) {
    ElMessage.error({
      message: errorInfo.message,
      duration: 5000,
      showClose: true
    })
  }

  /**
   * 显示错误通知
   */
  private showNotification(errorInfo: ErrorInfo) {
    const notificationStore = useNotificationStore()
    
    notificationStore.error('系统错误', errorInfo.message)
  }

  /**
   * 上报到服务器
   */
  private async reportToServer(errorInfo: ErrorInfo) {
    try {
      await fetch(this.reportEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(errorInfo)
      })
    } catch (error) {
      console.warn('Failed to report error to server:', error)
    }
  }

  /**
   * 获取错误历史
   */
  getErrorHistory() {
    return [...this.errorQueue]
  }

  /**
   * 清空错误历史
   */
  clearErrorHistory() {
    this.errorQueue = []
  }

  /**
   * 设置上报端点
   */
  setReportEndpoint(endpoint: string) {
    this.reportEndpoint = endpoint
  }
}

// 创建全局错误处理器实例
export const errorHandler = new ErrorHandler()

// 全局错误处理
export const setupGlobalErrorHandler = () => {
  // Vue错误处理
  window.addEventListener('error', (event) => {
    errorHandler.handle(event.error, {
      reportToServer: true
    })
  })

  // Promise未捕获错误
  window.addEventListener('unhandledrejection', (event) => {
    errorHandler.handle(event.reason, {
      reportToServer: true
    })
  })

  // Vue警告处理
  if (import.meta.env.DEV) {
    const originalWarn = console.warn
    console.warn = (...args) => {
      if (args[0]?.includes?.('[Vue warn]')) {
        errorHandler.handle(args.join(' '), {
          showMessage: false,
          logToConsole: true,
          reportToServer: false
        })
      }
      originalWarn.apply(console, args)
    }
  }
}

export default errorHandler
