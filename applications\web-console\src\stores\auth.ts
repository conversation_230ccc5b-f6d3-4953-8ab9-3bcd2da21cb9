import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login as apiLogin, logout as apiLogout, type LoginRequest, type LoginResponse } from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(localStorage.getItem('token'))
  const username = ref<string | null>(localStorage.getItem('username'))
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)

  /**
   * 用户登录
   * @param loginData 登录数据
   */
  const login = async (loginData: LoginRequest): Promise<void> => {
    isLoading.value = true
    try {
      const response: LoginResponse = await apiLogin(loginData)
      
      // 保存到状态
      token.value = response.token
      username.value = response.username
      
      // 保存到 localStorage
      localStorage.setItem('token', response.token)
      localStorage.setItem('username', response.username)
      
      console.log('登录成功:', response.username)
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 用户登出
   */
  const logout = () => {
    // 清除状态
    token.value = null
    username.value = null
    
    // 调用 API 登出（清除 localStorage）
    apiLogout()
  }

  /**
   * 初始化认证状态
   * 从 localStorage 恢复状态
   */
  const initAuth = () => {
    const savedToken = localStorage.getItem('token')
    const savedUsername = localStorage.getItem('username')
    
    if (savedToken && savedUsername) {
      token.value = savedToken
      username.value = savedUsername
    }
  }

  /**
   * 清除认证状态
   */
  const clearAuth = () => {
    token.value = null
    username.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('username')
  }

  return {
    // 状态
    token,
    username,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    
    // 方法
    login,
    logout,
    initAuth,
    clearAuth
  }
})
