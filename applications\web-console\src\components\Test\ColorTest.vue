<template>
  <div class="color-test">
    <h2>颜色对比度测试</h2>
    
    <!-- 欢迎卡片测试 -->
    <div class="test-section">
      <h3>欢迎卡片颜色测试</h3>
      <div class="welcome-card-test">
        <div class="welcome-header">
          <div class="user-info">
            <el-avatar :size="64" class="user-avatar">T</el-avatar>
            <div class="user-details">
              <h2 class="welcome-title">
                <span class="greeting">早上好，</span>
                <span class="username-highlight">测试用户！</span>
              </h2>
              <p class="welcome-subtitle">
                <el-icon class="date-icon"><Calendar /></el-icon>
                今天是 2025年1月18日，祝您工作愉快！
              </p>
              <div class="user-status">
                <div class="online-indicator">
                  <div class="status-dot"></div>
                  <span>在线</span>
                </div>
                <div class="weather-info">
                  <el-icon class="weather-icon"><Sunny /></el-icon>
                  <span>晴朗 22°C</span>
                </div>
              </div>
            </div>
          </div>
          <div class="quick-stats">
            <div class="quick-stat-item">
              <div class="stat-value">12</div>
              <div class="stat-label">待处理任务</div>
            </div>
            <div class="quick-stat-item">
              <div class="stat-value">5</div>
              <div class="stat-label">新政策</div>
            </div>
          </div>
        </div>
        
        <div class="welcome-actions">
          <el-button type="primary" size="large" class="action-btn primary-action">
            <el-icon><Plus /></el-icon>
            创建新任务
          </el-button>
          <el-button size="large" class="action-btn secondary-action">
            <el-icon><Search /></el-icon>
            查询数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 文字颜色测试 -->
    <div class="test-section">
      <h3>文字颜色测试</h3>
      <div class="text-samples">
        <div class="text-sample">
          <span class="text-primary">主要文字 (--text-primary)</span>
        </div>
        <div class="text-sample">
          <span class="text-secondary">次要文字 (--text-secondary)</span>
        </div>
        <div class="text-sample">
          <span class="text-tertiary">第三级文字 (--text-tertiary)</span>
        </div>
      </div>
    </div>

    <!-- 背景颜色测试 -->
    <div class="test-section">
      <h3>背景颜色测试</h3>
      <div class="bg-samples">
        <div class="bg-sample bg-primary">
          <span>主要背景 (--bg-primary)</span>
        </div>
        <div class="bg-sample bg-secondary">
          <span>次要背景 (--bg-secondary)</span>
        </div>
        <div class="bg-sample bg-tertiary">
          <span>第三级背景 (--bg-tertiary)</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Calendar, Sunny, Plus, Search } from '@element-plus/icons-vue'
</script>

<style scoped>
.color-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
}

.test-section h3 {
  margin-top: 0;
  color: var(--text-primary);
}

/* 欢迎卡片测试样式 */
.welcome-card-test {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: var(--radius-lg);
  padding: 32px;
  position: relative;
  overflow: hidden;
}

.welcome-card-test::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.welcome-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

.user-info {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex: 1;
}

.user-avatar {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  color: white;
  font-weight: 700;
  font-size: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  flex-shrink: 0;
}

.user-details {
  flex: 1;
}

.welcome-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
  color: #ffffff;
}

.greeting {
  color: rgba(255, 255, 255, 0.95);
}

.username-highlight {
  color: #ffd700;
  font-weight: 800;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.welcome-subtitle {
  margin: 0 0 12px 0;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.9);
}

.date-icon {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
}

.user-status {
  display: flex;
  align-items: center;
  gap: 16px;
}

.online-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
  box-shadow: 0 0 4px rgba(16, 185, 129, 0.5);
}

.weather-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.85);
  font-weight: 500;
}

.weather-icon {
  font-size: 14px;
  color: #ffd700;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.quick-stats {
  display: flex;
  gap: 24px;
}

.quick-stat-item {
  text-align: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.welcome-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-start;
  position: relative;
  z-index: 1;
}

.action-btn {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: #ffffff !important;
  font-weight: 600;
  transition: all 0.3s ease;
  flex: 0 0 auto;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.primary-action {
  background: rgba(255, 255, 255, 0.25) !important;
  border: 1px solid rgba(255, 255, 255, 0.4) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.secondary-action {
  background: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* 文字样本 */
.text-samples {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.text-sample {
  padding: 12px;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
}

.text-primary {
  color: var(--text-primary);
  font-weight: 600;
}

.text-secondary {
  color: var(--text-secondary);
  font-weight: 500;
}

.text-tertiary {
  color: var(--text-tertiary);
  font-weight: 400;
}

/* 背景样本 */
.bg-samples {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.bg-sample {
  padding: 20px;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  min-width: 200px;
  text-align: center;
  font-weight: 600;
}

.bg-primary {
  background: var(--bg-primary);
  color: var(--text-primary);
}

.bg-secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.bg-tertiary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}
</style>
