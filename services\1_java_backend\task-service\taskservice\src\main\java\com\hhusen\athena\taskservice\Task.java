package com.hhusen.athena.taskservice;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import java.sql.Timestamp;

@Entity
@Table(name = "tasks") // Optional: define table name explicitly
public class Task implements java.io.Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String name;

    private String status;
    
    private String mlflowRunId;
    
    private Double rainfall;
    
    private Double temperature;
    
    private Double waterLevel;
    
    private Integer population;
    
    private Double netWaterWithdrawal;
    
    private String cityName;
    
    private Double initialWaterLevel;

    @Column(name = "created_at", updatable = false)
    @CreationTimestamp
    private Timestamp createdAt;

    // Constructors
    public Task() {
    }

    public Task(String name, String status) {
        this.name = name;
        this.status = status;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getMlflowRunId() {
        return mlflowRunId;
    }
    
    public void setMlflowRunId(String mlflowRunId) {
        this.mlflowRunId = mlflowRunId;
    }
    
    public Double getRainfall() {
        return rainfall;
    }
    
    public void setRainfall(Double rainfall) {
        this.rainfall = rainfall;
    }
    
    public Double getTemperature() {
        return temperature;
    }
    
    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }
    
    public Double getWaterLevel() {
        return waterLevel;
    }
    
    public void setWaterLevel(Double waterLevel) {
        this.waterLevel = waterLevel;
    }
    
    public Integer getPopulation() {
        return population;
    }
    
    public void setPopulation(Integer population) {
        this.population = population;
    }
    
    public Double getNetWaterWithdrawal() {
        return netWaterWithdrawal;
    }
    
    public void setNetWaterWithdrawal(Double netWaterWithdrawal) {
        this.netWaterWithdrawal = netWaterWithdrawal;
    }
    
    public String getCityName() {
        return cityName;
    }
    
    public void setCityName(String cityName) {
        this.cityName = cityName;
    }
    
    public Double getInitialWaterLevel() {
        return initialWaterLevel;
    }
    
    public void setInitialWaterLevel(Double initialWaterLevel) {
        this.initialWaterLevel = initialWaterLevel;
    }

    public Timestamp getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "Task{" +
               "id=" + id +
               ", name='" + name + '\'' +
               ", status='" + status + '\'' +
               ", mlflowRunId='" + mlflowRunId + '\'' +
               ", rainfall=" + rainfall +
               ", temperature=" + temperature +
               ", waterLevel=" + waterLevel +
               ", population=" + population +
               ", netWaterWithdrawal=" + netWaterWithdrawal +
               ", cityName='" + cityName + '\'' +
               ", initialWaterLevel=" + initialWaterLevel +
               ", createdAt=" + createdAt +
               '}';
    }
} 