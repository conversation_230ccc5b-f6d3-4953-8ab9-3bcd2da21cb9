server:
  port: 8080

spring:
  application:
    name: api-gateway
  cloud:
    gateway:
      routes:
        - id: user_service_route
          uri: http://user-service-svc:8080
          predicates:
            - Path=/auth/**
        - id: task_service_route
          uri: http://task-service-svc:80
          predicates:
            - Path=/api/tasks/**
          filters:
            - StripPrefix=1
        - id: admin_service_route
          uri: http://admin-service-svc:80
          predicates:
            - Path=/api/admin/**

# 禁用 Eureka 客户端
eureka:
  client:
    enabled: false
    register-with-eureka: false
    fetch-registry: false

# 禁用服务发现
management:
  endpoints:
    web:
      exposure:
        include: health,info