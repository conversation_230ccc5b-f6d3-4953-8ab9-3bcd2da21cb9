<template>
  <div class="whitelist-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">白名单管理</h1>
          <p class="page-description">管理系统登录白名单，控制用户访问权限</p>
        </div>
        <div class="header-actions">
          <el-button 
            type="primary" 
            :icon="Plus" 
            @click="showAddDialog"
          >
            添加用户
          </el-button>
          <el-button 
            type="info" 
            :icon="Setting" 
            @click="showConfigDialog"
          >
            配置
          </el-button>
          <el-button 
            type="warning" 
            :icon="Document" 
            @click="showLogsDialog"
          >
            登录日志
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon enabled">
            <el-icon><User /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ enabledUsers.length }}</div>
            <div class="stat-label">启用用户</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon disabled">
            <el-icon><UserFilled /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ disabledUsers.length }}</div>
            <div class="stat-label">禁用用户</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon success">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.successfulToday }}</div>
            <div class="stat-label">今日成功登录</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon failed">
            <el-icon><CircleClose /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.failedToday }}</div>
            <div class="stat-label">今日失败尝试</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 白名单状态 -->
    <el-card class="status-card">
      <div class="status-header">
        <h3>白名单状态</h3>
        <el-switch
          v-model="whitelistEnabled"
          @change="toggleWhitelist"
          active-text="已启用"
          inactive-text="已禁用"
          :active-color="whitelistEnabled ? '#67c23a' : '#f56c6c'"
        />
      </div>
      <div class="status-info">
        <p v-if="whitelistEnabled" class="status-enabled">
          <el-icon><CircleCheck /></el-icon>
          白名单验证已启用，只有白名单中的用户可以登录系统
        </p>
        <p v-else class="status-disabled">
          <el-icon><Warning /></el-icon>
          白名单验证已禁用，所有用户都可以尝试登录系统
        </p>
      </div>
    </el-card>

    <!-- 用户列表 -->
    <el-card class="users-card">
      <template #header>
        <div class="card-header">
          <span>白名单用户</span>
          <el-input
            v-model="searchText"
            placeholder="搜索用户..."
            :prefix-icon="Search"
            style="width: 300px"
            clearable
          />
        </div>
      </template>

      <el-table
        :data="filteredUsers"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="username" label="用户名" width="150" />
        <el-table-column prop="displayName" label="显示名称" width="150" />
        <el-table-column prop="role" label="角色" width="120">
          <template #default="{ row }">
            <el-tag :type="getRoleTagType(row.role)" size="small">
              {{ getRoleDisplayName(row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="department" label="部门" width="120" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="enabled" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.enabled ? 'success' : 'danger'" size="small">
              {{ row.enabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastLogin" label="最后登录" width="180">
          <template #default="{ row }">
            {{ row.lastLogin ? formatDate(row.lastLogin) : '从未登录' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row, $index }">
            <el-button
              type="primary"
              size="small"
              :icon="Edit"
              @click="editUser(row, $index)"
            >
              编辑
            </el-button>
            <el-button
              :type="row.enabled ? 'warning' : 'success'"
              size="small"
              @click="toggleUserStatus(row, $index)"
            >
              {{ row.enabled ? '禁用' : '启用' }}
            </el-button>
            <el-button
              type="danger"
              size="small"
              :icon="Delete"
              @click="deleteUser(row, $index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑用户对话框 -->
    <el-dialog
      v-model="userDialogVisible"
      :title="isEditing ? '编辑用户' : '添加用户'"
      width="500px"
      @close="resetUserForm"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="userForm.username"
            placeholder="请输入用户名"
            :disabled="isEditing"
          />
        </el-form-item>
        <el-form-item label="显示名称" prop="displayName">
          <el-input
            v-model="userForm.displayName"
            placeholder="请输入显示名称"
          />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色" style="width: 100%">
            <el-option label="管理员" value="admin" />
            <el-option label="运维人员" value="operator" />
            <el-option label="开发人员" value="developer" />
            <el-option label="普通用户" value="user" />
            <el-option label="只读用户" value="viewer" />
          </el-select>
        </el-form-item>
        <el-form-item label="部门" prop="department">
          <el-input
            v-model="userForm.department"
            placeholder="请输入部门"
          />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="userForm.email"
            placeholder="请输入邮箱地址"
          />
        </el-form-item>
        <el-form-item label="状态" prop="enabled">
          <el-switch
            v-model="userForm.enabled"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="userDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveUser">确定</el-button>
      </template>
    </el-dialog>

    <!-- 配置对话框 -->
    <el-dialog
      v-model="configDialogVisible"
      title="白名单配置"
      width="400px"
    >
      <el-form label-width="150px">
        <el-form-item label="启用白名单">
          <el-switch v-model="config.enabled" />
        </el-form-item>
        <el-form-item label="大小写不敏感">
          <el-switch v-model="config.caseInsensitive" />
        </el-form-item>
        <el-form-item label="记录登录尝试">
          <el-switch v-model="config.logAttempts" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="configDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveConfig">保存</el-button>
      </template>
    </el-dialog>

    <!-- 登录日志对话框 -->
    <el-dialog
      v-model="logsDialogVisible"
      title="登录日志"
      width="80%"
    >
      <div class="logs-header">
        <el-button type="danger" size="small" @click="clearLogs">清空日志</el-button>
        <el-button type="primary" size="small" @click="exportLogs">导出日志</el-button>
      </div>
      <el-table :data="loginLogs" stripe max-height="400">
        <el-table-column prop="timestamp" label="时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="success" label="结果" width="100">
          <template #default="{ row }">
            <el-tag :type="row.success ? 'success' : 'danger'" size="small">
              {{ row.success ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="ip" label="IP地址" width="150" />
        <el-table-column prop="userAgent" label="用户代理" show-overflow-tooltip />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Setting,
  Document,
  User,
  UserFilled,
  CircleCheck,
  CircleClose,
  Warning,
  Search,
  Edit,
  Delete
} from '@element-plus/icons-vue'
import {
  WHITELIST_USERS,
  WHITELIST_CONFIG,
  type WhitelistUser
} from '@/config/whitelist'
import WhitelistService from '@/services/whitelistService'

// 响应式数据
const searchText = ref('')
const whitelistEnabled = ref(WHITELIST_CONFIG.enabled)
const users = ref<WhitelistUser[]>([...WHITELIST_USERS])
const stats = ref(WhitelistService.getWhitelistStats())

// 对话框状态
const userDialogVisible = ref(false)
const configDialogVisible = ref(false)
const logsDialogVisible = ref(false)
const isEditing = ref(false)
const editingIndex = ref(-1)

// 表单数据
const userForm = reactive({
  username: '',
  displayName: '',
  role: 'user',
  department: '',
  email: '',
  enabled: true
})

const config = reactive({
  enabled: WHITELIST_CONFIG.enabled,
  caseInsensitive: WHITELIST_CONFIG.caseInsensitive,
  logAttempts: WHITELIST_CONFIG.logAttempts
})

// 表单验证规则
const userFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 50, message: '用户名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  displayName: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 计算属性
const enabledUsers = computed(() => users.value.filter(user => user.enabled))
const disabledUsers = computed(() => users.value.filter(user => !user.enabled))

const filteredUsers = computed(() => {
  if (!searchText.value) return users.value

  const search = searchText.value.toLowerCase()
  return users.value.filter(user =>
    user.username.toLowerCase().includes(search) ||
    (user.displayName && user.displayName.toLowerCase().includes(search)) ||
    (user.email && user.email.toLowerCase().includes(search)) ||
    (user.department && user.department.toLowerCase().includes(search))
  )
})

const loginLogs = computed(() => {
  return WhitelistService.getLoginAttempts().reverse()
})

// 方法
const getRoleTagType = (role: string) => {
  const types: Record<string, string> = {
    admin: 'danger',
    operator: 'warning',
    developer: 'primary',
    user: 'success',
    viewer: 'info'
  }
  return types[role] || 'info'
}

const getRoleDisplayName = (role: string) => {
  const names: Record<string, string> = {
    admin: '管理员',
    operator: '运维人员',
    developer: '开发人员',
    user: '普通用户',
    viewer: '只读用户'
  }
  return names[role] || role
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const showAddDialog = () => {
  isEditing.value = false
  resetUserForm()
  userDialogVisible.value = true
}

const editUser = (user: WhitelistUser, index: number) => {
  isEditing.value = true
  editingIndex.value = index
  Object.assign(userForm, user)
  userDialogVisible.value = true
}

const resetUserForm = () => {
  Object.assign(userForm, {
    username: '',
    displayName: '',
    role: 'user',
    department: '',
    email: '',
    enabled: true
  })
}

const saveUser = () => {
  if (isEditing.value) {
    // 编辑用户
    Object.assign(users.value[editingIndex.value], userForm)
    ElMessage.success('用户信息已更新')
  } else {
    // 添加用户
    const existingUser = users.value.find(u => u.username === userForm.username)
    if (existingUser) {
      ElMessage.error('用户名已存在')
      return
    }

    users.value.push({
      ...userForm,
      createdAt: new Date().toISOString()
    })
    ElMessage.success('用户已添加')
  }

  userDialogVisible.value = false
  updateStats()
}

const toggleUserStatus = (user: WhitelistUser, index: number) => {
  user.enabled = !user.enabled
  ElMessage.success(`用户已${user.enabled ? '启用' : '禁用'}`)
  updateStats()
}

const deleteUser = async (user: WhitelistUser, index: number) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    users.value.splice(index, 1)
    ElMessage.success('用户已删除')
    updateStats()
  } catch (error) {
    // 用户取消删除
  }
}

const toggleWhitelist = (enabled: boolean) => {
  WHITELIST_CONFIG.enabled = enabled
  config.enabled = enabled
  ElMessage.success(`白名单验证已${enabled ? '启用' : '禁用'}`)
}

const showConfigDialog = () => {
  configDialogVisible.value = true
}

const saveConfig = () => {
  Object.assign(WHITELIST_CONFIG, config)
  configDialogVisible.value = false
  ElMessage.success('配置已保存')
}

const showLogsDialog = () => {
  logsDialogVisible.value = true
}

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有登录日志吗？',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    WhitelistService.clearLoginAttempts()
    updateStats()
    ElMessage.success('登录日志已清空')
  } catch (error) {
    // 用户取消
  }
}

const exportLogs = () => {
  const logs = WhitelistService.getLoginAttempts()
  const csvContent = [
    ['时间', '用户名', '结果', 'IP地址', '用户代理'].join(','),
    ...logs.map(log => [
      log.timestamp,
      log.username,
      log.success ? '成功' : '失败',
      log.ip,
      `"${log.userAgent}"`
    ].join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `login_logs_${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  URL.revokeObjectURL(url)

  ElMessage.success('日志已导出')
}

const updateStats = () => {
  stats.value = WhitelistService.getWhitelistStats()
}

// 生命周期
onMounted(() => {
  updateStats()
})
</script>

<style scoped>
.whitelist-management {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-description {
  color: #606266;
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.enabled {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.stat-icon.disabled {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.stat-icon.success {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.stat-icon.failed {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

/* 状态卡片 */
.status-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.status-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.status-info {
  padding: 16px;
  border-radius: 6px;
  background-color: #f8f9fa;
}

.status-enabled {
  color: #67c23a;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

.status-disabled {
  color: #e6a23c;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

/* 用户卡片 */
.users-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 日志对话框 */
.logs-header {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .whitelist-management {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }

  .status-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #fafafa;
}

:deep(.el-table__header th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  background-color: #fafafa;
  border-radius: 8px 8px 0 0;
  padding: 20px 24px;
}

:deep(.el-dialog__title) {
  font-weight: 600;
  color: #303133;
}

/* 按钮组样式 */
.header-actions .el-button {
  border-radius: 6px;
  font-weight: 500;
}

/* 开关样式 */
:deep(.el-switch) {
  --el-switch-on-color: #67c23a;
  --el-switch-off-color: #dcdfe6;
}

/* 标签样式 */
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}

/* 输入框样式 */
:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

/* 卡片样式 */
:deep(.el-card) {
  border: none;
  border-radius: 8px;
}

:deep(.el-card__body) {
  padding: 24px;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

/* 消息样式 */
:deep(.el-message) {
  border-radius: 6px;
}

:deep(.el-notification) {
  border-radius: 8px;
}
</style>
