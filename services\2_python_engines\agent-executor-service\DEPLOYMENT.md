# Agent Executor Service 部署指南

## 🚀 Kubernetes 部署

### 前置条件

1. **确保 llm-api-secret 存在**
   ```bash
   kubectl get secret llm-api-secret
   ```
   
   如果不存在，需要先创建：
   ```bash
   kubectl apply -f ../llm-service/k8s/llm-secrets.yaml
   ```

2. **确保依赖服务运行正常**
   ```bash
   kubectl get pods | grep -E "(task-service|llm-service|knowledge-service|mlflow)"
   ```

### 部署步骤

1. **构建 Docker 镜像**
   ```bash
   cd services/2_python_engines/agent-executor-service
   docker build -t athena/agent-executor-service:latest .
   ```

2. **部署到 Kubernetes**
   ```bash
   kubectl apply -f k8s/deployment.yaml
   ```

3. **验证部署状态**
   ```bash
   # 检查 Pod 状态
   kubectl get pods -l app=agent-executor-service
   
   # 检查服务状态
   kubectl get svc agent-executor-service
   
   # 查看日志
   kubectl logs -l app=agent-executor-service -f
   ```

4. **获取外部访问地址**
   ```bash
   # 获取 NodePort
   kubectl get svc agent-executor-service -o jsonpath='{.spec.ports[0].nodePort}'
   
   # 获取节点 IP
   kubectl get nodes -o wide
   ```

### 配置说明

#### 环境变量配置

| 变量名 | 来源 | 说明 |
|--------|------|------|
| `DASHSCOPE_API_KEY` | Secret: llm-api-secret | 通义千问 API 密钥（主要） |
| `OPENAI_API_KEY` | Secret: llm-api-secret | OpenAI API 密钥（备选） |
| `QWEN_BASE_URL` | ConfigMap | 通义千问 API 地址 |
| `TASK_SERVICE_URL` | ConfigMap | 任务服务地址 |
| `LLM_SERVICE_URL` | ConfigMap | LLM 服务地址 |
| `KNOWLEDGE_SERVICE_URL` | ConfigMap | 知识服务地址 |
| `MLFLOW_TRACKING_URI` | ConfigMap | MLflow 服务地址 |

#### 资源配置

- **CPU**: 请求 50m，限制 200m
- **内存**: 请求 128Mi，限制 256Mi
- **副本数**: 1（可根据需要调整）

#### 健康检查

- **存活探针**: GET /health，30秒后开始，每10秒检查
- **就绪探针**: GET /health，5秒后开始，每5秒检查

## 🔧 本地开发

### 环境准备

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **设置环境变量**
   ```bash
   export DASHSCOPE_API_KEY="sk-f697d5747a64411987aa0fad9c8e26aa"
   export TASK_SERVICE_URL="http://localhost:8080"
   export LLM_SERVICE_URL="http://localhost:8001"
   ```

3. **启动服务**
   ```bash
   python main.py
   ```

### 测试验证

1. **健康检查**
   ```bash
   curl http://localhost:8000/health
   ```

2. **服务信息**
   ```bash
   curl http://localhost:8000/
   ```

3. **工具定义**
   ```bash
   curl http://localhost:8000/tools-schema
   ```

4. **Agent 测试**
   ```bash
   python test_agent.py
   ```

## 🌐 外部访问

### 通过 NodePort 访问

1. **获取访问地址**
   ```bash
   NODE_IP=$(kubectl get nodes -o jsonpath='{.items[0].status.addresses[?(@.type=="InternalIP")].address}')
   NODE_PORT=$(kubectl get svc agent-executor-service -o jsonpath='{.spec.ports[0].nodePort}')
   echo "Service URL: http://$NODE_IP:$NODE_PORT"
   ```

2. **测试外部访问**
   ```bash
   curl http://$NODE_IP:$NODE_PORT/health
   ```

### API 调用示例

```bash
# 执行 Agent 命令
curl -X POST "http://$NODE_IP:$NODE_PORT/execute-command" \
  -H "Content-Type: application/json" \
  -d '{"command": "我想为北京创建一个水资源模拟，降雨量50mm，温度25度，人口2000万"}'

# 获取工具定义
curl "http://$NODE_IP:$NODE_PORT/tools-schema"
```

## 🐛 故障排除

### 常见问题

1. **Pod 启动失败**
   ```bash
   kubectl describe pod -l app=agent-executor-service
   kubectl logs -l app=agent-executor-service
   ```

2. **API 密钥问题**
   ```bash
   kubectl get secret llm-api-secret -o yaml
   ```

3. **服务连接问题**
   ```bash
   kubectl get svc
   kubectl get endpoints agent-executor-service
   ```

4. **依赖服务不可用**
   ```bash
   kubectl get pods | grep -E "(task-service|llm-service)"
   ```

### 日志分析

```bash
# 实时查看日志
kubectl logs -l app=agent-executor-service -f

# 查看特定时间段的日志
kubectl logs -l app=agent-executor-service --since=1h

# 查看前一个容器的日志
kubectl logs -l app=agent-executor-service --previous
```

## 📊 监控和维护

### 性能监控

- 通过 `/health` 端点监控服务状态
- 通过 Kubernetes metrics 监控资源使用
- 通过日志监控 Agent 执行情况

### 扩容建议

```yaml
# 水平扩容
spec:
  replicas: 3  # 增加副本数

# 垂直扩容
resources:
  requests:
    memory: "256Mi"
    cpu: "100m"
  limits:
    memory: "512Mi"
    cpu: "500m"
```

### 更新部署

```bash
# 更新镜像
kubectl set image deployment/agent-executor-service agent-executor-service=athena/agent-executor-service:v2.0.0

# 滚动重启
kubectl rollout restart deployment/agent-executor-service

# 查看更新状态
kubectl rollout status deployment/agent-executor-service
```
