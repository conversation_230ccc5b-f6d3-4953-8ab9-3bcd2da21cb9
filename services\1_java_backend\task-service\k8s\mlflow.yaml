apiVersion: apps/v1
kind: Deployment
metadata:
  name: mlflow-deployment
  labels:
    app: mlflow
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mlflow
  template:
    metadata:
      labels:
        app: mlflow
    spec:
      containers:
      - name: mlflow
        image: ghcr.io/mlflow/mlflow:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 5000
        env:
        - name: MLFLOW_TRACKING_URI
          value: ***************************************************/athena
        - name: MLFLOW_S3_ENDPOINT_URL
          value: http://minio-svc:9000
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: minio-secret
              key: accessKey
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: minio-secret
              key: secretKey
        command: ["mlflow", "server", "--host", "0.0.0.0"]
---
apiVersion: v1
kind: Service
metadata:
  name: mlflow-svc
spec:
  type: NodePort
  ports:
  - port: 5000
    targetPort: 5000
    protocol: TCP
  selector:
    app: mlflow
