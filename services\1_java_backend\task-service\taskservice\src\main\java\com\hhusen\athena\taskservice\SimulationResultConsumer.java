package com.hhusen.athena.taskservice;

import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.mlflow.tracking.MlflowClient;

import java.util.Map;
import java.util.Optional;

@Component
public class SimulationResultConsumer {

    @Autowired
    private TaskRepository taskRepository;

    @Autowired
    private MlflowClient mlflowClient;

    @RabbitListener(queues = "simulation_results")
    public void receiveResult(Map<String, Object> result) {
        Long taskId = ((Number) result.get("taskId")).longValue();
        Double waterLevel = ((Number) result.get("waterLevel")).doubleValue();
        
        System.out.println("Received simulation result for task " + taskId + ": waterLevel = " + waterLevel);
        
        Optional<Task> optionalTask = taskRepository.findById(taskId);
        if (optionalTask.isPresent()) {
            Task task = optionalTask.get();
            task.setWaterLevel(waterLevel);
            task.setStatus("COMPLETED");
            
            // 保存更新后的Task到数据库
            taskRepository.save(task);
            
            // 在MLflow中记录最终水位
            String runId = task.getMlflowRunId();
            if (runId != null && !runId.isEmpty()) {
                try {
                    mlflowClient.logMetric(runId, "final_water_level", waterLevel);
                    System.out.println("Updated MLflow run " + runId + " with final water level: " + waterLevel);
                } catch (Exception e) {
                    System.err.println("Error updating MLflow: " + e.getMessage());
                }
            }
        } else {
            System.err.println("Task not found with ID: " + taskId);
        }
    }
} 