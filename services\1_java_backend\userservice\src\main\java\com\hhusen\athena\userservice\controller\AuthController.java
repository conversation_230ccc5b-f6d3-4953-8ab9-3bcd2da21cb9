package com.hhusen.athena.userservice.controller;

import com.hhusen.athena.userservice.model.LoginRequest;
import com.hhusen.athena.userservice.model.LoginResponse;
import com.hhusen.athena.userservice.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/auth")
public class AuthController {

    private final JwtUtil jwtUtil;

    @Autowired
    public AuthController(JwtUtil jwtUtil) {
        this.jwtUtil = jwtUtil;
    }

    @PostMapping("/login")
    public ResponseEntity<LoginResponse> login(@RequestBody LoginRequest request) {
        // 这里应该有实际的用户验证逻辑，这里简化为只要提供了用户名和密码就验证通过
        // 实际应用中，应该查询数据库验证用户名和密码
        
        if (request.getUsername() != null && !request.getUsername().isEmpty() 
                && request.getPassword() != null && !request.getPassword().isEmpty()) {
            
            // 生成JWT令牌
            String token = jwtUtil.generateToken(request.getUsername());
            
            // 创建并返回响应
            LoginResponse response = new LoginResponse(token, request.getUsername());
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.badRequest().build();
        }
    }
} 