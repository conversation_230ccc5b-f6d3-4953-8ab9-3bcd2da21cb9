apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: chromadb-pvc
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: chromadb-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: chromadb
  template:
    metadata:
      labels:
        app: chromadb
    spec:
      containers:
      - name: chromadb
        image: ghcr.io/chroma-core/chroma:0.4.24
        ports:
        - containerPort: 8000
        env:
        - name: CHROMA_SERVER_HOST
          value: "0.0.0.0"
        - name: CHROMA_SERVER_HTTP_PORT
          value: "8000"
        - name: CHROMA_PERSIST_DIRECTORY
          value: "/chroma/chroma"
        - name: ALLOW_RESET
          value: "true"
        volumeMounts:
        - name: chromadb-storage
          mountPath: /chroma/chroma
      volumes:
      - name: chromadb-storage
        persistentVolumeClaim:
          claimName: chromadb-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: chromadb-svc
spec:
  type: ClusterIP
  selector:
    app: chromadb
  ports:
  - protocol: TCP
    port: 8000
    targetPort: 8000
