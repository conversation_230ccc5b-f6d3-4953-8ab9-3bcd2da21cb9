# Admin Service 部署指南

本指南将帮助你完整部署 Kubernetes 管理服务，实现集群状态监控功能。

## 🎯 功能概览

- **实时 Pod 监控**: 查看所有 Pod 的状态、重启次数、运行时间
- **流式日志查看**: 实时查看 Pod 日志，支持多容器选择
- **一键重启**: 重启 Deployment 及其所有 Pod
- **动态扩缩容**: 调整 Deployment 的副本数量
- **安全访问**: 通过 RBAC 安全访问 Kubernetes API

## 📋 部署步骤

### 1. 构建和部署后端服务

```bash
# 进入 admin-service 目录
cd services/2_python_engines/admin-service

# 构建并部署到 minikube
./build.sh
```

这个脚本会：
- 构建 Docker 镜像 `athena/admin-service:0.0.1`
- 将镜像加载到 minikube
- 创建 ServiceAccount 和 RBAC 权限
- 部署服务到 Kubernetes

### 2. 验证后端服务

```bash
# 检查 Pod 状态
kubectl get pods -l app=admin-service

# 检查服务状态
kubectl get svc admin-service-svc

# 查看服务日志
kubectl logs -l app=admin-service -f

# 测试健康检查
kubectl port-forward svc/admin-service-svc 8080:80
curl http://localhost:8080/health
```

### 3. 更新 API Gateway

API Gateway 配置已自动更新，包含以下路由：

```yaml
- id: admin_service_route
  uri: http://admin-service-svc:80
  predicates:
    - Path=/api/admin/**
```

### 4. 重新构建前端

```bash
# 进入前端目录
cd applications/web-console

# 安装依赖（如果需要）
npm install

# 构建前端
npm run build
```

### 5. 重启相关服务

```bash
# 重启 API Gateway（如果需要）
kubectl rollout restart deployment/apigateway

# 重启前端服务（如果需要）
kubectl rollout restart deployment/web-console
```

## 🔍 验证部署

### 1. 检查服务状态

```bash
# 查看所有相关服务
kubectl get pods,svc -l app=admin-service
kubectl get pods,svc -l app=apigateway
kubectl get pods,svc -l app=web-console

# 检查 RBAC 配置
kubectl get serviceaccount admin-service-sa
kubectl get clusterrole admin-service-role
kubectl get clusterrolebinding admin-service-binding
```

### 2. 测试 API 端点

```bash
# 端口转发到 API Gateway
kubectl port-forward svc/apigateway-svc 8080:80

# 测试 Pod 列表 API
curl http://localhost:8080/api/admin/pods

# 测试健康检查
curl http://localhost:8080/api/admin/health
```

### 3. 验证前端功能

1. **访问 Web Console**:
   ```bash
   kubectl port-forward svc/web-console-svc 3000:80
   ```
   打开 http://localhost:3000

2. **登录系统**:
   - 使用你的用户凭据登录

3. **访问集群状态页面**:
   - 在侧边栏找到"集群管理" → "集群状态"
   - 点击进入集群监控页面

4. **验证功能**:
   - ✅ 查看 Pod 列表和状态统计
   - ✅ 点击"查看日志"按钮，查看实时日志流
   - ✅ 尝试重启某个 Deployment
   - ✅ 尝试扩缩容操作

## 🐛 故障排除

### 1. 后端服务无法启动

**问题**: Pod 一直处于 Pending 或 CrashLoopBackOff 状态

**解决方案**:
```bash
# 查看详细错误信息
kubectl describe pod -l app=admin-service
kubectl logs -l app=admin-service --tail=50

# 常见问题：
# - 镜像未正确加载：重新运行 build.sh
# - 权限问题：检查 RBAC 配置
# - 资源不足：检查 minikube 资源
```

### 2. 权限错误

**问题**: API 调用返回 403 Forbidden

**解决方案**:
```bash
# 检查 ServiceAccount
kubectl get serviceaccount admin-service-sa -o yaml

# 检查 ClusterRole
kubectl get clusterrole admin-service-role -o yaml

# 检查 ClusterRoleBinding
kubectl get clusterrolebinding admin-service-binding -o yaml

# 重新应用 RBAC 配置
kubectl apply -f services/2_python_engines/admin-service/k8s/rbac.yaml
```

### 3. 前端无法连接后端

**问题**: 前端显示网络错误或 404

**解决方案**:
```bash
# 检查 API Gateway 路由配置
kubectl get configmap apigateway-config -o yaml

# 检查服务连通性
kubectl exec -it deployment/apigateway -- curl http://admin-service-svc/health

# 重启 API Gateway
kubectl rollout restart deployment/apigateway
```

### 4. 日志流无法显示

**问题**: 点击"查看日志"没有内容或报错

**解决方案**:
```bash
# 检查 Pod 是否存在日志
kubectl logs <pod-name> -n <namespace>

# 检查浏览器控制台错误
# 确保浏览器支持 ReadableStream API

# 检查 CORS 配置
# 确保 admin-service 的 CORS 设置正确
```

## 🔄 版本更新

### 更新到新版本

```bash
# 更新版本号
cd services/2_python_engines/admin-service
./update-version.sh 0.0.2

# 重新构建部署
./build.sh
```

### 回滚到之前版本

```bash
# 查看部署历史
kubectl rollout history deployment/admin-service

# 回滚到上一个版本
kubectl rollout undo deployment/admin-service

# 回滚到指定版本
kubectl rollout undo deployment/admin-service --to-revision=1
```

## 📊 监控和日志

### 查看服务指标

```bash
# 查看 Pod 资源使用
kubectl top pods -l app=admin-service

# 查看服务事件
kubectl get events --field-selector involvedObject.name=admin-service
```

### 日志收集

```bash
# 实时查看日志
kubectl logs -l app=admin-service -f

# 导出日志到文件
kubectl logs -l app=admin-service > admin-service.log
```

## 🔐 安全注意事项

1. **最小权限原则**: RBAC 配置仅授予必要的权限
2. **网络隔离**: 服务仅在集群内部访问
3. **认证授权**: 通过 API Gateway 进行统一认证
4. **日志审计**: 所有 API 调用都有日志记录

## 📈 性能优化

1. **资源限制**: 根据实际使用调整 CPU 和内存限制
2. **副本数量**: 根据负载调整 admin-service 副本数
3. **缓存策略**: 考虑添加 Pod 列表缓存
4. **连接池**: 优化 Kubernetes API 客户端连接

## 🎉 完成

恭喜！你已经成功部署了 Kubernetes 管理服务。现在你可以：

- 🔍 实时监控集群中的所有 Pod
- 📋 查看详细的 Pod 日志
- 🔄 一键重启有问题的服务
- 📊 动态调整服务规模

享受你的新集群管理界面吧！
