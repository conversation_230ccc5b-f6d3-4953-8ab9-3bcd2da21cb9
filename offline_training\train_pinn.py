import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
from torch.autograd import grad

# Check if GPU is available
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# Define the HydroPINN model
class HydroPINN(nn.Module):
    def __init__(self, hidden_layers=3, neurons_per_layer=20):
        super(HydroPINN, self).__init__()
        
        # Input layer: rainfall and temperature
        self.input_layer = nn.Linear(2, neurons_per_layer)
        
        # Hidden layers
        self.hidden_layers = nn.ModuleList()
        for _ in range(hidden_layers):
            self.hidden_layers.append(nn.Linear(neurons_per_layer, neurons_per_layer))
        
        # Output layer: water_level
        self.output_layer = nn.Linear(neurons_per_layer, 1)
        
        # Activation function
        self.activation = nn.Tanh()
    
    def forward(self, x):
        x = self.activation(self.input_layer(x))
        for layer in self.hidden_layers:
            x = self.activation(layer(x))
        return self.output_layer(x)


# Function to generate training data
def generate_training_data(n_samples=1000):
    # Generate random inputs
    rainfall = torch.rand(n_samples, 1) * 100  # Rainfall between 0-100 mm
    temperature = torch.rand(n_samples, 1) * 40  # Temperature between 0-40 °C
    
    # Combine inputs
    inputs = torch.cat([rainfall, temperature], dim=1)
    
    # Generate "true" water level based on a complex function
    true_water_level = rainfall * 0.6 + temperature * 0.15 + torch.sin(rainfall/50) * 5
    
    return inputs.to(device), true_water_level.to(device)


# Function to generate physical constraint points (collocation points)
def generate_physics_points(n_points=500):
    # Generate points with rainfall = 0
    rainfall = torch.zeros(n_points, 1)
    temperature = torch.rand(n_points, 1) * 40  # Temperature between 0-40 °C
    
    # Combine inputs
    physics_points = torch.cat([rainfall, temperature], dim=1)
    
    return physics_points.to(device)


# Function to compute the physics loss
def compute_physics_loss(model, physics_points, target_derivative=0.1):
    # Make sure we track gradients for these points
    physics_points.requires_grad_(True)
    
    # Forward pass through the model
    predicted = model(physics_points)
    
    # Calculate gradients of the output with respect to the inputs
    grads = grad(outputs=predicted, inputs=physics_points, 
                  grad_outputs=torch.ones_like(predicted),
                  create_graph=True)[0]
    
    # Extract the gradient with respect to temperature (index 1)
    dwdt = grads[:, 1:2]  # d(water_level)/d(temperature)
    
    # The physics loss is the MSE between the actual derivative and the target
    physics_loss = torch.mean((dwdt - target_derivative) ** 2)
    
    return physics_loss


# Training function
def train_pinn(model, epochs=2000, lr=0.001, weight_data=1.0, weight_physics=0.5):
    optimizer = optim.Adam(model.parameters(), lr=lr)
    data_criterion = nn.MSELoss()
    
    # Generate data
    inputs, true_water_level = generate_training_data(n_samples=1000)
    physics_points = generate_physics_points(n_points=500)
    
    # Training history
    history = {'total_loss': [], 'data_loss': [], 'physics_loss': []}
    
    # Training loop
    for epoch in range(epochs):
        optimizer.zero_grad()
        
        # Forward pass for data points
        predicted_water_level = model(inputs)
        data_loss = data_criterion(predicted_water_level, true_water_level)
        
        # Calculate physics loss
        physics_loss = compute_physics_loss(model, physics_points)
        
        # Total loss
        total_loss = weight_data * data_loss + weight_physics * physics_loss
        
        # Backward pass
        total_loss.backward()
        optimizer.step()
        
        # Record history
        history['total_loss'].append(total_loss.item())
        history['data_loss'].append(data_loss.item())
        history['physics_loss'].append(physics_loss.item())
        
        # Print progress
        if (epoch+1) % 100 == 0:
            print(f"Epoch {epoch+1}/{epochs}, Total Loss: {total_loss.item():.6f}, "
                  f"Data Loss: {data_loss.item():.6f}, Physics Loss: {physics_loss.item():.6f}")
    
    return model, history


# Function to plot training history
def plot_training_history(history):
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 3, 1)
    plt.plot(history['total_loss'])
    plt.title('Total Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    
    plt.subplot(1, 3, 2)
    plt.plot(history['data_loss'])
    plt.title('Data Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    
    plt.subplot(1, 3, 3)
    plt.plot(history['physics_loss'])
    plt.title('Physics Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    
    plt.tight_layout()
    plt.savefig('training_history.png')
    plt.close()


if __name__ == "__main__":
    # Create the model and move it to the device
    model = HydroPINN(hidden_layers=3, neurons_per_layer=20).to(device)
    print(model)
    
    # Train the model
    model, history = train_pinn(
        model, 
        epochs=2000, 
        lr=0.001, 
        weight_data=1.0, 
        weight_physics=0.5
    )
    
    # Plot training history
    plot_training_history(history)
    
    # Save the trained model
    torch.save(model.state_dict(), 'hydro_pinn_v1.pth')
    print("Model saved as 'hydro_pinn_v1.pth'")
    
    # Validation
    print("\nValidation with some test points:")
    # Generate a few test points
    test_rainfall = torch.tensor([[0.0], [50.0], [100.0]]).to(device)
    test_temperature = torch.tensor([[20.0], [20.0], [20.0]]).to(device)
    test_inputs = torch.cat([test_rainfall, test_temperature], dim=1)
    
    # Make predictions
    with torch.no_grad():
        predicted = model(test_inputs)
    
    # Display results
    for i in range(len(test_rainfall)):
        print(f"Rainfall: {test_rainfall[i].item():.1f} mm, "
              f"Temperature: {test_temperature[i].item():.1f} °C, "
              f"Predicted Water Level: {predicted[i].item():.2f}")
