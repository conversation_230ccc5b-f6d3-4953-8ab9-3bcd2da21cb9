#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
from openai import OpenAI

"""
测试脚本：用于在本地Windows环境测试通过OpenAI库调用通义千问API
使用方法：
1. 设置环境变量DASHSCOPE_API_KEY为您的通义千问API密钥
2. 运行此脚本: python test_openai_qwen.py
"""

def test_qwen_api():
    # 从环境变量获取API密钥
    api_key = os.environ.get("DASHSCOPE_API_KEY")
    if not api_key:
        print("错误：需要设置DASHSCOPE_API_KEY环境变量")
        return

    try:
        # 初始化OpenAI客户端，配置为通义千问的兼容模式
        # 注意：正确的端点是 compatible-mode 而不是 compatible
        client = OpenAI(
            api_key=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )

        # 输出客户端配置信息
        print(f"客户端配置:")
        print(f"  API密钥: {api_key[:5]}...{api_key[-4:]}")
        print(f"  基础URL: {client.base_url}")

        # 测试消息
        messages = [
            {"role": "user", "content": "你好，请介绍一下自己"}
        ]

        # 1. 测试非流式响应
        print("\n===== 测试普通响应 =====")
        try:
            response = client.chat.completions.create(
                model="qwen-plus",
                messages=messages,
                stream=False
            )
            print("API响应成功:")
            print(f"  内容: {response.choices[0].message.content[:100]}...")
        except Exception as e:
            print(f"API请求失败: {str(e)}")

        # 2. 测试流式响应
        print("\n===== 测试流式响应 =====")
        try:
            stream_response = client.chat.completions.create(
                model="qwen-plus",
                messages=messages,
                stream=True
            )
            print("开始接收流式响应...")
            full_text = ""
            for chunk in stream_response:
                if chunk.choices and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    full_text += content
                    print(content, end="", flush=True)
            print("\n\n完整响应长度:", len(full_text))
        except Exception as e:
            print(f"流式API请求失败: {str(e)}")

    except Exception as e:
        print(f"初始化客户端时发生错误: {str(e)}")


if __name__ == "__main__":
    test_qwen_api() 