import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface LoadingState {
  id: string
  message?: string
  progress?: number
  cancelable?: boolean
  onCancel?: () => void
}

export const useLoadingStore = defineStore('loading', () => {
  // 状态
  const loadingStates = ref<Map<string, LoadingState>>(new Map())
  const globalLoading = ref(false)
  const globalMessage = ref('')

  // 计算属性
  const isLoading = computed(() => {
    return globalLoading.value || loadingStates.value.size > 0
  })

  const loadingCount = computed(() => {
    return loadingStates.value.size
  })

  const currentLoadings = computed(() => {
    return Array.from(loadingStates.value.values())
  })

  /**
   * 开始加载
   */
  const startLoading = (id: string, options?: Partial<LoadingState>) => {
    const loadingState: LoadingState = {
      id,
      message: options?.message || '加载中...',
      progress: options?.progress,
      cancelable: options?.cancelable || false,
      onCancel: options?.onCancel
    }

    loadingStates.value.set(id, loadingState)
    return id
  }

  /**
   * 停止加载
   */
  const stopLoading = (id: string) => {
    loadingStates.value.delete(id)
  }

  /**
   * 更新加载状态
   */
  const updateLoading = (id: string, updates: Partial<LoadingState>) => {
    const existing = loadingStates.value.get(id)
    if (existing) {
      loadingStates.value.set(id, { ...existing, ...updates })
    }
  }

  /**
   * 设置加载进度
   */
  const setProgress = (id: string, progress: number) => {
    updateLoading(id, { progress })
  }

  /**
   * 设置加载消息
   */
  const setMessage = (id: string, message: string) => {
    updateLoading(id, { message })
  }

  /**
   * 取消加载
   */
  const cancelLoading = (id: string) => {
    const loadingState = loadingStates.value.get(id)
    if (loadingState?.cancelable && loadingState.onCancel) {
      loadingState.onCancel()
    }
    stopLoading(id)
  }

  /**
   * 清空所有加载状态
   */
  const clearAll = () => {
    loadingStates.value.clear()
    globalLoading.value = false
    globalMessage.value = ''
  }

  /**
   * 设置全局加载状态
   */
  const setGlobalLoading = (loading: boolean, message?: string) => {
    globalLoading.value = loading
    globalMessage.value = message || ''
  }

  /**
   * 包装异步操作
   */
  const wrapAsync = async <T>(
    operation: () => Promise<T>,
    options?: {
      id?: string
      message?: string
      showProgress?: boolean
      onProgress?: (progress: number) => void
    }
  ): Promise<T> => {
    const id = options?.id || `async_${Date.now()}`
    
    try {
      startLoading(id, {
        message: options?.message,
        progress: options?.showProgress ? 0 : undefined
      })

      // 如果有进度回调，设置进度更新
      if (options?.onProgress) {
        const originalOnProgress = options.onProgress
        options.onProgress = (progress: number) => {
          setProgress(id, progress)
          originalOnProgress(progress)
        }
      }

      const result = await operation()
      return result
    } finally {
      stopLoading(id)
    }
  }

  /**
   * 批量操作加载状态
   */
  const batchOperation = async <T>(
    operations: Array<() => Promise<T>>,
    options?: {
      message?: string
      concurrent?: boolean
      onProgress?: (completed: number, total: number) => void
    }
  ): Promise<T[]> => {
    const id = `batch_${Date.now()}`
    const total = operations.length
    let completed = 0

    try {
      startLoading(id, {
        message: options?.message || `处理中 (0/${total})`,
        progress: 0
      })

      const updateProgress = () => {
        const progress = Math.round((completed / total) * 100)
        updateLoading(id, {
          message: `${options?.message || '处理中'} (${completed}/${total})`,
          progress
        })
        options?.onProgress?.(completed, total)
      }

      let results: T[]

      if (options?.concurrent) {
        // 并发执行
        const promises = operations.map(async (operation) => {
          const result = await operation()
          completed++
          updateProgress()
          return result
        })
        results = await Promise.all(promises)
      } else {
        // 串行执行
        results = []
        for (const operation of operations) {
          const result = await operation()
          results.push(result)
          completed++
          updateProgress()
        }
      }

      return results
    } finally {
      stopLoading(id)
    }
  }

  /**
   * 模拟加载进度
   */
  const simulateProgress = (
    id: string,
    duration: number = 3000,
    onComplete?: () => void
  ) => {
    let progress = 0
    const interval = 50 // 50ms更新一次
    const increment = (interval / duration) * 100

    const timer = setInterval(() => {
      progress += increment
      
      if (progress >= 100) {
        progress = 100
        setProgress(id, progress)
        clearInterval(timer)
        onComplete?.()
      } else {
        setProgress(id, progress)
      }
    }, interval)

    return () => clearInterval(timer)
  }

  /**
   * 获取加载状态
   */
  const getLoadingState = (id: string) => {
    return loadingStates.value.get(id)
  }

  /**
   * 检查是否正在加载
   */
  const isLoadingById = (id: string) => {
    return loadingStates.value.has(id)
  }

  return {
    // 状态
    loadingStates,
    globalLoading,
    globalMessage,

    // 计算属性
    isLoading,
    loadingCount,
    currentLoadings,

    // 方法
    startLoading,
    stopLoading,
    updateLoading,
    setProgress,
    setMessage,
    cancelLoading,
    clearAll,
    setGlobalLoading,
    wrapAsync,
    batchOperation,
    simulateProgress,
    getLoadingState,
    isLoadingById
  }
})
