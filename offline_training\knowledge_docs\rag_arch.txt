Retrieval-Augmented Generation (RAG) is an architecture that combines the strengths of large language models (LLMs) with external knowledge retrieval systems.
In a RAG system, when a query is received, it first retrieves relevant documents from a knowledge base (like one stored in ChromaDB). These documents are then provided as context to the LLM, which generates a more accurate and informed response.