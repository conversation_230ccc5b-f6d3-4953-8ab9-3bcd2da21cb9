<template>
  <div class="knowledge-graph-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon class="title-icon"><Share /></el-icon>
        知识图谱管理
      </h1>
      <p class="page-description">管理Neo4j知识图谱中的政策节点和关系</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6" v-for="stat in graphStats" :key="stat.label">
        <el-card class="stat-card hover-lift">
          <div class="stat-content">
            <div class="stat-icon" :class="stat.iconClass">
              <el-icon size="24">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stat.count }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-select v-model="selectedCity" placeholder="选择城市" @change="handleCityChange" clearable>
            <el-option
              v-for="city in cities"
              :key="city.name"
              :label="`${city.name} (${city.region})`"
              :value="city.name"
            />
          </el-select>
          <el-button type="primary" :icon="Plus" @click="showCreateDialog">
            创建政策
          </el-button>
          <el-button :icon="Refresh" @click="refreshData">刷新</el-button>
        </div>
        <div class="toolbar-right">
          <el-button :icon="View" @click="showGraphVisualization">
            图谱可视化
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 政策列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">政策列表</span>
          <span class="card-subtitle" v-if="selectedCity">{{ selectedCity }} 的政策</span>
        </div>
      </template>

      <el-table
        :data="policies"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="120" />
        <el-table-column prop="name" label="政策名称" min-width="150" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="water_saving_factor" label="节水因子" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.water_saving_factor" type="success">
              {{ (row.water_saving_factor * 100).toFixed(1) }}%
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="start_date" label="开始日期" width="120" />
        <el-table-column prop="end_date" label="结束日期" width="120" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" :icon="Edit" @click="editPolicy(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" :icon="Delete" @click="deletePolicy(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑政策对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEditing ? '编辑政策' : '创建政策'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="policyForm"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="政策名称" prop="name">
          <el-input v-model="policyForm.name" placeholder="请输入政策名称" />
        </el-form-item>
        <el-form-item label="政策描述" prop="description">
          <el-input
            v-model="policyForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入政策描述"
          />
        </el-form-item>
        <el-form-item label="节水因子" prop="water_saving_factor">
          <el-input-number
            v-model="policyForm.water_saving_factor"
            :min="0"
            :max="1"
            :step="0.1"
            :precision="2"
            placeholder="0.0 - 1.0"
          />
        </el-form-item>
        <el-form-item label="实施城市" prop="cityName" v-if="!isEditing">
          <el-select v-model="policyForm.cityName" placeholder="选择城市">
            <el-option
              v-for="city in cities"
              :key="city.name"
              :label="`${city.name} (${city.region})`"
              :value="city.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="开始日期" prop="start_date">
          <el-date-picker
            v-model="policyForm.start_date"
            type="date"
            placeholder="选择开始日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="结束日期" prop="end_date">
          <el-date-picker
            v-model="policyForm.end_date"
            type="date"
            placeholder="选择结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            {{ isEditing ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 图谱可视化对话框 -->
    <el-dialog
      v-model="graphDialogVisible"
      title="知识图谱可视化"
      width="80%"
      top="5vh"
    >
      <div class="graph-container">
        <div id="graph-visualization" style="width: 100%; height: 500px; border: 1px solid #ddd;">
          <!-- 这里将集成图谱可视化组件 -->
          <div class="graph-placeholder">
            <el-icon size="48"><Share /></el-icon>
            <p>图谱可视化功能开发中...</p>
            <p>将展示Neo4j中的政策节点和城市节点关系</p>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Edit,
  Delete,
  Refresh,
  View,
  Share,
  Document,
  Location,
  Connection,
  DataBoard
} from '@element-plus/icons-vue'
import { knowledgeGraphApi, type Policy, type City, type PolicyCreateRequest, type PolicyUpdateRequest } from '@/api/knowledge'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const graphDialogVisible = ref(false)
const isEditing = ref(false)

// 数据
const policies = ref<Policy[]>([])
const cities = ref<City[]>([])
const selectedCity = ref<string>('')
const selectedRows = ref<Policy[]>([])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 统计数据
const graphStats = ref([
  { label: '政策节点', count: 0, icon: 'Document', iconClass: 'policy-icon' },
  { label: '城市节点', count: 0, icon: 'Location', iconClass: 'city-icon' },
  { label: '关系连接', count: 0, icon: 'Connection', iconClass: 'relation-icon' },
  { label: '图谱深度', count: 0, icon: 'DataBoard', iconClass: 'depth-icon' }
])

// 表单数据
const policyForm = reactive<PolicyCreateRequest & { id?: string }>({
  name: '',
  description: '',
  water_saving_factor: 0,
  start_date: '',
  end_date: '',
  cityName: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入政策名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入政策描述', trigger: 'blur' }
  ],
  cityName: [
    { required: true, message: '请选择实施城市', trigger: 'change' }
  ]
}

const formRef = ref()

// 页面加载时初始化数据
onMounted(() => {
  loadCities()
  loadGraphStats()
})

// 加载城市列表
const loadCities = async () => {
  try {
    cities.value = await knowledgeGraphApi.getCities()
  } catch (error) {
    console.error('加载城市列表失败:', error)
    ElMessage.error('加载城市列表失败')
  }
}

// 加载图谱统计数据
const loadGraphStats = async () => {
  try {
    // 这里可以调用后端API获取真实的统计数据
    // 暂时使用模拟数据
    graphStats.value[0].count = policies.value.length
    graphStats.value[1].count = cities.value.length
    graphStats.value[2].count = policies.value.length // 假设每个政策都有一个城市关系
    graphStats.value[3].count = 2 // 假设图谱深度为2
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 处理城市选择变化
const handleCityChange = (cityName: string) => {
  if (cityName) {
    loadPoliciesByCity(cityName)
  } else {
    policies.value = []
    total.value = 0
  }
}

// 加载指定城市的政策
const loadPoliciesByCity = async (cityName: string) => {
  loading.value = true
  try {
    const data = await knowledgeGraphApi.getPoliciesByCity(cityName)
    policies.value = data
    total.value = data.length
    loadGraphStats()
  } catch (error) {
    console.error('加载政策列表失败:', error)
    ElMessage.error('加载政策列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  if (selectedCity.value) {
    loadPoliciesByCity(selectedCity.value)
  }
  loadCities()
}

// 显示创建对话框
const showCreateDialog = () => {
  isEditing.value = false
  dialogVisible.value = true
}

// 编辑政策
const editPolicy = (policy: Policy) => {
  isEditing.value = true
  Object.assign(policyForm, policy)
  dialogVisible.value = true
}

// 删除政策
const deletePolicy = async (policy: Policy) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除政策 "${policy.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await knowledgeGraphApi.deletePolicy(policy.id)
    ElMessage.success('删除成功')
    
    if (selectedCity.value) {
      loadPoliciesByCity(selectedCity.value)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除政策失败:', error)
      ElMessage.error('删除政策失败')
    }
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    if (isEditing.value) {
      const updateData: PolicyUpdateRequest = {
        id: policyForm.id!,
        name: policyForm.name,
        description: policyForm.description,
        water_saving_factor: policyForm.water_saving_factor,
        start_date: policyForm.start_date,
        end_date: policyForm.end_date
      }
      await knowledgeGraphApi.updatePolicy(updateData)
      ElMessage.success('更新成功')
    } else {
      await knowledgeGraphApi.createPolicy(policyForm)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    
    if (selectedCity.value) {
      loadPoliciesByCity(selectedCity.value)
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(policyForm, {
    name: '',
    description: '',
    water_saving_factor: 0,
    start_date: '',
    end_date: '',
    cityName: ''
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 显示图谱可视化
const showGraphVisualization = () => {
  graphDialogVisible.value = true
}

// 处理表格选择变化
const handleSelectionChange = (selection: Policy[]) => {
  selectedRows.value = selection
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  // 这里可以重新加载数据
}

// 处理当前页变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  // 这里可以重新加载数据
}
</script>

<style scoped>
.knowledge-graph-container {
  padding: 24px;
  background: var(--bg-secondary);
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.title-icon {
  color: var(--primary-color);
}

.page-description {
  color: var(--text-secondary);
  margin: 0;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.policy-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.city-icon { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.relation-icon { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.depth-icon { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-top: 4px;
}

.toolbar-card {
  margin-bottom: 24px;
  border: none;
  box-shadow: var(--shadow-sm);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-card {
  border: none;
  box-shadow: var(--shadow-sm);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.card-title {
  font-weight: 600;
  color: var(--text-primary);
}

.card-subtitle {
  color: var(--text-secondary);
  font-size: 14px;
}

.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.graph-container {
  padding: 16px;
}

.graph-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  text-align: center;
}

.graph-placeholder p {
  margin: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-graph-container {
    padding: 16px;
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-left {
    flex-wrap: wrap;
  }

  .stat-content {
    gap: 12px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
  }

  .stat-value {
    font-size: 20px;
  }
}
</style>
