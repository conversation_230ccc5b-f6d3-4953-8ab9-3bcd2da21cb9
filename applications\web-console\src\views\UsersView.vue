<template>
  <div class="users-container">
    <!-- 顶部搜索和操作栏 -->
    <div class="action-header">
      <el-card class="filter-card">
        <div class="filter-form">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item label="用户名">
              <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
            </el-form-item>
            <el-form-item label="角色">
              <el-select v-model="searchForm.role" placeholder="选择角色" clearable>
                <el-option label="管理员" value="admin" />
                <el-option label="普通用户" value="user" />
                <el-option label="访客" value="guest" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
                <el-option label="正常" value="active" />
                <el-option label="禁用" value="disabled" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
              <el-button :icon="Refresh" @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>
    
    <!-- 用户列表 -->
    <el-card class="list-card">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <el-icon class="header-icon"><User /></el-icon>
            <span>用户列表</span>
            <el-tag type="info" class="count-tag">{{ userData.length }} 人</el-tag>
          </div>
          <div class="header-actions">
            <el-button type="success" :icon="Upload" plain @click="handleImport">导入</el-button>
            <el-button type="info" :icon="Download" plain @click="handleExport">导出</el-button>
            <el-button type="primary" :icon="Plus" @click="handleAdd">添加用户</el-button>
          </div>
        </div>
      </template>
      
      <!-- 表格 -->
      <el-table 
        :data="userData" 
        border 
        stripe 
        style="width: 100%" 
        v-loading="loading"
        :header-cell-style="{ background: '#f5f7fa' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column type="index" label="#" width="60" />
        <el-table-column prop="avatar" label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar">{{ row.username.charAt(0).toUpperCase() }}</el-avatar>
          </template>
        </el-table-column>
        <el-table-column prop="username" label="用户名" sortable />
        <el-table-column prop="realName" label="真实姓名" />
        <el-table-column prop="role" label="角色">
          <template #default="{ row }">
            <el-tag 
              :type="row.role === 'admin' ? 'danger' : row.role === 'user' ? 'primary' : 'info'"
              effect="plain"
            >
              {{ row.role === 'admin' ? '管理员' : row.role === 'user' ? '普通用户' : '访客' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag 
              :type="row.status === 'active' ? 'success' : 'danger'" 
              effect="dark"
            >
              {{ row.status === 'active' ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" sortable width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              text 
              :icon="Edit" 
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button 
              type="primary" 
              size="small" 
              text 
              :icon="Key" 
              @click="handleResetPwd(row)"
            >
              重置密码
            </el-button>
            <el-button 
              :type="row.status === 'active' ? 'danger' : 'success'" 
              size="small" 
              text 
              :icon="row.status === 'active' ? Lock : Unlock"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
            <el-dropdown>
              <el-button size="small" text>
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleViewDetails(row)">
                    <el-icon><View /></el-icon>查看详情
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleAssignRoles(row)">
                    <el-icon><UserFilled /></el-icon>分配角色
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="handleDelete(row)" style="color: #F56C6C;">
                    <el-icon><Delete /></el-icon>删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 用户表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑用户' : '新增用户'"
      width="580px"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="100px"
      >
        <el-form-item label="用户头像">
          <el-avatar :size="80" :src="userForm.avatar">
            {{ userForm.username ? userForm.username.charAt(0).toUpperCase() : 'U' }}
          </el-avatar>
          <el-button text type="primary" style="margin-left: 16px" :icon="UploadFilled">更换头像</el-button>
        </el-form-item>
        
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>
        
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="userForm.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option label="管理员" value="admin" />
            <el-option label="普通用户" value="user" />
            <el-option label="访客" value="guest" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱地址" />
        </el-form-item>
        
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入手机号码" />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-radio-group v-model="userForm.status">
            <el-radio label="active">正常</el-radio>
            <el-radio label="disabled">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input v-model="userForm.remark" type="textarea" :rows="2" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Search, 
  Refresh, 
  Edit, 
  Delete, 
  Download, 
  Upload,
  Key,
  View,
  Lock,
  Unlock,
  ArrowDown,
  User,
  UserFilled,
  UploadFilled
} from '@element-plus/icons-vue'

// 表格加载状态
const loading = ref(false)

// 搜索表单
const searchForm = reactive({
  username: '',
  role: '',
  status: ''
})

// 表格数据
const userData = ref([
  {
    id: 1,
    username: 'admin',
    realName: '系统管理员',
    role: 'admin',
    email: '<EMAIL>',
    phone: '13800138000',
    status: 'active',
    createTime: '2024-01-01 09:00:00',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
    remark: '系统超级管理员'
  },
  {
    id: 2,
    username: 'user001',
    realName: '张三',
    role: 'user',
    email: '<EMAIL>',
    phone: '13800138001',
    status: 'active',
    createTime: '2024-01-05 10:15:00',
    avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
    remark: '研发部员工'
  },
  {
    id: 3,
    username: 'user002',
    realName: '李四',
    role: 'user',
    email: '<EMAIL>',
    phone: '13800138002',
    status: 'active',
    createTime: '2024-01-10 14:30:00',
    avatar: 'https://randomuser.me/api/portraits/women/3.jpg',
    remark: '市场部员工'
  },
  {
    id: 4,
    username: 'guest001',
    realName: '王五',
    role: 'guest',
    email: '<EMAIL>',
    phone: '13800138003',
    status: 'disabled',
    createTime: '2024-01-15 16:45:00',
    avatar: 'https://randomuser.me/api/portraits/men/4.jpg',
    remark: '临时访客'
  }
])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(4)

// 选中的行
const selectedRows = ref([])

// 对话框相关
const dialogVisible = ref(false)
const isEdit = ref(false)

// 表单引用
const userFormRef = ref()

// 用户表单
const userForm = reactive({
  id: null,
  username: '',
  realName: '',
  role: '',
  email: '',
  phone: '',
  status: 'active',
  avatar: '',
  remark: ''
})

// 表单验证规则
const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择用户角色', trigger: 'change' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 页面加载时
onMounted(() => {
  fetchUserData()
})

// 模拟获取用户数据
const fetchUserData = () => {
  loading.value = true
  // 模拟API请求延迟
  setTimeout(() => {
    // 实际项目中这里会是API调用
    loading.value = false
  }, 500)
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchUserData()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

// 表格选择行变化
const handleSelectionChange = (rows) => {
  selectedRows.value = rows
}

// 分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchUserData()
}

// 页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchUserData()
}

// 添加用户
const handleAdd = () => {
  isEdit.value = false
  Object.keys(userForm).forEach(key => {
    userForm[key] = key === 'status' ? 'active' : ''
  })
  userForm.id = null
  dialogVisible.value = true
}

// 编辑用户
const handleEdit = (row) => {
  isEdit.value = true
  // 复制行数据到表单
  Object.keys(userForm).forEach(key => {
    userForm[key] = row[key]
  })
  dialogVisible.value = true
}

// 查看详情
const handleViewDetails = (row) => {
  ElMessage.info(`查看用户详情：${row.username}`)
}

// 分配角色
const handleAssignRoles = (row) => {
  ElMessage.info(`分配角色给：${row.username}`)
}

// 重置密码
const handleResetPwd = (row) => {
  ElMessageBox.confirm(`确认重置 ${row.username} 的密码吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('密码已重置')
  }).catch(() => {})
}

// 切换状态
const handleToggleStatus = (row) => {
  const newStatus = row.status === 'active' ? 'disabled' : 'active'
  const actionText = row.status === 'active' ? '禁用' : '启用'
  
  ElMessageBox.confirm(`确认${actionText}用户：${row.username}？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    row.status = newStatus
    ElMessage.success(`已${actionText}用户：${row.username}`)
  }).catch(() => {})
}

// 删除用户
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除用户：${row.username}？此操作不可恢复！`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'danger'
  }).then(() => {
    const index = userData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      userData.value.splice(index, 1)
      total.value--
      ElMessage.success('删除成功')
    }
  }).catch(() => {})
}

// 导入用户
const handleImport = () => {
  ElMessage.info('导入用户功能开发中...')
}

// 导出用户
const handleExport = () => {
  ElMessage.info('导出用户功能开发中...')
}

// 提交表单
const submitForm = () => {
  userFormRef.value.validate((valid) => {
    if (!valid) return
    
    if (isEdit.value) {
      // 编辑现有用户
      const index = userData.value.findIndex(item => item.id === userForm.id)
      if (index !== -1) {
        userData.value[index] = { ...userForm }
        ElMessage.success('用户信息已更新')
      }
    } else {
      // 添加新用户
      const newUser = { ...userForm }
      newUser.id = userData.value.length + 1
      newUser.createTime = new Date().toLocaleString()
      userData.value.push(newUser)
      total.value++
      ElMessage.success('用户添加成功')
    }
    
    dialogVisible.value = false
  })
}
</script>

<style scoped>
.users-container {
  width: 100%;
}

.action-header {
  margin-bottom: 16px;
}

.filter-card {
  margin-bottom: 16px;
  background-color: #fff;
}

.list-card {
  margin-bottom: 16px;
  background-color: #fff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.header-icon {
  margin-right: 8px;
}

.count-tag {
  margin-left: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .search-form {
    flex-wrap: wrap;
  }
  
  .el-form--inline .el-form-item {
    margin-right: 10px;
  }
  
  .header-actions {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .header-actions {
    width: 100%;
  }
  
  .header-actions .el-button {
    flex: 1;
  }
}
</style>
