import api from './auth'

const API_BASE_URL = import.meta.env.VITE_API_URL || '/api'

// Pod 接口定义
export interface PodContainer {
  name: string
  ready: boolean
  restart_count: number
  image: string
  state: string
  started_at?: string
  reason?: string
  message?: string
  exit_code?: number
}

export interface Pod {
  name: string
  namespace: string
  status: string
  reason?: string
  pod_ip?: string
  host_ip?: string
  node_name?: string
  restart_count: number
  age: string
  creation_timestamp?: string
  containers: PodContainer[]
  labels?: Record<string, string>
  annotations?: Record<string, string>
}

// 扩缩容请求接口
export interface ScaleRequest {
  replicas: number
}

// API 响应接口
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
}

// Admin API 类
export class AdminAPI {
  /**
   * 获取所有 Pod 列表
   */
  static async getPods(namespace?: string): Promise<Pod[]> {
    try {
      const params = namespace ? { namespace } : {}
      const response = await api.get(`${API_BASE_URL}/admin/pods`, { params })
      return response.data
    } catch (error) {
      console.error('Failed to fetch pods:', error)
      throw error
    }
  }

  /**
   * 获取 Pod 日志（流式）
   * 返回一个 ReadableStream 用于实时读取日志
   */
  static async getPodLogs(
    namespace: string,
    podName: string,
    options: {
      container?: string
      tailLines?: number
      follow?: boolean
    } = {}
  ): Promise<ReadableStream<Uint8Array>> {
    try {
      const params = new URLSearchParams()
      if (options.container) params.append('container', options.container)
      if (options.tailLines) params.append('tail_lines', options.tailLines.toString())
      if (options.follow) params.append('follow', 'true')

      const url = `${API_BASE_URL}/admin/pods/${namespace}/${podName}/logs?${params.toString()}`

      // 获取认证 token
      const token = localStorage.getItem('token')
      const headers: Record<string, string> = {
        'Accept': 'text/plain',
      }

      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch(url, {
        method: 'GET',
        headers,
      })

      if (!response.ok) {
        if (response.status === 401) {
          // 认证失败，清除 token 并跳转登录
          localStorage.removeItem('token')
          localStorage.removeItem('username')
          window.location.href = '/login'
          throw new Error('Authentication failed')
        }
        throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`)
      }

      if (!response.body) {
        throw new Error('Response body is null')
      }

      return response.body
    } catch (error) {
      console.error('Failed to fetch pod logs:', error)
      throw error
    }
  }

  /**
   * 重启 Deployment
   */
  static async restartDeployment(namespace: string, deploymentName: string): Promise<ApiResponse> {
    try {
      const response = await api.post(
        `${API_BASE_URL}/admin/deployments/${namespace}/${deploymentName}/restart`
      )
      return response.data
    } catch (error) {
      console.error('Failed to restart deployment:', error)
      throw error
    }
  }

  /**
   * 扩缩容 Deployment
   */
  static async scaleDeployment(
    namespace: string,
    deploymentName: string,
    replicas: number
  ): Promise<ApiResponse> {
    try {
      const response = await api.post(
        `${API_BASE_URL}/admin/deployments/${namespace}/${deploymentName}/scale`,
        { replicas }
      )
      return response.data
    } catch (error) {
      console.error('Failed to scale deployment:', error)
      throw error
    }
  }

  /**
   * 创建日志流读取器
   * 这是一个辅助方法，用于更方便地处理流式日志
   */
  static createLogStreamReader(
    stream: ReadableStream<Uint8Array>,
    onData: (chunk: string) => void,
    onError?: (error: Error) => void,
    onEnd?: () => void
  ): { reader: ReadableStreamDefaultReader<Uint8Array>; cancel: () => void } {
    const reader = stream.getReader()
    const decoder = new TextDecoder('utf-8', { fatal: false })
    let cancelled = false

    const readChunk = async () => {
      try {
        while (!cancelled) {
          const { done, value } = await reader.read()

          if (done) {
            console.log('Log stream ended')
            onEnd?.()
            break
          }

          if (value && value.length > 0) {
            try {
              const chunk = decoder.decode(value, { stream: true })
              if (chunk.trim()) {
                onData(chunk)
              }
            } catch (decodeError) {
              console.warn('Failed to decode chunk:', decodeError)
              // 尝试使用更宽松的解码
              const fallbackChunk = new TextDecoder('utf-8', { fatal: false }).decode(value)
              onData(fallbackChunk)
            }
          }
        }
      } catch (error) {
        console.error('Log stream read error:', error)
        if (!cancelled) {
          onError?.(error as Error)
        }
      } finally {
        try {
          reader.releaseLock()
        } catch (e) {
          // 忽略释放锁的错误
        }
      }
    }

    readChunk()

    return {
      reader,
      cancel: () => {
        cancelled = true
        try {
          reader.cancel('User cancelled')
        } catch (e) {
          console.warn('Failed to cancel reader:', e)
        }
      }
    }
  }

  /**
   * 获取 Pod 日志（非流式，用于调试）
   */
  static async getPodLogsSimple(
    namespace: string,
    podName: string,
    options: {
      container?: string
      tailLines?: number
    } = {}
  ): Promise<string> {
    try {
      const params = new URLSearchParams()
      if (options.container) params.append('container', options.container)
      if (options.tailLines) params.append('tail_lines', options.tailLines.toString())
      params.append('follow', 'false') // 非流式

      const response = await api.get(`${API_BASE_URL}/admin/pods/${namespace}/${podName}/logs?${params.toString()}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch pod logs (simple):', error)
      throw error
    }
  }
}

export default AdminAPI
