# 测试修复后的 Agent Executor Service
param(
    [string]$ServiceUrl = "http://127.0.0.1:58345"
)

Write-Host "🔧 测试修复后的 Agent Executor Service"
Write-Host "服务地址: $ServiceUrl"
Write-Host "=" * 60

# 1. 检查工具定义
Write-Host "`n1. 🔍 检查工具定义"
try {
    $tools = curl.exe "$ServiceUrl/tools-schema" --silent | ConvertFrom-Json
    Write-Host "✅ 工具数量: $($tools.tools.Count)"
    foreach ($tool in $tools.tools) {
        Write-Host "   - $($tool.function.name): $($tool.function.description)"
    }
} catch {
    Write-Host "❌ 获取工具定义失败: $_"
}

# 2. 测试简单对话（无工具调用）
Write-Host "`n2. 💬 测试简单对话"
$simpleBody = '{"command": "你好，请简单介绍一下你的功能"}'

try {
    Write-Host "发送简单对话..."
    curl.exe -N -X POST "$ServiceUrl/execute-command" `
        -H "Content-Type: application/json" `
        -d $simpleBody
    Write-Host "`n✅ 简单对话测试完成"
} catch {
    Write-Host "❌ 简单对话失败: $_"
}

# 3. 测试完整的模拟流程（带等待）
Write-Host "`n3. 🚀 测试完整模拟流程（带等待机制）"
$fullBody = '{"command": "请为测试城市创建一个水资源模拟，人口50000，降雨量100mm，温度25度。创建后等待完成，然后提供详细的分析报告。"}'

try {
    Write-Host "发送完整模拟流程命令..."
    Write-Host "预期流程: 创建任务 -> 等待完成 -> 获取报告"
    curl.exe -N -X POST "$ServiceUrl/execute-command" `
        -H "Content-Type: application/json" `
        -d $fullBody
    Write-Host "`n✅ 完整模拟流程测试完成"
} catch {
    Write-Host "❌ 完整模拟流程失败: $_"
}

# 4. 测试 Rivertown 场景（原始失败的命令）
Write-Host "`n4. 🏙️ 测试 Rivertown 场景"
$rivertownBody = '{"command": "请对人口为 50000、降雨量为 110、温度为 28.5 的 Rivertown 运行模拟。完成后，告诉我最终的水位并提供简要分析。"}'

try {
    Write-Host "发送 Rivertown 模拟命令..."
    Write-Host "这是之前失败的命令，现在应该能正常工作"
    curl.exe -N -X POST "$ServiceUrl/execute-command" `
        -H "Content-Type: application/json" `
        -d $rivertownBody
    Write-Host "`n✅ Rivertown 场景测试完成"
} catch {
    Write-Host "❌ Rivertown 场景失败: $_"
}

# 5. 测试等待工具（如果有现有任务）
Write-Host "`n5. ⏳ 测试等待工具"
$waitBody = '{"command": "请检查任务ID 1 的状态，如果还在运行请等待完成"}'

try {
    Write-Host "测试等待现有任务..."
    curl.exe -N -X POST "$ServiceUrl/execute-command" `
        -H "Content-Type: application/json" `
        -d $waitBody
    Write-Host "`n✅ 等待工具测试完成"
} catch {
    Write-Host "❌ 等待工具测试失败: $_"
}

Write-Host "`n" + "=" * 60
Write-Host "🎉 所有测试完成！"
Write-Host ""
Write-Host "📋 修复总结:"
Write-Host "1. ✅ 添加了 wait_for_task_completion 工具"
Write-Host "2. ✅ 修复了 JSON 序列化错误"
Write-Host "3. ✅ 改进了系统提示，指导正确的工作流程"
Write-Host "4. ✅ 增强了流式响应效果"
Write-Host "5. ✅ 添加了详细的调试日志"
Write-Host ""
Write-Host "💡 如果仍有问题，请查看服务日志:"
Write-Host "kubectl logs -l app=agent-executor-service -f"
