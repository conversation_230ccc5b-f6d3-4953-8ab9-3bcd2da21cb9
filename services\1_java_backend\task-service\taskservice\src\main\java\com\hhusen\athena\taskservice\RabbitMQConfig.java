package com.hhusen.athena.taskservice;

import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;

@Configuration
public class RabbitMQConfig {

    @Bean
    public Queue simulationTasksQueue() {
        return new Queue("simulation_tasks", true);
    }
    
    @Bean
    public Queue simulationResultsQueue() {
        return new Queue("simulation_results", true);
    }

    @Bean
    public Queue agentActionsQueue() {
        return new Queue("agent_actions", true);
    }
    
    @Bean
    public Queue environmentUpdatesQueue() {
        return new Queue("environment_updates", true);
    }

    // 这个 Bean 会告诉 Spring AMQP 使用 JSON 作为消息格式
    @Bean
    public MessageConverter jsonMessageConverter() {
        return new Jackson2JsonMessageConverter();
    }
} 