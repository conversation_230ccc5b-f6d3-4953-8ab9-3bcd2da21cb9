# 前端适配指南

本文档说明如何配置和运行独立的 Vue 前端项目来访问 Kubernetes 集群管理功能。

## 🎯 **架构说明**

```
Vue 前端 (localhost:5173)
    ↓ (代理)
API Gateway (localhost:8080)
    ↓
Admin Service (K8s 集群内)
    ↓
Kubernetes API
```

## 🔧 **已完成的适配**

### 1. **代理配置** ✅
- 更新了 `vite.config.ts` 中的代理地址为 `localhost:8080`
- 配置了 `/api` 和 `/auth` 路径的代理转发

### 2. **API 集成** ✅
- 创建了 `src/api/admin.ts` 文件，包含所有 admin-service API 调用
- 集成了认证系统，自动在请求中添加 JWT Token
- 支持流式日志查看功能

### 3. **页面组件** ✅
- 创建了 `ClusterStatusView.vue` 集群状态监控页面
- 添加了路由配置 `/admin/cluster-status`
- 更新了侧边栏菜单，添加"集群管理"入口

### 4. **认证处理** ✅
- 使用统一的 axios 实例处理认证
- 自动添加 Authorization 头
- 401 错误自动跳转登录页面

## 🚀 **启动步骤**

### 1. 确保后端服务运行

```bash
# 检查 Kubernetes 服务状态
kubectl get pods -l app=admin-service
kubectl get pods -l app=api-gateway

# 端口转发 API Gateway
kubectl port-forward svc/api-gateway-svc 8080:8080
```

### 2. 启动前端开发服务器

```bash
# 进入前端目录
cd applications/web-console

# 使用启动脚本（推荐）
chmod +x start-dev.sh
./start-dev.sh

# 或手动启动
npm install
npm run dev
```

### 3. 访问应用

- **前端地址**: http://localhost:5173
- **默认账户**: admin / admin123

## 📋 **功能验证清单**

### 登录功能 ✅
- [ ] 能够正常访问登录页面
- [ ] 使用 admin/admin123 成功登录
- [ ] 登录后跳转到首页

### 集群管理功能 ✅
- [ ] 侧边栏显示"集群管理"菜单
- [ ] 点击"集群状态"进入监控页面
- [ ] 页面显示 Pod 统计卡片
- [ ] 表格正确显示所有 Pod 信息

### Pod 操作功能 ✅
- [ ] 点击"查看日志"弹出模态框
- [ ] 日志内容实时滚动显示
- [ ] 多容器 Pod 可以切换容器
- [ ] 可以下载日志文件

### Deployment 操作功能 ✅
- [ ] 点击"重启"按钮成功重启服务
- [ ] 点击"扩缩容"可以调整副本数
- [ ] 操作后 Pod 状态正确更新

## 🔍 **故障排除**

### 1. 代理连接失败

**错误**: `ECONNREFUSED 127.0.0.1:8080`

**解决方案**:
```bash
# 确保 API Gateway 端口转发正在运行
kubectl port-forward svc/api-gateway-svc 8080:8080

# 检查服务状态
kubectl get svc api-gateway-svc
```

### 2. 认证失败 (401)

**错误**: `401 Unauthorized`

**解决方案**:
- 确保已正确登录
- 检查 localStorage 中是否有 token
- 重新登录获取新的 token

### 3. API 路由不存在 (404)

**错误**: `404 Not Found` for `/api/admin/*`

**解决方案**:
```bash
# 检查 API Gateway 配置
kubectl logs -l app=api-gateway --tail=20

# 重启 API Gateway
kubectl rollout restart deployment/api-gateway
```

### 4. 前端编译错误

**错误**: TypeScript 或 Vue 编译错误

**解决方案**:
```bash
# 清理并重新安装依赖
rm -rf node_modules package-lock.json
npm install

# 检查 TypeScript 配置
npm run type-check
```

## 📁 **文件结构**

```
applications/web-console/
├── src/
│   ├── api/
│   │   ├── admin.ts          # Admin Service API 调用
│   │   └── auth.ts           # 认证相关 API
│   ├── views/
│   │   └── ClusterStatusView.vue  # 集群状态页面
│   ├── router/
│   │   └── index.ts          # 路由配置
│   └── components/
│       └── Layout/
│           └── MainLayout.vue     # 主布局（包含菜单）
├── vite.config.ts            # Vite 配置（代理设置）
├── .env.development          # 开发环境变量
├── start-dev.sh             # 启动脚本
└── package.json             # 项目依赖
```

## 🔄 **开发工作流**

1. **启动后端服务**:
   ```bash
   kubectl port-forward svc/api-gateway-svc 8080:8080
   ```

2. **启动前端服务**:
   ```bash
   ./start-dev.sh
   ```

3. **开发和测试**:
   - 修改代码后自动热重载
   - 使用浏览器开发者工具调试
   - 检查网络请求和响应

4. **构建生产版本**:
   ```bash
   npm run build
   ```

## 🎉 **完成**

现在你的 Vue 前端项目已经完全适配了 Kubernetes 集群管理功能！

- ✅ **认证系统**: 自动处理 JWT Token
- ✅ **API 代理**: 正确转发到后端服务
- ✅ **集群监控**: 实时查看 Pod 状态
- ✅ **日志查看**: 流式日志显示
- ✅ **服务管理**: 重启和扩缩容功能

享受你的新集群管理界面吧！🚀
