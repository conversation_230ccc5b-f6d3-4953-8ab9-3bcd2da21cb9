package com.hhusen.athena.knowledgeservice.repository;

import com.hhusen.athena.knowledgeservice.model.Policy;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PolicyRepository extends Neo4jRepository<Policy, String> {
    
    @Query("MATCH (p:Policy)-[r:IMPLEMENTED_IN]->(c:City {name: $cityName}) RETURN p, r, c")
    List<Policy> findPoliciesByCity(@Param("cityName") String cityName);
} 