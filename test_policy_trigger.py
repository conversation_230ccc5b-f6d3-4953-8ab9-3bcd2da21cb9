import requests
import json
import time

# 配置
TASK_SERVICE_URL = "http://localhost:8080/tasks"
MLFLOW_TRACKING_URI = "http://localhost:5000"

def create_test_task(city_name, population, rainfall, temperature, name="Policy Test", initial_water_level=None):
    """
    创建一个测试任务
    
    参数:
        city_name (str): 城市名称
        population (int): 人口数量
        rainfall (float): 降雨量
        temperature (float): 温度
        name (str): 任务名称
        initial_water_level (float): 初始水位（米），用于触发政策
    """
    print(f"\n=== 创建测试任务 ===")
    print(f"城市: {city_name}")
    print(f"人口: {population}")
    print(f"降雨量: {rainfall}")
    print(f"温度: {temperature}")
    print(f"初始水位: {initial_water_level or '默认(5.0m)'}")
    
    task_data = {
        "name": name,
        "population": population,
        "rainfall": rainfall,
        "temperature": temperature,
        "cityName": city_name,
        "status": "PENDING"
    }
    
    # 如果提供了初始水位，添加到任务数据中
    if initial_water_level is not None:
        task_data["initialWaterLevel"] = initial_water_level
    
    try:
        response = requests.post(f"{TASK_SERVICE_URL}", json=task_data, timeout=10)
        response.raise_for_status()
        
        task = response.json()
        print(f"✅ 任务创建成功！")
        print(f"任务ID: {task['id']}")
        print(f"MLflow Run ID: {task['mlflowRunId']}")
        
        return task
        
    except Exception as e:
        print(f"❌ 创建任务失败: {e}")
        return None

def check_task_status(task_id, max_wait=60):
    """
    检查任务状态，等待任务完成
    
    参数:
        task_id (int): 任务ID
        max_wait (int): 最大等待时间（秒）
    """
    print(f"\n=== 检查任务状态 ===")
    print(f"任务ID: {task_id}")
    
    start_time = time.time()
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(f"{TASK_SERVICE_URL}/{task_id}", timeout=5)
            response.raise_for_status()
            
            task = response.json()
            status = task.get('status')
            water_level = task.get('waterLevel')
            
            print(f"状态: {status}, 水位: {water_level}")
            
            if status == "COMPLETED":
                print(f"✅ 任务完成！")
                return task
                
            time.sleep(2)
            
        except Exception as e:
            print(f"检查状态失败: {e}")
            time.sleep(2)
    
    print(f"❌ 等待超时")
    return None

def analyze_mlflow_data(run_id):
    """
    分析MLflow数据，检查政策触发情况
    
    参数:
        run_id (str): MLflow运行ID
    """
    print(f"\n=== 分析MLflow数据 ===")
    print(f"Run ID: {run_id}")
    
    try:
        import mlflow
        from mlflow.tracking import MlflowClient
        
        mlflow.set_tracking_uri(MLFLOW_TRACKING_URI)
        client = MlflowClient()
        
        run = client.get_run(run_id)
        
        print(f"运行状态: {run.info.status}")
        
        # 检查政策相关参数
        params = run.data.params
        metrics = run.data.metrics
        
        print("\n--- 政策状态 ---")
        policy_triggered = params.get('policy_triggered', 'unknown')
        applied_policy_name = params.get('applied_policy_name', 'unknown')
        trigger_condition_met = params.get('trigger_condition_met', 'unknown')
        
        print(f"政策是否触发: {policy_triggered}")
        print(f"应用的政策名称: {applied_policy_name}")
        print(f"触发条件是否满足: {trigger_condition_met}")
        
        print("\n--- 关键指标 ---")
        final_adaptation_factor = metrics.get('final_adaptation_factor', 'unknown')
        print(f"最终适应因子: {final_adaptation_factor}")
        
        # 检查是否有节水效果
        if final_adaptation_factor != 'unknown':
            if float(final_adaptation_factor) < 1.0:
                print("✅ 检测到节水效果！")
            else:
                print("ℹ️ 未检测到节水效果")
        
        return {
            'policy_triggered': policy_triggered,
            'applied_policy_name': applied_policy_name,
            'trigger_condition_met': trigger_condition_met,
            'final_adaptation_factor': final_adaptation_factor
        }
        
    except Exception as e:
        print(f"❌ 分析MLflow数据失败: {e}")
        return None

def test_policy_scenarios():
    """
    测试不同的政策触发场景
    """
    print("=== 政策触发测试 ===")
    
    # 场景1: Rivertown城市，正常水位（应该不触发政策）
    print("\n🔍 场景1: Rivertown城市，正常水位")
    task1 = create_test_task("Rivertown", 50000, 2.5, 22.0, "Rivertown-Normal", initial_water_level=5.0)
    if task1:
        completed_task1 = check_task_status(task1['id'])
        if completed_task1:
            analyze_mlflow_data(task1['mlflowRunId'])
    
    time.sleep(5)  # 等待一段时间
    
    # 场景2: Rivertown城市，低水位（应该触发DroughtAlert政策）
    print("\n🔍 场景2: Rivertown城市，低水位")
    task2 = create_test_task("Rivertown", 50000, 0.5, 25.0, "Rivertown-Drought", initial_water_level=3.8)
    if task2:
        completed_task2 = check_task_status(task2['id'])
        if completed_task2:
            analyze_mlflow_data(task2['mlflowRunId'])
    
    time.sleep(5)  # 等待一段时间
    
    # 场景3: 其他城市（应该不触发政策，因为没有为该城市定义政策）
    print("\n🔍 场景3: 其他城市")
    task3 = create_test_task("OtherCity", 50000, 2.5, 22.0, "OtherCity-Test", initial_water_level=3.8)
    if task3:
        completed_task3 = check_task_status(task3['id'])
        if completed_task3:
            analyze_mlflow_data(task3['mlflowRunId'])

if __name__ == "__main__":
    print("=== 政策触发功能测试脚本 ===")
    print("\n请确保以下端口转发正在运行：")
    print("1. kubectl port-forward svc/task-service-svc 8080:80")
    print("2. kubectl port-forward svc/mlflow-svc 5000:5000")
    
    test_policy_scenarios()
    
    print("\n=== 测试完成 ===") 