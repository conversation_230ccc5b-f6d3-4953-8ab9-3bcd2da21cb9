apiVersion: apps/v1
kind: Deployment
metadata:
  name: pinn-engine-deployment
  labels:
    app: pinn-engine
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pinn-engine
  template:
    metadata:
      labels:
        app: pinn-engine
    spec:
      containers:
      - name: pinn-engine
        image: athena/pinn-engine:0.0.16
        imagePullPolicy: IfNotPresent
        env:
        - name: RABBITMQ_HOST
          value: rabbitmq-svc # RabbitMQ 服务名称
        - name: MLFLOW_TRACKING_URI
          value: http://mlflow-svc:5000 # MLflow 服务地址
        - name: MODEL_NAME
          value: "HydroPINN" # 要加载的模型名称
        - name: MODEL_VERSION
          value: "1" # 模型版本号，可以设置为"latest"
        - name: BASIN_AREA
          value: "5000000" # 流域面积，默认为5平方公里（5,000,000平方米）
        - name: DEFAULT_RAINFALL
          value: "2.5" # 默认日降雨量2.5毫米（月均75毫米）
        - name: DEFAULT_TEMPERATURE
          value: "22.0" # 默认温度22摄氏度
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: minio-secret
              key: accessKey
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: minio-secret
              key: secretKey
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m" 