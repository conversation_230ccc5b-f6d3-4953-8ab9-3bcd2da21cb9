import os
import time
import requests
import mlflow
from mlflow.tracking import MlflowClient

# --- 配置 ---
# MLflow 服务地址 (需要端口转发)
MLFLOW_TRACKING_URI = "http://localhost:5000"

# Task-Service 服务地址 (需要端口转发)
TASK_SERVICE_BASE_URL = "http://localhost:8080/tasks"

# 要查询的目标任务 ID
TASK_ID_TO_QUERY = 10

# 轮询配置
RETRY_INTERVAL_SECONDS = 3  # 每隔3秒重试一次
MAX_RETRIES = 10            # 最多重试10次

def get_run_id_with_retry(task_id: int):
    """
    通过轮询和重试机制，从 task-service 获取指定任务的 mlflowRunId。
    """
    print(f"--- 开始从 Task-Service 获取 Task ID: {task_id} 的 mlflowRunId ---")
    
    for attempt in range(MAX_RETRIES):
        print(f"\n第 {attempt + 1}/{MAX_RETRIES} 次尝试...")
        try:
            response = requests.get(f"{TASK_SERVICE_BASE_URL}/{task_id}", timeout=5)
            response.raise_for_status()
            
            task_data = response.json()
            print(f"  [成功] 收到来自 Task-Service 的响应: {task_data}")
            
            # 检查 mlflowRunId 是否存在且不为空
            mlflow_run_id = task_data.get("mlflowRunId")
            if mlflow_run_id and mlflow_run_id.strip():
                print(f"\n🎉 [成功] 已获取 mlflowRunId: {mlflow_run_id}")
                return mlflow_run_id
            else:
                print(f"  [等待] 响应中缺少或 mlflowRunId 为空。将在 {RETRY_INTERVAL_SECONDS} 秒后重试...")

        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 404:
                print(f"  [错误] Task-Service 返回 404 Not Found。任务ID {task_id} 可能不存在。")
                return None # 任务不存在，直接退出
            print(f"  [错误] Task-Service 返回HTTP错误: {e}。将在 {RETRY_INTERVAL_SECONDS} 秒后重试...")
        except requests.exceptions.RequestException as e:
            print(f"  [错误] 无法连接到 Task-Service: {e}。将在 {RETRY_INTERVAL_SECONDS} 秒后重试...")
        
        time.sleep(RETRY_INTERVAL_SECONDS)
        
    print(f"\n❌ [失败] 在 {MAX_RETRIES} 次尝试后，仍未获取到有效的 mlflowRunId。")
    return None

def fetch_mlflow_data(run_id: str):
    """
    使用给定的 run_id 从 MLflow 获取详细的运行数据。
    """
    print(f"\n--- 正在使用 Run ID: {run_id} 从 MLflow 获取数据 ---")
    try:
        mlflow.set_tracking_uri(MLFLOW_TRACKING_URI)
        client = MlflowClient()
        
        run = client.get_run(run_id)
        
        print(f"✅ [成功] 已从 MLflow 获取到数据!")
        print(f"  - 运行 ID: {run.info.run_id}")
        print(f"    状态: {run.info.status}")
        
        if run.data.params:
            print("    参数:")
            for key, value in run.data.params.items():
                print(f"      {key}: {value}")
        else:
            print("    参数: 无")

        if run.data.metrics:
            print("    指标:")
            for key, value in run.data.metrics.items():
                print(f"      {key}: {value}")
        else:
            print("    指标: 无")

    except Exception as e:
        print(f"❌ [错误] 从 MLflow 获取数据时出错: {e}")


if __name__ == "__main__":
    print("--- 模拟 LLM-Service 获取数据流程的测试脚本 ---")
    print("\n请确保您已在两个独立的终端中启动了端口转发:")
    print("1. kubectl port-forward svc/mlflow-svc 5000:5000")
    print("2. kubectl port-forward svc/task-service-svc 8080:8080")
    
    run_id = get_run_id_with_retry(TASK_ID_TO_QUERY)
    
    if run_id:
        fetch_mlflow_data(run_id)
    else:
        print("\n由于未能获取 mlflowRunId，无法继续从 MLflow 查询数据。")
        
    print("\n--- 脚本执行完毕 ---") 