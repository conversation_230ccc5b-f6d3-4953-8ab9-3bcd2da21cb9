package com.hhusen.athena.knowledgeservice.service;

import java.util.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.neo4j.core.Neo4jClient;
import org.springframework.stereotype.Service;

import com.hhusen.athena.knowledgeservice.model.Policy;
import com.hhusen.athena.knowledgeservice.repository.PolicyRepository;

@Service
public class PolicyService {

    private final PolicyRepository policyRepository;
    private final Neo4jClient neo4jClient;

    @Autowired
    public PolicyService(PolicyRepository policyRepository, Neo4jClient neo4jClient) {
        this.policyRepository = policyRepository;
        this.neo4jClient = neo4jClient;
    }

    public List<Policy> getPoliciesByCity(String cityName) {
        return policyRepository.findPoliciesByCity(cityName);
    }

    public Policy createPolicy(Map<String, Object> policyData) {
        String policyId = UUID.randomUUID().toString();
        String name = (String) policyData.get("name");
        String description = (String) policyData.get("description");
        Double waterSavingFactor = (Double) policyData.get("water_saving_factor");
        String startDate = (String) policyData.get("start_date");
        String endDate = (String) policyData.get("end_date");
        String cityName = (String) policyData.get("cityName");

        // 创建政策节点
        neo4jClient.query(
            "CREATE (p:Policy {id: $id, name: $name, description: $description, " +
            "water_saving_factor: $waterSavingFactor, " +
            "start_date: $startDate, end_date: $endDate}) " +
            "WITH p " +
            "MATCH (c:City {name: $cityName}) " +
            "CREATE (p)-[:IMPLEMENTED_IN]->(c) " +
            "RETURN p"
        )
        .bindAll(Map.of(
            "id", policyId,
            "name", name,
            "description", description,
            "waterSavingFactor", waterSavingFactor != null ? waterSavingFactor : 0.0,
            "startDate", startDate != null ? startDate : "",
            "endDate", endDate != null ? endDate : "",
            "cityName", cityName
        ))
        .run();

        // 返回创建的政策对象
        Policy policy = new Policy();
        policy.setId(policyId);
        policy.setName(name);
        policy.setDescription(description);
        policy.setWaterSavingFactor(waterSavingFactor);
        policy.setStartDate(startDate);
        policy.setEndDate(endDate);

        return policy;
    }

    public Policy updatePolicy(String policyId, Map<String, Object> policyData) {
        String name = (String) policyData.get("name");
        String description = (String) policyData.get("description");
        Double waterSavingFactor = (Double) policyData.get("water_saving_factor");
        String startDate = (String) policyData.get("start_date");
        String endDate = (String) policyData.get("end_date");

        // 更新政策节点
        neo4jClient.query(
            "MATCH (p:Policy {id: $id}) " +
            "SET p.name = $name, p.description = $description, " +
            "p.water_saving_factor = $waterSavingFactor, " +
            "p.start_date = $startDate, p.end_date = $endDate " +
            "RETURN p"
        )
        .bindAll(Map.of(
            "id", policyId,
            "name", name != null ? name : "",
            "description", description != null ? description : "",
            "waterSavingFactor", waterSavingFactor != null ? waterSavingFactor : 0.0,
            "startDate", startDate != null ? startDate : "",
            "endDate", endDate != null ? endDate : ""
        ))
        .run();

        // 返回更新的政策对象
        Policy policy = new Policy();
        policy.setId(policyId);
        policy.setName(name);
        policy.setDescription(description);
        policy.setWaterSavingFactor(waterSavingFactor);
        policy.setStartDate(startDate);
        policy.setEndDate(endDate);

        return policy;
    }

    public void deletePolicy(String policyId) {
        neo4jClient.query(
            "MATCH (p:Policy {id: $id}) " +
            "DETACH DELETE p"
        )
        .bind(policyId).to("id")
        .run();
    }

    public List<Map<String, Object>> getCities() {
        return neo4jClient.query(
            "MATCH (c:City) RETURN c.name as name, c.population as population, c.region as region"
        )
        .fetch()
        .all()
        .stream()
        .map(record -> Map.of(
            "name", record.get("name"),
            "population", record.get("population"),
            "region", record.get("region")
        ))
        .collect(java.util.stream.Collectors.toList());
    }

    public Map<String, Object> getGraphStructure() {
        // 获取节点统计
        var nodeStats = neo4jClient.query(
            "MATCH (n) RETURN labels(n) as labels, count(n) as count"
        ).fetch().all();

        // 获取关系统计
        var relationStats = neo4jClient.query(
            "MATCH ()-[r]->() RETURN type(r) as type, count(r) as count"
        ).fetch().all();

        return Map.of(
            "nodes", nodeStats,
            "relationships", relationStats
        );
    }
}