package com.hhusen.athena.knowledgeservice.service;

import com.hhusen.athena.knowledgeservice.model.Policy;
import com.hhusen.athena.knowledgeservice.repository.PolicyRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PolicyService {
    
    private final PolicyRepository policyRepository;
    
    @Autowired
    public PolicyService(PolicyRepository policyRepository) {
        this.policyRepository = policyRepository;
    }
    
    public List<Policy> getPoliciesByCity(String cityName) {
        return policyRepository.findPoliciesByCity(cityName);
    }
} 