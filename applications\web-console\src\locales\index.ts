import { createI18n } from 'vue-i18n'
import zhCN from './zh-CN'
import enUS from './en-US'

// 支持的语言列表
export const supportedLocales = [
  {
    code: 'zh-CN',
    name: '简体中文',
    flag: '🇨🇳'
  },
  {
    code: 'en-US',
    name: 'English',
    flag: '🇺🇸'
  }
]

// 获取默认语言
const getDefaultLocale = () => {
  // 1. 从localStorage获取用户设置
  const savedLocale = localStorage.getItem('user-language')
  if (savedLocale && supportedLocales.some(locale => locale.code === savedLocale)) {
    return savedLocale
  }

  // 2. 从浏览器语言获取
  const browserLocale = navigator.language
  const matchedLocale = supportedLocales.find(locale => 
    locale.code === browserLocale || locale.code.startsWith(browserLocale.split('-')[0])
  )
  
  if (matchedLocale) {
    return matchedLocale.code
  }

  // 3. 默认返回中文
  return 'zh-CN'
}

// 创建i18n实例
export const i18n = createI18n({
  legacy: false, // 使用Composition API模式
  locale: getDefaultLocale(),
  fallbackLocale: 'zh-CN',
  messages: {
    'zh-CN': zhCN,
    'en-US': enUS
  },
  globalInjection: true, // 全局注入$t函数
  silentTranslationWarn: true, // 静默翻译警告
  silentFallbackWarn: true // 静默回退警告
})

// 设置语言
export const setLocale = (locale: string) => {
  if (supportedLocales.some(l => l.code === locale)) {
    i18n.global.locale.value = locale as any
    localStorage.setItem('user-language', locale)
    
    // 设置HTML lang属性
    document.documentElement.lang = locale
    
    // 设置Element Plus语言
    setElementLocale(locale)
  }
}

// 获取当前语言
export const getCurrentLocale = () => {
  return i18n.global.locale.value
}

// 获取语言显示名称
export const getLocaleName = (code: string) => {
  const locale = supportedLocales.find(l => l.code === code)
  return locale ? locale.name : code
}

// 设置Element Plus语言
const setElementLocale = async (locale: string) => {
  try {
    let elementLocale
    
    switch (locale) {
      case 'zh-CN':
        elementLocale = await import('element-plus/dist/locale/zh-cn.mjs')
        break
      case 'en-US':
        elementLocale = await import('element-plus/dist/locale/en.mjs')
        break
      default:
        elementLocale = await import('element-plus/dist/locale/zh-cn.mjs')
    }
    
    // 这里需要在main.ts中配置Element Plus的ConfigProvider
    // 或者使用全局配置
    if (window.ElConfigProvider) {
      window.ElConfigProvider.locale = elementLocale.default
    }
  } catch (error) {
    console.warn('Failed to load Element Plus locale:', error)
  }
}

// 翻译函数的类型定义
export type TranslateFunction = (key: string, params?: Record<string, any>) => string

// 导出翻译函数
export const t = i18n.global.t as TranslateFunction

// 检查是否为RTL语言
export const isRTL = (locale: string) => {
  const rtlLocales = ['ar', 'he', 'fa', 'ur']
  return rtlLocales.some(rtl => locale.startsWith(rtl))
}

// 格式化数字（根据语言环境）
export const formatNumber = (num: number, locale?: string) => {
  const currentLocale = locale || getCurrentLocale()
  return new Intl.NumberFormat(currentLocale).format(num)
}

// 格式化日期（根据语言环境）
export const formatDate = (date: Date | string, options?: Intl.DateTimeFormatOptions, locale?: string) => {
  const currentLocale = locale || getCurrentLocale()
  const d = typeof date === 'string' ? new Date(date) : date
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }
  
  return new Intl.DateTimeFormat(currentLocale, { ...defaultOptions, ...options }).format(d)
}

// 格式化时间（根据语言环境）
export const formatTime = (date: Date | string, locale?: string) => {
  const currentLocale = locale || getCurrentLocale()
  const d = typeof date === 'string' ? new Date(date) : date
  
  return new Intl.DateTimeFormat(currentLocale, {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(d)
}

// 格式化相对时间
export const formatRelativeTime = (date: Date | string, locale?: string) => {
  const currentLocale = locale || getCurrentLocale()
  const d = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diff = now.getTime() - d.getTime()
  
  const rtf = new Intl.RelativeTimeFormat(currentLocale, { numeric: 'auto' })
  
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) {
    return rtf.format(-days, 'day')
  } else if (hours > 0) {
    return rtf.format(-hours, 'hour')
  } else if (minutes > 0) {
    return rtf.format(-minutes, 'minute')
  } else {
    return rtf.format(-seconds, 'second')
  }
}

// 初始化国际化
export const initI18n = () => {
  // 设置初始语言
  const locale = getCurrentLocale()
  setLocale(locale)
  
  // 监听语言变化
  i18n.global.locale.value = locale as any
  
  console.log(`I18n initialized with locale: ${locale}`)
}

export default i18n
