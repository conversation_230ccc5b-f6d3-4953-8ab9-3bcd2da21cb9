from fastapi import Fast<PERSON><PERSON>, HTTPException, Query, Depends, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from kubernetes import client, config
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging
import os
import json
import asyncio
from pydantic import BaseModel

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("admin-service")

app = FastAPI(
    title="Kubernetes Admin Service",
    description="A secure proxy for Kubernetes API operations",
    version="1.0.0",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化Kubernetes客户端
@app.on_event("startup")
async def startup_event():
    try:
        # 尝试加载集群内配置
        config.load_incluster_config()
        logger.info("Successfully loaded in-cluster configuration")
    except config.config_exception.ConfigException:
        # 如果不在集群内，尝试加载本地配置（用于开发）
        try:
            config.load_kube_config()
            logger.info("Successfully loaded local kube configuration")
        except Exception as e:
            logger.error(f"Failed to load Kubernetes configuration: {e}")
            raise

# 格式化Pod信息的函数
def format_pod_info(pod) -> Dict[str, Any]:
    # 计算Pod的年龄
    creation_time = pod.metadata.creation_timestamp
    if creation_time:
        age = datetime.now(creation_time.tzinfo) - creation_time
        age_str = f"{age.days}d {age.seconds//3600}h {(age.seconds//60)%60}m"
    else:
        age_str = "Unknown"
    
    # 获取容器状态
    containers = []
    restart_count = 0
    
    if pod.status.container_statuses:
        for container in pod.status.container_statuses:
            restart_count += container.restart_count
            container_status = {
                "name": container.name,
                "ready": container.ready,
                "restart_count": container.restart_count,
                "image": container.image,
            }
            
            # 添加当前状态
            if container.state.running:
                container_status["state"] = "Running"
                container_status["started_at"] = container.state.running.started_at
            elif container.state.waiting:
                container_status["state"] = "Waiting"
                container_status["reason"] = container.state.waiting.reason
                container_status["message"] = container.state.waiting.message
            elif container.state.terminated:
                container_status["state"] = "Terminated"
                container_status["reason"] = container.state.terminated.reason
                container_status["exit_code"] = container.state.terminated.exit_code
            
            containers.append(container_status)
    
    # 构建返回的Pod信息
    return {
        "name": pod.metadata.name,
        "namespace": pod.metadata.namespace,
        "status": pod.status.phase,
        "reason": pod.status.reason if pod.status.reason else None,
        "pod_ip": pod.status.pod_ip,
        "host_ip": pod.status.host_ip,
        "node_name": pod.spec.node_name,
        "restart_count": restart_count,
        "age": age_str,
        "creation_timestamp": pod.metadata.creation_timestamp.isoformat() if pod.metadata.creation_timestamp else None,
        "containers": containers,
        "labels": pod.metadata.labels,
        "annotations": pod.metadata.annotations,
    }

# 获取所有Pod列表
@app.get("/api/admin/pods", response_model=List[Dict[str, Any]])
async def get_pods(namespace: Optional[str] = None):
    try:
        v1 = client.CoreV1Api()
        
        if namespace:
            pods = v1.list_namespaced_pod(namespace=namespace)
        else:
            pods = v1.list_pod_for_all_namespaces()
        
        return [format_pod_info(pod) for pod in pods.items]
    except Exception as e:
        logger.error(f"Error fetching pods: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch pods: {str(e)}")

# 获取Pod日志（流式）
@app.get("/api/admin/pods/{namespace}/{pod_name}/logs")
async def get_pod_logs(
    namespace: str, 
    pod_name: str, 
    container: Optional[str] = None,
    tail_lines: Optional[int] = Query(None, description="Number of lines from the end of the logs to show"),
    follow: bool = Query(False, description="Follow the log stream")
):
    try:
        v1 = client.CoreV1Api()
        
        # 检查Pod是否存在
        try:
            pod = v1.read_namespaced_pod(name=pod_name, namespace=namespace)
        except client.exceptions.ApiException as e:
            if e.status == 404:
                raise HTTPException(status_code=404, detail=f"Pod {pod_name} not found in namespace {namespace}")
            raise
        
        # 如果没有指定容器且Pod有多个容器，使用第一个容器
        if not container and pod.spec.containers and len(pod.spec.containers) > 0:
            container = pod.spec.containers[0].name
        
        # 创建一个生成器函数来流式传输日志
        def log_generator():
            try:
                logs = v1.read_namespaced_pod_log(
                    name=pod_name,
                    namespace=namespace,
                    container=container,
                    follow=follow,
                    tail_lines=tail_lines,
                    _preload_content=False
                )
                
                # 流式传输日志
                for chunk in logs:
                    yield chunk
            except Exception as e:
                logger.error(f"Error streaming logs: {e}")
                yield f"Error streaming logs: {str(e)}".encode("utf-8")
        
        return StreamingResponse(
            log_generator(),
            media_type="text/plain"
        )
    except Exception as e:
        logger.error(f"Error fetching pod logs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch pod logs: {str(e)}")

# 重启Deployment
@app.post("/api/admin/deployments/{namespace}/{deployment_name}/restart")
async def restart_deployment(namespace: str, deployment_name: str):
    try:
        apps_v1 = client.AppsV1Api()
        
        # 检查Deployment是否存在
        try:
            deployment = apps_v1.read_namespaced_deployment(name=deployment_name, namespace=namespace)
        except client.exceptions.ApiException as e:
            if e.status == 404:
                raise HTTPException(status_code=404, detail=f"Deployment {deployment_name} not found in namespace {namespace}")
            raise
        
        # 准备patch数据，更新restartedAt注解来触发滚动重启
        now = datetime.now().isoformat()
        patch = {
            "spec": {
                "template": {
                    "metadata": {
                        "annotations": {
                            "kubectl.kubernetes.io/restartedAt": now
                        }
                    }
                }
            }
        }
        
        # 应用patch
        result = apps_v1.patch_namespaced_deployment(
            name=deployment_name,
            namespace=namespace,
            body=patch
        )
        
        return {
            "success": True,
            "message": f"Deployment {deployment_name} in namespace {namespace} restarted at {now}",
            "deployment": deployment_name,
            "namespace": namespace
        }
    except Exception as e:
        logger.error(f"Error restarting deployment: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to restart deployment: {str(e)}")

# 扩缩容Deployment的请求模型
class ScaleRequest(BaseModel):
    replicas: int

# 扩缩容Deployment
@app.post("/api/admin/deployments/{namespace}/{deployment_name}/scale")
async def scale_deployment(
    namespace: str, 
    deployment_name: str, 
    scale_request: ScaleRequest
):
    try:
        apps_v1 = client.AppsV1Api()
        
        # 检查Deployment是否存在
        try:
            deployment = apps_v1.read_namespaced_deployment(name=deployment_name, namespace=namespace)
        except client.exceptions.ApiException as e:
            if e.status == 404:
                raise HTTPException(status_code=404, detail=f"Deployment {deployment_name} not found in namespace {namespace}")
            raise
        
        # 验证replicas值
        if scale_request.replicas < 0:
            raise HTTPException(status_code=400, detail="Replicas count cannot be negative")
        
        # 准备patch数据
        patch = {
            "spec": {
                "replicas": scale_request.replicas
            }
        }
        
        # 应用patch
        result = apps_v1.patch_namespaced_deployment(
            name=deployment_name,
            namespace=namespace,
            body=patch
        )
        
        return {
            "success": True,
            "message": f"Deployment {deployment_name} in namespace {namespace} scaled to {scale_request.replicas} replicas",
            "deployment": deployment_name,
            "namespace": namespace,
            "replicas": scale_request.replicas
        }
    except Exception as e:
        logger.error(f"Error scaling deployment: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to scale deployment: {str(e)}")

# 健康检查端点
@app.get("/health")
async def health_check():
    return {"status": "healthy"}

# 主入口
if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", 8000))
    uvicorn.run("main:app", host="0.0.0.0", port=port, reload=True)
