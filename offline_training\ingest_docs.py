import os
import chromadb
import torch
import requests
import json
import uuid
from langchain_community.document_loaders import DirectoryLoader
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
import time # Added for retry logic

# ======================================================================================
# --- Constants ---
# ======================================================================================

CHROMA_HOST = "localhost"
CHROMA_PORT = 8000
CHROMA_BASE_URL = f"http://{CHROMA_HOST}:{CHROMA_PORT}"
KNOWLEDGE_BASE_DIR = os.path.join(os.path.dirname(__file__), "knowledge_docs")
EMBEDDING_MODEL_NAME = os.path.join(os.path.dirname(__file__), "models", "all-MiniLM-L6-v2")
COLLECTION_NAME = "athena_knowledge_base"


def main():
    """
    Main function to load, split, embed, and ingest documents into ChromaDB.
    """
    print("Starting document ingestion process...")

    # 1. Load documents
    print(f"Loading documents from: {KNOWLEDGE_BASE_DIR}")
    loader = DirectoryLoader(KNOWLEDGE_BASE_DIR, glob="**/*.txt", show_progress=True)
    documents = loader.load()
    if not documents:
        print("No documents found. Please check the 'knowledge_docs' directory.")
        return

    # 2. Split documents into chunks
    print("Splitting documents into chunks...")
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=50)
    splits = text_splitter.split_documents(documents)

    # 3. Initialize embedding model
    print(f"Initializing embedding model from local path: {EMBEDDING_MODEL_NAME}")
    if not os.path.isdir(EMBEDDING_MODEL_NAME):
        print(f"Error: Local model directory not found at '{EMBEDDING_MODEL_NAME}'")
        print("Please manually download the model files from 'https://huggingface.co/sentence-transformers/all-MiniLM-L6-v2/tree/main'")
        print(f"and place them in the '{EMBEDDING_MODEL_NAME}' directory.")
        return

    device = "cuda" if torch.cuda.is_available() else "cpu"
    model_kwargs = {'device': device}
    if device == "cpu":
        print("Warning: No GPU found. Using CPU for embeddings, which can be slow.")
    embedding_model = HuggingFaceEmbeddings(
        model_name=EMBEDDING_MODEL_NAME,
        model_kwargs=model_kwargs
    )

    # 4. Connect to ChromaDB (using direct REST API instead of client library)
    print(f"Connecting to ChromaDB at {CHROMA_HOST}:{CHROMA_PORT}...")
    try:
        # 使用最简单的健康检查端点
        health_response = requests.get(f"{CHROMA_BASE_URL}/api/v1/heartbeat")
        if health_response.status_code != 200:
            raise Exception(f"Failed to connect to ChromaDB: Status code {health_response.status_code}")
        
        print(f"Successfully connected to ChromaDB.")
        
    except Exception as e:
        print(f"Error connecting to ChromaDB: {e}")
        print("Please ensure ChromaDB is running and accessible, and that port-forwarding is set up correctly if running in Kubernetes.")
        print("  (e.g., `kubectl port-forward service/chromadb-svc 8000:8000`)")
        return

    # 5. First, try to get existing collections and find our collection ID
    print(f"Checking if collection '{COLLECTION_NAME}' exists...")
    collection_id = None
    
    try:
        # 首先尝试获取所有集合
        collections_response = requests.get(f"{CHROMA_BASE_URL}/api/v1/collections")
        
        if collections_response.status_code == 200:
            # 检查响应格式
            response_data = collections_response.json()
            
            # 处理不同的响应格式
            collections = []
            if isinstance(response_data, dict) and "collections" in response_data:
                collections = response_data["collections"]
            elif isinstance(response_data, list):
                collections = response_data
                
            # 搜索集合
            for collection in collections:
                if collection.get("name") == COLLECTION_NAME:
                    collection_id = collection.get("id")
                    print(f"Found existing collection '{COLLECTION_NAME}' with ID: {collection_id}")
                    break
    except Exception as e:
        print(f"Error listing collections: {e}")
        print("Will try to create the collection instead.")
    
    # 如果没有找到集合，尝试创建一个新的
    if not collection_id:
        print(f"Creating new collection '{COLLECTION_NAME}'...")
        try:
            create_payload = {
                "name": COLLECTION_NAME,
                "metadata": {"description": "Athena knowledge base for RAG"}
            }
            
            create_response = requests.post(
                f"{CHROMA_BASE_URL}/api/v1/collections", 
                json=create_payload
            )
            
            if create_response.status_code in [200, 201]:
                response_data = create_response.json()
                collection_id = response_data.get("id")
                print(f"Successfully created collection with ID: {collection_id}")
            else:
                print(f"Warning: Failed to create collection. Status: {create_response.status_code}")
                print(f"Response: {create_response.text}")
                
                # 即使创建失败，也再次尝试获取集合列表，看看集合是否已存在
                print("Trying to retrieve collection ID again...")
                retry_response = requests.get(f"{CHROMA_BASE_URL}/api/v1/collections")
                
                if retry_response.status_code == 200:
                    retry_data = retry_response.json()
                    
                    collections = []
                    if isinstance(retry_data, dict) and "collections" in retry_data:
                        collections = retry_data["collections"]
                    elif isinstance(retry_data, list):
                        collections = retry_data
                    
                    for collection in collections:
                        if collection.get("name") == COLLECTION_NAME:
                            collection_id = collection.get("id")
                            print(f"Retrieved existing collection ID: {collection_id}")
                            break
        
        except Exception as e:
            print(f"Error creating collection: {e}")
    
    if not collection_id:
        print("Error: Could not obtain collection ID. Cannot continue.")
        return

    # 6. Embed and store documents
    print(f"Preparing {len(splits)} document chunks for ingestion...")
    docs_to_add = [doc.page_content for doc in splits]
    metadatas_to_add = [doc.metadata for doc in splits]
    ids_to_add = [str(uuid.uuid4()) for _ in splits]

    print("Generating embeddings...")
    embeddings_to_add = embedding_model.embed_documents(docs_to_add)

    print("Adding documents to the collection...")
    success_count = 0
    
    try:
        # 每批处理10个文档，降低单次请求负担
        batch_size = 10
        for i in range(0, len(docs_to_add), batch_size):
            end_idx = min(i + batch_size, len(docs_to_add))
            current_batch_size = end_idx - i
            
            # 准备当前批次的数据
            batch_payload = {
                "ids": ids_to_add[i:end_idx],
                "embeddings": embeddings_to_add[i:end_idx],
                "metadatas": metadatas_to_add[i:end_idx],
                "documents": docs_to_add[i:end_idx]
            }
            
            # 最多尝试3次
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    # 使用集合ID而不是名称
                    add_response = requests.post(
                        f"{CHROMA_BASE_URL}/api/v1/collections/{collection_id}/add",
                        json=batch_payload
                    )
                    
                    if add_response.status_code in [200, 201]:
                        success_count += current_batch_size
                        print(f"Added batch {i//batch_size + 1}/{(len(docs_to_add)-1)//batch_size + 1} to collection")
                        break
                    else:
                        print(f"Warning: Failed to add batch (attempt {attempt+1}/{max_retries}). Status: {add_response.status_code}")
                        print(f"Response: {add_response.text[:200]}...")  # 只打印部分响应以避免日志过长
                        if attempt == max_retries - 1:
                            print(f"Failed to add batch after {max_retries} attempts")
                        else:
                            print(f"Retrying in 1 second...")
                            time.sleep(1)  # 等待1秒再重试
                            
                except Exception as e:
                    print(f"Error adding batch (attempt {attempt+1}/{max_retries}): {e}")
                    if attempt == max_retries - 1:
                        print(f"Failed to add batch after {max_retries} attempts")
                    else:
                        print(f"Retrying in 1 second...")
                        time.sleep(1)  # 等待1秒再重试
        
        # 打印最终结果
        print(f"\n--- Ingestion Complete ---")
        print(f"Successfully added {success_count} out of {len(docs_to_add)} documents to the '{COLLECTION_NAME}' collection.")
            
    except Exception as e:
        print(f"Error during document ingestion: {e}")
        if success_count > 0:
            print(f"Partial success: Added {success_count} out of {len(docs_to_add)} documents.")
        return


def setup_knowledge_base():
    """
    Creates the knowledge base directory and sample files if they don't exist.
    """
    if not os.path.exists(KNOWLEDGE_BASE_DIR):
        print(f"Creating directory: {KNOWLEDGE_BASE_DIR}")
        os.makedirs(KNOWLEDGE_BASE_DIR)

    doc1_path = os.path.join(KNOWLEDGE_BASE_DIR, "pinn_intro.txt")
    if not os.path.exists(doc1_path):
        print(f"Creating sample file: {doc1_path}")
        with open(doc1_path, "w", encoding="utf-8") as f:
            f.write("Physics-informed neural networks (PINNs) are a type of neural network that can be trained to solve supervised learning tasks while respecting any given law of physics described by general nonlinear partial differential equations.\n")
            f.write("This makes them particularly useful for problems where there is a shortage of labeled data, but the underlying physical principles are well understood.")

    doc2_path = os.path.join(KNOWLEDGE_BASE_DIR, "rag_arch.txt")
    if not os.path.exists(doc2_path):
        print(f"Creating sample file: {doc2_path}")
        with open(doc2_path, "w", encoding="utf-8") as f:
            f.write("Retrieval-Augmented Generation (RAG) is an architecture that combines the strengths of large language models (LLMs) with external knowledge retrieval systems.\n")
            f.write("In a RAG system, when a query is received, it first retrieves relevant documents from a knowledge base (like one stored in ChromaDB). These documents are then provided as context to the LLM, which generates a more accurate and informed response.")


if __name__ == "__main__":
    setup_knowledge_base()
    main() 