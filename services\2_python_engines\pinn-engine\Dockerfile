FROM python:3.8-slim

WORKDIR /app

# 配置pip使用清华源
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/

# 复制依赖文件
COPY requirements.txt .

# 分步安装依赖，先安装基础包
RUN pip install --no-cache-dir pika==1.2.0 requests==2.31.0 minio==7.2.2 --trusted-host mirrors.aliyun.com && \
    pip install --no-cache-dir numpy>=1.19.2 --trusted-host mirrors.aliyun.com && \
    pip install --no-cache-dir mlflow==2.11.0 --trusted-host mirrors.aliyun.com && \
    pip install --no-cache-dir torch>=1.8.0 --extra-index-url https://download.pytorch.org/whl/cpu --trusted-host download.pytorch.org --trusted-host mirrors.aliyun.com && \
    pip cache purge

# 复制应用代码
COPY consumer.py .
COPY model_loader.py .

# 创建模型目录用于本地备份模型
RUN mkdir -p /app/models

# 复制离线训练的模型文件到容器中
COPY hydro_pinn_v1.pth /app/hydro_pinn_v1.pth

# 容器启动时执行的命令
CMD ["python", "-u", "consumer.py"]